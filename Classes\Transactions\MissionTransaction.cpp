#include "MissionTransaction.h"
#include "../Services/HTTPService.h"

USING_NS_CC;
USING_STD;

static const char* TRANS_GAME_TASK_QUERY = "Game_Task_Query.ashx";
static const char* TRANS_GAME_TASK_SUBMIT = "Game_Task_Submit.ashx";

FUNC_GET_INSTANCE(MissionTransaction);

MissionTransaction::MissionTransaction()
{
	Director::getInstance()->getEventDispatcher()->addCustomEventListener(TRANS_GAME_TASK_QUERY, [=](EventCustom* event) {
		auto data = static_cast<HTTPData *>(event->getUserData());
		auto userData = GameData::getInstance()->getUserData();
		auto& tasks = userData->getTasks();
		auto list = data->readDatas("List");
		for (auto element : list)
		{
			auto taskData = TaskData::create();
			taskData->retain();
			TaskData::Info& taskInfo = taskData->getInfo();
			taskInfo.mid = element->readInt("TaskID");
			taskInfo.name = element->readString("TaskName");
			taskInfo.state = element->readInt("CompleteState");
			taskInfo.type = element->readInt("TaskType");
			taskInfo.receivenpc = element->readInt("ReceiveNPC");
			taskInfo.paynpc = element->readInt("PayNPC");
			taskInfo.city = element->readInt("CityID");
			taskInfo.deliveryCityID = element->readInt("DeliveryCityID");
			taskInfo.target = element->readInt("Target");
			taskInfo.description = element->readString("Description");
			taskInfo.reward = element->readString("Reward");
			taskInfo.receiveTaskDialog = element->readString("ReceiveTaskDialog");
			taskInfo.payTaskDialog = element->readString("PayTaskDialog");
			taskInfo.targetCityID = element->readInt("TargetCityID");
			TaskData::Factor& factor = taskData->getFactor();
			factor.type = element->readInt("CompleteFactor");
			factor.id = element->readInt("CompleteFactorID");
			factor.value = element->readInt("CompleteFactorValue");

			tasks.insert(STRING(taskInfo.mid),taskData);
		}
		Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_GAME_MISSION_QUERY, NULL);
	});
}

MissionTransaction::~MissionTransaction()
{
	Director::getInstance()->getEventDispatcher()->removeCustomEventListeners(TRANS_GAME_TASK_QUERY);
}

void MissionTransaction::query(string UID, int CityID)
{
	auto data = HTTPData::create();
	data->write("UID", UID.c_str());
	data->write("CityID", CityID);

	auto url = WEB_HOST + TRANS_GAME_TASK_QUERY;
	HTTPService::getInstance()->request(url.c_str(), data, TRANS_GAME_TASK_QUERY);
}

void MissionTransaction::submit(std::string UID,int TaskID,int CityID,int SumbitType,std::string PropsGuid)
{
	auto data = HTTPData::create();
	data->write("UID", UID.c_str());
	data->write("TaskID", TaskID);
	data->write("CityID", CityID);
	data->write("SubmitType", SumbitType);
	data->write("PropsGuid", PropsGuid.c_str());

	auto url = WEB_HOST + TRANS_GAME_TASK_SUBMIT;
	HTTPService::getInstance()->request(url.c_str(), data, TRANS_GAME_TASK_SUBMIT);
}

