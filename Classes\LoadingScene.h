#ifndef __LOADING_SCENE_H__
#define __LOADING_SCENE_H__

#include "cocos2d.h"
#include "Services/HTTPService.h"
#include "Scenes/BaseScene.h"
#include <iostream>

class LoadingScene : public BaseScene
{
public:
	enum LoadingProcessType : int
	{
		CONFIGS = -1,
		SHIPS = 0,
		SHIPS_LOADING = 1,
		PLAYERS = 35,
		PLAYERS_LOADING = 36,
		ANIMATIONS = 70,
		ANIMATIONS_LOADING = 71,
		FINISH = 100,
	};

	enum class SceneType
	{
		MAP,
		CITY,
		START,
	};

	static cocos2d::Scene* createScene();
	static cocos2d::Scene* createScene(SceneType sceneType);
	static cocos2d::Scene* createScene(SceneType sceneType, std::vector<std::string> loadings);

	LoadingScene();
	virtual ~LoadingScene();

	virtual bool init() override;
	virtual void update(float dt) override;
	virtual void onEnter() override;

	void running(float percent);
	void updateTips(float dt);
	void loadingCallBack(cocos2d::Texture2D *texture);
	void start(float dt);

	CREATE_FUNC(LoadingScene);

private:
	void showProcess(int current);

private:
	LoadingProcessType _processType;
	cocos2d::Node * node;
};

#endif // __LOADING_SCENE_H__
