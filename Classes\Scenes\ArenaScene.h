#ifndef __ARENA_SCENE_H__
#define __ARENA_SCENE_H__

#include "cocos2d.h"
#include "ui/CocosGUI.h"
#include "cocostudio/CocoStudio.h"
#include "BaseScene.h"

class ArenaScene : public BaseScene
{
public:
	static cocos2d::Scene* createScene();
	virtual bool init();  
	void menuCloseCallback(cocos2d::Ref* pSender);
	void touchButton(cocos2d::Ref* object, cocos2d::ui::TouchEventType type);

	void loadBattlefield();
	void loadPanel();

	ArenaScene();
	virtual ~ArenaScene();
	virtual void onEnter() override;

	void update(float dt) override;
	CREATE_FUNC(ArenaScene);
	
private:
	cocos2d::Node* _node;
	int _cd;
};

#endif // __ARENA_SCENE_H__