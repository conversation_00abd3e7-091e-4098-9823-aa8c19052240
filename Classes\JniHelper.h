#ifndef __JNI_HELPER_H__
#define __JNI_HELPER_H__

#include "cocos2d.h"
using namespace cocos2d;

void cacheUserID(const char* info)
{
	CCLOG("cm user id: ", info);
}

static const std::string EVENT_PAY_MONEY_CALLBACK[] = { "EVENT_PAY_MONEY_CANCEL", "EVENT_PAY_MONEY_SUCCESS", "EVENT_PAY_MONEY_FAIL" };

void payMoneyCallback(int state, std::string billing, std::string tradeId)
{
	std::map<std::string, std::string> data;
	data["Billing"] = billing;
	data["TradeID"] = tradeId;
	cocos2d::Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_PAY_MONEY_CALLBACK[state], &data);
	//cocos2d::NotificationCenter::getInstance()->postNotification(EVENT_PAY_MONEY_CALLBACK[state]);
}

#endif // __JNI_HELPER_H__
