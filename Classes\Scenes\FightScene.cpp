#include "FightScene.h"
#include "../SOSConfig.h"
#include "CityScene.h"
#include "SettleScene.h"
#include "../Services/SoundService.h"
#include "../Services/LocalizeService.h"
#include "../Views/FightFailView.h"

USING_NS_CC;
USING_NS_CC_UI;
USING_NS_CC_CCS;

enum TagFightScene
{
	UI_FIGHT_2 = 10003,
	UI_FIGHT_3 = 10004,
	UI_NODE_AOE = 10023,
	UI_ANIMATION_START = 10037,

	UI2_TEXT_ROUND = 1401,
	UI2_BUTTON_FAST = 1402,
	UI2_IMAGE_SKILL = 1407,
	UI2_TEXT_NAME = 1409,
	UI2_BUTTON_TEMP = 1500,
	UI2_TEXT_TEAM_SKILL = 1405,

	UI3_NODE_PLAYER = 1411,
	UI3_NODE_PLAYER_ANIMATION = 14111,
	UI3_IMAGE_BLOOD_BG = 1412,
	UI3_IMAGE_BLOOD = 1413,
	UI3_IMAGE_ENERGY = 1414,
	UI3_TEXT_NAME = 1417,
	UI3_NODE_ATTACK = 1418,
	UI3_NODE_EFFECT = 1419,
	UI3_IMAGE_BIAS = 95001,
};

enum TagFightLayer
{
	TEAM_SKILL = 4000,
	PLAYER = 5000,
	AOE = 6000,
	SHOOTER = 7000,
	EFFECT = 8000,
	LABEL = 9000,
	SETTLE_SCENE = 10000,
};

std::string r_fightBgName;
std::vector<std::string> r_armatureDatas;
std::vector<Node *> r_armtures;

Scene* FightScene::createScene()
{
	auto fight = GameData::getInstance()->getFightData();
	return FightScene::createScene(fight);
}

Scene* FightScene::createScene(FightData* fightData)
{
	auto layer = FightScene::create(fightData);
	auto scene = Scene::create();
	scene->addChild(layer);
	return scene;
}

FightScene* FightScene::create(FightData* fightData)
{
	FightScene *pRet = new FightScene();
	if (pRet && pRet->init(fightData))
	{
		pRet->autorelease();
		return pRet;
	}
	else
	{
		delete pRet;
		pRet = NULL;
		return NULL;
	}
}

FightScene::FightScene()
: _fightData(nullptr)
, _scene(nullptr)
, _currentRound(0)
, _currentAction(-1)
, _currentZOrder(-1)
, _currentCount(0)
{

}

FightScene::~FightScene()
{
	SoundService::getInstance()->playBGM();

	this->getEventDispatcher()->removeCustomEventListeners(EVENT_LEVEL_CHANGED);

	Director::getInstance()->getTextureCache()->removeTextureForKey(r_fightBgName);

	for (auto file : r_armatureDatas)
	{
		ArmatureDataManager::getInstance()->removeArmatureFileInfo(file);
	}

	ArmatureDataManager::getInstance()->removeArmatureFileInfo("Animation_Fight.ExportJson");

	for (int i = 1; i <= 3; i++)
	{
		auto name = String::createWithFormat("UI_Fight/UI_Fight_%d.png", i)->getCString();
		Director::getInstance()->getTextureCache()->removeTextureForKey(name);

		auto frameName = String::createWithFormat("UI_Fight/UI_Fight_%d.plist", i)->getCString();
		SpriteFrameCache::getInstance()->removeSpriteFramesFromFile(frameName);
	}

	// 	SpriteFrameCache::getInstance()->removeUnusedSpriteFrames();
	// 	Director::getInstance()->getTextureCache()->removeUnusedTextures();

	ArmatureDataManager::getInstance()->removeArmatureFilesInfo();
	// 	SpriteFrameCache::getInstance()->removeSpriteFrames();
	// 	Director::getInstance()->getTextureCache()->removeAllTextures();

	r_armatureDatas.clear();
	r_armtures.clear();
	CC_SAFE_RELEASE_NULL(_fightData);
}

bool FightScene::init()
{
	if (!BaseScene::init())
	{
		return false;
	}

	_scene = CCS_CREATE_SCENE("Scene_Fight_1");
	_scene->setTag(TagBaseScene::LAYER);
	this->addChild(_scene);

	CCS_GET_COMPONENT_FROM_SCENE(_scene, Layer, layer, UI_FIGHT_2);
	CCS_GET_CHILD(layer, CheckBox, fast, UI2_BUTTON_FAST);
	CCS_GET_CHILD(layer, CheckBox, temp, UI2_BUTTON_TEMP);

	//temp->setVisible(true);
// 	fast->setOpacity(255);
// 	fast->setVisible(true);
// 	fast->setTouchEnabled(true);
	//temp->addEventListenerCheckBox(this, checkboxselectedeventselector(FightScene::onTouchCheck));
	//fast->addEventListenerCheckBox(this, checkboxselectedeventselector(FightScene::onTouchCheck));

	CCS_GET_ARMATURE_FROM_SCENE(_scene, Armature, start, UI_ANIMATION_START);
	start->getAnimation()->setMovementEventCallFunc(CC_CALLBACK_0(FightScene::onStartEvent, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3));

	this->schedule(schedule_selector(FightScene::cleanup), 0.5f);
	
	return true;
}

bool FightScene::init(FightData* fightData)
{
	_fightData = fightData;
	CC_SAFE_RETAIN(_fightData);

	if (!FightScene::init())
	{
		return false;
	}
	
	Texture2D::setDefaultAlphaPixelFormat(Texture2D::PixelFormat::RGB888);
	r_fightBgName = String::createWithFormat("scene_fight_%d.jpg", _fightData->getInfo().scene)->getCString();
	auto bg = Sprite::create(r_fightBgName);
	bg->setAnchorPoint(Point::ZERO);
	this->addChild(bg, -1);
	Texture2D::setDefaultAlphaPixelFormat(Texture2D::PixelFormat::DEFAULT);

	// 	auto dialogUI = CCS_CREATE_LAYER("UI_Mission_1.ExportJson");
	// 	this->addChild(dialogUI, 99999999);

	initTeam(TeamData::LEFT);
	initTeam(TeamData::RIGHT);

	return true;
}

void FightScene::onEnter()
{
	//next();
	SoundService::getInstance()->playBGM(SOUND_FIGHT);

	this->getEventDispatcher()->addCustomEventListener(EVENT_LEVEL_CHANGED, [=](EventCustom* event){
		auto pid = static_cast<std::string *>(event->getUserData());
		if (*pid == GameData::getInstance()->getUserData()->getInfo().role)
		{
			_fightData->getInfo().upgrade = true;
		}
	});

	CCLOG("%s", Director::getInstance()->getTextureCache()->getCachedTextureInfo().c_str());

	Layer::onEnter();
}

void FightScene::initTeam(TeamData::FaceType face)
{
	auto team = _fightData->getTeam(face);
	auto &positions = team->getPositions();

	auto pos = face * 9 + UI_FIGHT_3;

	for (int i = 0; i < positions.size(); i++)
	{
		CCLOG("pos: %d, pid: %s", i, positions[i].c_str());

		auto pid = positions[i];
		if (pid != TeamPosition::EMPTY)
		{
			CCS_GET_CHILD(_scene, Node, playerNode, pos + i);
			CCS_GET_COMPONENT(playerNode, Layer, playerLayer);
			CCS_GET_CHILD(playerLayer, Layout, container, UI3_NODE_PLAYER);
			CCS_GET_CHILD(playerLayer, ImageView, bloodBG, UI3_IMAGE_BLOOD_BG);
			CCS_GET_CHILD(playerLayer, LoadingBar, blood, UI3_IMAGE_BLOOD);
			CCS_GET_CHILD(playerLayer, LoadingBar, energy, UI3_IMAGE_ENERGY);
			CCS_GET_CHILD(playerLayer, Text, name, UI3_TEXT_NAME);
			CCS_GET_CHILD(playerLayer, ImageView, biasIcon, UI3_IMAGE_BIAS);

			playerNode->setTag((face * 2) - 1);

			auto player = GameData::getInstance()->getPlayer(pid);
			CCLOG("HP: %d", player->getInfo().life);

			auto file = GET_PLAYER_FILE(player->getInfo().type, player->getInfo().appraisal);
			r_armatureDatas.push_back(file);
			ArmatureDataManager::getInstance()->addArmatureFileInfo(file);
			auto armature = Armature::create(GET_PLAYER_NAME(player->getInfo().type, player->getInfo().appraisal));
			armature->setTag(UI3_NODE_PLAYER_ANIMATION);
			armature->setUserObject(player);
			armature->getAnimation()->play(PlayerAction::IDLE);
			armature->getAnimation()->setSpeedScale(1);
			armature->getAnimation()->setMovementEventCallFunc(CC_CALLBACK_0(FightScene::onAnimationEvent, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3));
			armature->getAnimation()->setFrameEventCallFunc(CC_CALLBACK_0(FightScene::onFrameEvent, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3, std::placeholders::_4));
			container->addChild(armature);

			name->setText(player->getInfo().name);
			name->setVisible(true);
			bloodBG->setVisible(true);
			blood->setVisible(true);
			energy->setVisible(true);
			biasIcon->loadTexture(GET_PLAYER_BIAS(player->getInfo().bias), TextureResType::PLIST);
			biasIcon->setVisible(true);

			CCLOG("pos: %f,%f", playerNode->getPosition().x, playerNode->getPosition().y);
			_playerNodes[pid] = playerNode;
		}
	}

	CCS_GET_COMPONENT_FROM_SCENE(_scene, Layer, layer, UI_FIGHT_2);
	CCS_GET_CHILD(layer, Text, nameText, UI2_TEXT_NAME + face);
	CCS_GET_CHILD(layer, ImageView, skillImage, UI2_IMAGE_SKILL + face);
	CCS_GET_CHILD(layer, Text, teamSkillText, UI2_TEXT_TEAM_SKILL + face);

	nameText->setText(team->getUser().name);
	if (team->getInfo().skill > 0)
	{
		skillImage->setVisible(true);
		skillImage->loadTexture(GET_TEAM_SKILL_BG(team->getInfo().skill), TextureResType::PLIST);

		//auto teamSkill = GameData::getInstance()->getSkill();
		teamSkillText->setText(LocalizeService::getInstance()->getString(STRING((100030 + team->getInfo().skill))));
		//teamSkillText->setText(teamSkill->getInfo().name);
	}
}

void FightScene::onTouchCheck(Ref *object, CheckBoxEventType eventType)
{
	auto check = static_cast<CheckBox*>(object);

	switch (eventType)
	{
	case CheckBoxEventType::CHECKBOX_STATE_EVENT_SELECTED:
		CCLOG("selected ...");
		if (check->getTag() == UI2_BUTTON_FAST)
		{
			//Director::getInstance()->setAnimationInterval(1.0 / 30);
			auto scheduler = Director::getInstance()->getScheduler();
			scheduler->setTimeScale(2.0);
		}
		else
		{
			Director::getInstance()->replaceScene(CityScene::createScene());
		}
		break;
	case CheckBoxEventType::CHECKBOX_STATE_EVENT_UNSELECTED:
		CCLOG("unselected ...");
		if (check->getTag() == UI2_BUTTON_FAST)
		{
			//Director::getInstance()->setAnimationInterval(1.0 / 60);
			auto scheduler = Director::getInstance()->getScheduler();
			scheduler->setTimeScale(1.0);
		}
		break;
	default:
		break;
	}
}

void FightScene::onStartEvent(Armature *armature, MovementEventType movementType, const std::string& movementID)
{
	if (movementType == COMPLETE)
	{
		next();
	}
}

void FightScene::onAnimationEvent(Armature *armature, MovementEventType movementType, const std::string& movementID)
{
	if (movementType == COMPLETE)
	{
		if (movementID == PlayerAction::ATTACK)
		{
			CCLOG("player attack loop complete ... ");

			CCS_GET_COMPONENT(_currentNode, Layer, playerLayer);
			CCS_GET_CHILD(playerLayer, Layout, playerContainer, UI3_NODE_PLAYER);
			CCS_GET_CHILD(playerLayer, LoadingBar, playerEnerge, UI3_IMAGE_ENERGY);
			CCS_GET_CHILD(playerContainer, Armature, playerAction, UI3_NODE_PLAYER_ANIMATION);

			auto round = _fightData->getRound(_currentRound);
			auto action = round.actions[_currentAction];
			auto data = GameData::getInstance()->getPlayer(action.pid);
			data->setEnergy(data->getInfo().energy + action.energe);

			CCLOG("current energe: %d", data->getInfo().energy);

			playerEnerge->setPercent(100.0f * data->getInfo().energy / data->getInfo().maxEnergy);
			playerAction->getAnimation()->play(PlayerAction::IDLE);

			if (_currentZOrder == -1)
			{
				_currentCount--;
				next();
				return;
			}

			_currentNode->setPosition(_currentPoint);
			_currentNode->setLocalZOrder(_currentZOrder);

			_currentCount--;
			next();
		}
		else if (movementID == PlayerAction::MAGIC)
		{
			CCLOG("player magic loop complete ... ");

			CCS_GET_COMPONENT(_currentNode, Layer, playerLayer);
			CCS_GET_CHILD(playerLayer, Layout, playerContainer, UI3_NODE_PLAYER);
			CCS_GET_CHILD(playerLayer, LoadingBar, playerEnerge, UI3_IMAGE_ENERGY);
			CCS_GET_CHILD(playerContainer, Armature, playerAction, UI3_NODE_PLAYER_ANIMATION);

			auto round = _fightData->getRound(_currentRound);
			auto action = round.actions[_currentAction];
			auto data = GameData::getInstance()->getPlayer(action.pid);
			data->setEnergy(data->getInfo().energy + action.energe);

			playerEnerge->setPercent(data->getInfo().energy);
			playerAction->getAnimation()->play(PlayerAction::IDLE);

			if (_currentZOrder == -1)
			{
				_currentCount--;
				next();
				return;
			}

			_currentNode->setPosition(_currentPoint);
			_currentNode->setLocalZOrder(_currentZOrder);

			_currentCount--;
			next();
		}
		else if (movementID == PlayerAction::HITTED)
		{
			armature->getAnimation()->play(PlayerAction::IDLE);

			auto target = static_cast<Value *>(armature->getUserData());
			if (target != nullptr)
			{
				CCLOG("-------------DEAD-------------");			
				armature->setUserData(nullptr); 
				//if (_isWreck)
				//{

				//	int width = Director::getInstance()->getWinSize().width;
				//	int height = Director::getInstance()->getWinSize().height;
				//	auto moveTo1 = MoveTo::create(0.5f, Point(-width, CCRANDOM_0_1()*height));

				//	auto rotate = Repeat::create(Sequence::create(RotateTo::create(0.25, -180), RotateTo::create(0.25, -180), NULL), 1);
				//	auto move = Sequence::create(moveTo1, FadeOut::create(GameData::getInstance()->actionDeadDelay),
				//		CallFunc::create(CC_CALLBACK_0(FightScene::onDamageCallback, this, _playerNodes[target->asString()], true)),
				//		NULL);


				//	armature->runAction(Spawn::create(move, NULL));
				//	armature->runAction(rotate);
				//	armature->setAnchorPoint(Point(0.5, 0.2));
				//}
				//else
				//{
					//_playerNodes[target->asString()]->setVisible(false);
					armature->setUserData(nullptr);
					armature->runAction(Sequence::create(
						FadeOut::create(GameData::getInstance()->actionDeadDelay),
						CallFunc::create(CC_CALLBACK_0(FightScene::onDamageCallback, this, _playerNodes[target->asString()], true)),
						NULL));
				//}

			
				delete target;
			}

		}
		else
		{
			CCLOG("current animation: %s, type: %d", movementID.c_str(), movementType);
		}
	}
}

void FightScene::onFrameEvent(Bone *bone, const std::string& evt, int originFrameIndex, int currentFrameIndex)
{
	CCLOG("(%d) emit a frame event (%s) at frame index (%d).", bone->getArmature()->getTag(), evt.c_str(), currentFrameIndex);
	
	auto round = _fightData->getRound(_currentRound);
	auto action = round.actions[_currentAction];

	if (evt == "hit")
	{
		auto player = GameData::getInstance()->getPlayer(action.pid);
		if (action.skill <= FightData::SkillType::LUCKY)
		{
			if (player->getSound().hitting != "0")
			{
				SoundService::getInstance()->playSFX("Sounds/" + player->getSound().hitting + ".mp3");
			}
		}
		else
		{
			if (player->getSound().magic != "0")
			{
				SoundService::getInstance()->playSFX("Sounds/" + player->getSound().magic + ".mp3");
			}
		}

		for (auto target : action.targets)
		{
			auto targetNode = _playerNodes[target.pid];
			CCLOG("target hitted id: %s", target.pid.c_str());
			runHitting(targetNode, target.defense, target.life, target.energe, target.dead);
		}
	}
	else if (evt == "shoot")
	{
		auto player = GameData::getInstance()->getPlayer(action.pid);
		auto skill = action.skill <= 5 ? player->getDefaultSkill() : GameData::getInstance()->getSkill(action.skill);

		if (skill->getShooter().mid != "0")
		{
			if (skill->getShooter().sound != "0")
			{
				SoundService::getInstance()->playSFX("Sounds/" + skill->getShooter().sound + ".mp3");
			}

			auto playerNode = _playerNodes[action.pid];

			CCS_GET_COMPONENT(playerNode, Layer, playerLayer);
			CCS_GET_CHILD(playerLayer, Layout, attackPoint, UI3_NODE_ATTACK);

			for (auto &target : action.targets)
			{
				auto targetNode = _playerNodes[target.pid];

				CCS_GET_COMPONENT(targetNode, Layer, targetLayer);
				CCS_GET_CHILD(targetLayer, Layout, effectPoint, UI3_NODE_EFFECT);

				CCLOG("skill mid:%d, shooter mid: %s", skill->getInfo().mid, skill->getShooter().mid.c_str());

				auto file = GET_SKILL_FILE("Shooter", skill->getShooter().mid.c_str());
				r_armatureDatas.push_back(file);
				ArmatureDataManager::getInstance()->addArmatureFileInfo(file);
				auto armature = Armature::create(GET_SKILL_NAME("Shooter", skill->getShooter().mid.c_str()));
				
				armature->setUserObject(targetNode);
				armature->setScaleX(targetNode->getTag());
				armature->setPosition(playerNode->getPosition() + attackPoint->getPosition());
				armature->getAnimation()->play(skill->getShooter().mid);
				armature->runAction(Sequence::create(
					MoveTo::create(0.5f, targetNode->getPosition() + effectPoint->getPosition()),
					CallFunc::create(CC_CALLBACK_0(FightScene::onShooterCallback, this, armature, true)),
					NULL));
				this->addChild(armature, TagFightLayer::SHOOTER);
				_currentCount++;	

				//if (action.skill <= 5)
				//{

				//}
				//else
				//{
				//	this->runAction(ScaleTo::create(0.1f, 2));
				//	this->runAction(Follow::create(armature));

				//}

			}
		}
		else if (skill->getTrigger().effect != "0")
		{
			if (skill->getTrigger().sound != "0")
			{
				SoundService::getInstance()->playSFX("Sounds/" + skill->getTrigger().sound + ".mp3");
			}

			switch (skill->getTrigger().extent)
			{
			case SkillData::ExtentType::SINGLE:
				for (auto target : action.targets)
				{
					auto targetNode = _playerNodes[target.pid];
					runEffect(skill, targetNode);
					_currentCount++;
				}
				break;
			case SkillData::ExtentType::PORTRAIT:
				runEffect(skill, _playerNodes[action.targets[0].pid]);
				_currentCount++;
				break;
			case SkillData::ExtentType::AOE:
				runAOE(skill);
				_currentCount++;
				break;
			default:
				CCLOG("skill mid:%d extent:%d", skill->getInfo().mid, skill->getTrigger().extent);
				break;
			}
		}
		else
		{
			CCLOG("no skill effect: %d", skill->getInfo().mid);
		}
	}
	else if (evt == "fx")
	{
		auto player = GameData::getInstance()->getPlayer(action.pid);
		auto skill = action.skill <= 5 ? player->getDefaultSkill() : GameData::getInstance()->getSkill(action.skill);

		switch (skill->getTrigger().extent)
		{
		case SkillData::ExtentType::SINGLE:
			for (auto &target : action.targets)
			{
				auto data = static_cast<PlayerData *>(bone->getArmature()->getUserObject());
				if (target.pid == data->getInfo().id)
				{
					auto targetNode = _playerNodes[target.pid];
					CCLOG("target hitted id: %s", target.pid.c_str());

					runHitting(targetNode, 0, target.life, target.energe, target.dead, false);
					break;
				}
			}
			break;
		case SkillData::ExtentType::AOE:
			for (auto &target : action.targets)
			{
				auto targetNode = _playerNodes[target.pid];
				CCLOG("target hitted id: %s", target.pid.c_str());

				runHitting(targetNode, 0, target.life, target.energe, target.dead, false);
			}
			break;
		default:
			break;
		}
	}

	

	auto playerNode = _playerNodes[action.pid];

	auto target = action.targets[0];
	auto targetNode = _playerNodes[target.pid];

	if (target.dead)
	{
		_scene->setScale(1);
		_scene->setPosition(Point(0,0));
	}
}

void FightScene::onEffectEvent(Armature *armature, MovementEventType movementType, const std::string& movementID)
{
	if (movementType == COMPLETE)
	{
		CCLOG("effect id: %s", movementID.c_str());
		r_armtures.push_back(armature);
		_currentCount--;
		next();
	}
}

void FightScene::cleanup(float dt)
{
	for (auto armature : r_armtures)
	{
		this->removeChild(armature);
	}

	r_armtures.clear();
}

void FightScene::onDamageCallback(Node* node, bool cleanup)
{
	node->removeFromParentAndCleanup(cleanup);
}

void FightScene::onShooterCallback(Node* node, bool cleanup)
{
	node->removeFromParentAndCleanup(cleanup);

	auto targetNode = static_cast<Node *>(node->getUserObject());

	auto round = _fightData->getRound(_currentRound);
	auto action = round.actions[_currentAction];

	auto player = GameData::getInstance()->getPlayer(action.pid);
	auto skill = action.skill <= 5 ? player->getDefaultSkill() : GameData::getInstance()->getSkill(action.skill);

	for (auto target : action.targets)
	{
		if (targetNode == _playerNodes[target.pid])
		{
			CCLOG("target hitted id: %s", target.pid.c_str());
			runHitting(targetNode, target.defense, target.life, target.energe, target.dead);
			break;
		}
	}

	_currentCount--;
	next();
}

void FightScene::start(float dt)
{
	auto round = _fightData->getRound(_currentRound);

	CCS_GET_COMPONENT_FROM_SCENE(_scene, Layer, layer, UI_FIGHT_2);
	CCS_GET_CHILD(layer, Text, roundLabel, UI2_TEXT_ROUND);

	roundLabel->setText(STRING(_currentRound + 1));
	//this->schedule(schedule_selector(FightScene::setPlayerFocus));
	runAct();
}

void FightScene::next()
{
	CCLOG("current count: %d", _currentCount);
	
	if (_currentCount > 0) return;

	if (_currentRound >= _fightData->getRounds().size())
	{
		CCLOG("round over ...");

		//Director::getInstance()->getScheduler()->setTimeScale(1);
		auto scheduler = Director::getInstance()->getScheduler();
		scheduler->setTimeScale(1);
		
		this->scheduleOnce(schedule_selector(FightScene::showResult), 1.0f);
		return;
	}

	auto round = _fightData->getRound(_currentRound);

	_currentAction++;

	if (_currentAction >= round.actions.size())
	{
		_currentRound++;
		_currentAction = -1;
		next();
		return;
	}

	this->scheduleOnce(schedule_selector(FightScene::start), GameData::getInstance()->actionDelay);
}

void FightScene::runAct()
{
	auto round = _fightData->getRound(_currentRound);
	auto action = round.actions[_currentAction];

	auto player = GameData::getInstance()->getPlayer(action.pid);
	auto playerNode = _playerNodes[action.pid];

	CCS_GET_COMPONENT(playerNode, Layer, playerLayer);
	CCS_GET_CHILD(playerLayer, Layout, container, UI3_NODE_PLAYER);
	CCS_GET_CHILD(container, Armature, playerAction, UI3_NODE_PLAYER_ANIMATION);

	CCLOG("current skill: %d", action.skill);

	//Director::getInstance()->getScheduler()->setTimeScale(1);
	//
	//if (action.skill <= 5){
	//	this->setPosition(0, 0);
	//	this->runAction(ScaleTo::create(0.1f, 1));
	//}
	//else
	//{
	//	Director::getInstance()->getScheduler()->setTimeScale(0.6);
	//	this->runAction(ScaleTo::create(0.1f, 2));
	//	this->runAction(Follow::create(playerNode));
	//}
	//_scene->removeChildByTag(654321);

	SkillData *skill = nullptr;
	if (action.skill <= 5)
	{
		skill = player->getDefaultSkill();
		playerAction->getAnimation()->play(PlayerAction::ATTACK);
		_currentCount++;

	}
	else
	{
		skill = GameData::getInstance()->getSkill(action.skill);
		playerAction->getAnimation()->play(PlayerAction::MAGIC);
		_currentCount++;

		
		auto target = action.targets[0];
		auto targetNode = _playerNodes[target.pid];

		CCS_GET_COMPONENT(targetNode, Layer, tarLayer);


		//auto lc = LayerColor::create();
		//lc->setColor(Color3B::BLACK);
		//lc->setOpacity(150);
		//_scene->addChild(lc, playerNode->getZOrder() + 1, 654321);
		//playerNode->setZOrder(playerNode->getZOrder() + 1);
		//targetNode->setZOrder(targetNode->getZOrder() + 1);

	}

	auto target = action.targets[0];
	auto targetNode = _playerNodes[target.pid];

	switch (skill->getTrigger().type)
	{
	case SkillData::TriggerType::CLOSE:
		CCLOG("pos: %f,%f", targetNode->getPosition().x, targetNode->getPosition().y);
		_currentNode = playerNode;
		_currentPoint = playerNode->getPosition();
		_currentZOrder = playerNode->getLocalZOrder();

		playerNode->runAction(MoveTo::create(0.3f, Point(targetNode->getPositionX() + 100 * playerNode->getTag(), targetNode->getPositionY())));
		playerNode->setLocalZOrder(TagFightLayer::PLAYER);
		break;
	case SkillData::TriggerType::DISTANCE:
		_currentNode = playerNode;
		_currentPoint = Point::ZERO;
		_currentZOrder = -1;
		break;
	default:
		break;
	}

	
}

void FightScene::runHitting(Node* targetNode, int type, int life, int energy, bool dead, bool hit)
{
	auto round = _fightData->getRound(_currentRound);
	auto action = round.actions[_currentAction];

	auto player = GameData::getInstance()->getPlayer(action.pid);
	auto skill = action.skill <= 5 ? player->getDefaultSkill() : GameData::getInstance()->getSkill(action.skill);


	//Director::getInstance()->getScheduler()->setTimeScale(1);
	//_isWreck = false;


	CCLOG("skill mid: %d, hitting: %s", skill->getInfo().mid, skill->getHitting().effect.c_str());

	CCS_GET_COMPONENT(targetNode, Layer, targetLayer);
	CCS_GET_CHILD(targetLayer, Layout, targetContainer, UI3_NODE_PLAYER);
	CCS_GET_CHILD(targetLayer, LoadingBar, targetEnerge, UI3_IMAGE_ENERGY);
	CCS_GET_CHILD(targetLayer, LoadingBar, targetBlood, UI3_IMAGE_BLOOD);
	CCS_GET_CHILD(targetContainer, Armature, targetAction, UI3_NODE_PLAYER_ANIMATION);

	if (life > 0 && life / skill->getTrigger().count == 0)
	{
		life = 1;
	}
	else
		life /= skill->getTrigger().count;
	CCLOG("current life: %d, count: %d", life, skill->getTrigger().count);

	// 	auto node = CCS_CREATE_LAYER("UI_FightWord/UI_FightWord_1.json");
	// 	CCS_GET_CHILD(node, TextBMFont, damageLabel, 5);
	// 	CCS_GET_CHILD(node, TextBMFont, typeLabel, 2);

	auto damageLabel = TextBMFont::create();

	if (life <= 0)
	{
		damageLabel->setText(("+" + STRING(life * -1)).c_str());
		damageLabel->setFntFile("Fonts/font_num3_green.fnt");
	}
	else
	{
		damageLabel->setText(("-" + STRING(life)).c_str());
		damageLabel->setFntFile("Fonts/font_num3.fnt");
	}

	TextBMFont *typeLabel = nullptr;
	//typeLabel->setText("");
	switch (type)
	{
	case FightData::Round::Action::DefenseType::HITTED:
		switch (action.skill)
		{
		case FightData::SkillType::CRIT:
		case FightData::SkillType::WRECK:

			//_isWreck = true;

			typeLabel = TextBMFont::create();
			typeLabel->setFntFile("Fonts/Fight_Font_4.fnt");
			typeLabel->setText(LocalizeService::getInstance()->getString(STRING(2000 + action.skill)).c_str());
			break;
		case FightData::SkillType::LUCKY:
			typeLabel = TextBMFont::create();
			typeLabel->setFntFile("Fonts/Fight_Font_3.fnt");
			typeLabel->setText(LocalizeService::getInstance()->getString(STRING(2000 + action.skill)).c_str());
			break;
		default:
			break;
		}
		break;
	case FightData::Round::Action::DefenseType::EVADE:
		break;
	case FightData::Round::Action::DefenseType::PARRY:
		typeLabel = TextBMFont::create();
		typeLabel->setFntFile("Fonts/Fight_Font_2.fnt");
		typeLabel->setText(LocalizeService::getInstance()->getString(STRING(2010 + action.skill)).c_str());
		break;
	}

	auto dsx = GameData::getInstance()->labelDamageStartX;
	auto dsy = GameData::getInstance()->labelDamageStartY;
	auto dlx = GameData::getInstance()->labelDamageLastX;
	auto dly = GameData::getInstance()->labelDamageLastY;
	auto tsx = GameData::getInstance()->labelTypeStartX;
	auto tsy = GameData::getInstance()->labelTypeStartY;
	auto tlx = GameData::getInstance()->labelTypeLastX;
	auto tly = GameData::getInstance()->labelTypeLastY;
	auto delay = GameData::getInstance()->labelDelay;

	damageLabel->setPosition(Point(targetNode->getPositionX() + dsx, targetNode->getPositionY() + dsy));
	damageLabel->runAction(Sequence::create(
		MoveTo::create(delay, Point(targetNode->getPositionX() + dlx, targetNode->getPositionY() + dly)),
		CallFunc::create(CC_CALLBACK_0(FightScene::onDamageCallback, this, damageLabel, true)),
		NULL));
	this->addChild(damageLabel, TagFightLayer::LABEL);

	if (typeLabel != nullptr)
	{
		typeLabel->setPosition(Point(targetNode->getPositionX() + tsx, targetNode->getPositionY() + tsy));
		typeLabel->runAction(Sequence::create(
			MoveTo::create(delay, Point(targetNode->getPositionX() + tlx, targetNode->getPositionY() + tly)),
			CallFunc::create(CC_CALLBACK_0(FightScene::onDamageCallback, this, typeLabel, true)),
			NULL));
		this->addChild(typeLabel, TagFightLayer::LABEL);
	}

	// 	node->setPosition(Point(targetNode->getPositionX() + dsx, targetNode->getPositionY() + dsy));
	// 	ActionManagerEx::getInstance()->playActionByName("UI_FightWord_1.json", "Animation0", CallFunc::create([node](){
	// 		node->setVisible(false);
	// 		r_armtures.push_back(node);
	// 	}));
	// // 	damageLabel->runAction(Sequence::create(
	// // 		MoveTo::create(delay, Point(targetNode->getPositionX() + dlx, targetNode->getPositionY() + dly)),
	// // 		CallFunc::create(CC_CALLBACK_0(FightScene::onDamageCallback, this, damageLabel, true)),
	// // 		NULL));
	// 	this->addChild(node, TagFightLayer::LABEL);

	auto target = static_cast<PlayerData*>(targetAction->getUserObject());
	target->getAttribute().HP_D += life;
	if (target->getAttribute().HP_D < 0)
	{
		target->getAttribute().HP_D = 0;
	}
	//target->getInfo().life -= life;

	//auto max = target->getHP();
	auto max = target->getInfo().life;
	auto crt = max - target->getAttribute().HP_D;
	auto percent = 100.0f * crt / max;
	percent = percent <= 10 ? (percent > 0 ? 10 : 0) : (percent >= 100 ? 100 : percent);

	targetBlood->setPercent(percent);
	target->setEnergy(target->getInfo().energy + energy);
	targetEnerge->setPercent(100.0f * target->getInfo().energy / target->getInfo().maxEnergy);

	CCLOG("[hit] [%f] hp: %d, max: %d, [%f], ep: %d, pid: %s", percent, crt, max, 100.0f * target->getInfo().energy / target->getInfo().maxEnergy, energy, target->getInfo().id.c_str());

	if (hit)
	{
		targetAction->getAnimation()->play(PlayerAction::HITTED);
	}

	if (dead)
	{
		targetAction->setUserData(new Value(target->getInfo().id));
	}

}

void FightScene::runAOE(SkillData* skill)
{
	CCS_GET_CHILD(_scene, Node, aoeNode, UI_NODE_AOE + (_currentNode->getTag() * -1 + 1) / 2);
	CCLOG("skill mid: %d, aoe mid: %s", skill->getInfo().mid, skill->getTrigger().effect.c_str());

	auto file = GET_SKILL_FILE("Aoe", skill->getTrigger().effect.c_str());
	r_armatureDatas.push_back(file);
	ArmatureDataManager::getInstance()->addArmatureFileInfo(file);
	auto armature = Armature::create(GET_SKILL_NAME("Aoe", skill->getTrigger().effect.c_str()));
	armature->getAnimation()->play(skill->getTrigger().effect);
	armature->setPosition(aoeNode->getPosition());
	armature->setScaleX(3 * _currentNode->getTag() * -1);
	armature->setScaleY(3);
	armature->getAnimation()->setMovementEventCallFunc(CC_CALLBACK_0(FightScene::onEffectEvent, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3));
	armature->getAnimation()->setFrameEventCallFunc(CC_CALLBACK_0(FightScene::onFrameEvent, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3, std::placeholders::_4));
	this->addChild(armature, TagFightLayer::AOE);
}

void FightScene::runEffect(SkillData* skill, Node* targetNode)
{
	CCS_GET_COMPONENT(targetNode, Layer, targetLayer);
	CCS_GET_CHILD(targetLayer, Layout, effectPoint, UI3_NODE_EFFECT);
	CCS_GET_CHILD(targetLayer, Layout, targetContainer, UI3_NODE_PLAYER);
	CCS_GET_CHILD(targetContainer, Armature, targetAction, UI3_NODE_PLAYER_ANIMATION);

	CCLOG("skill mid: %d, effect mid: %s", skill->getInfo().mid, skill->getTrigger().effect.c_str());

	auto file = GET_SKILL_FILE("Effect", skill->getTrigger().effect.c_str());
	r_armatureDatas.push_back(file);
	ArmatureDataManager::getInstance()->addArmatureFileInfo(file);
	auto armature = Armature::create(GET_SKILL_NAME("Effect", skill->getTrigger().effect.c_str()));
	armature->setPosition(targetNode->getPosition() + effectPoint->getPosition());
	armature->setUserObject(targetAction->getUserObject());
	//armature->setScale(4);
	armature->getAnimation()->play(skill->getTrigger().effect);
	armature->getAnimation()->setMovementEventCallFunc(CC_CALLBACK_0(FightScene::onEffectEvent, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3));
	armature->getAnimation()->setFrameEventCallFunc(CC_CALLBACK_0(FightScene::onFrameEvent, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3, std::placeholders::_4));
	this->addChild(armature, TagFightLayer::EFFECT);
}

void FightScene::showResult(float dt)
{
	if (_fightData->getInfo().isWin)
	{
		this->addChild(SettleScene::createScene(_fightData), TagFightLayer::SETTLE_SCENE, TagBaseScene::FIGHT_SUCCESS_VIEW);
	}
	else
	{
		this->addChild(FightFailView::create(_fightData), TagFightLayer::SETTLE_SCENE);
	}

	CCLOG("%s", Director::getInstance()->getTextureCache()->getCachedTextureInfo().c_str());
}



