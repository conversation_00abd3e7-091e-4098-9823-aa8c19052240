#ifndef __SOS_ITEM_TIPS_VIEW__
#define __SOS_ITEM_TIPS_VIEW__

#include "cocos2d.h"
#include "PopupView.h"
#include "../Datas/ItemData.h"
#include "../Datas/PlayerData.h"
#include "ui/CocosGUI.h"

static const std::string EVENT_USE_ITEM = "event_use_item";

class ItemTipsView : public PopupView
{
public:
	CREATE_FUNC(ItemTipsView);

	static ItemTipsView* create(ItemData* item, PlayerData* player);

public:
	ItemTipsView() {};
	~ItemTipsView();

	virtual bool init() override;

	void touchButton(cocos2d::Ref* object, cocos2d::ui::TouchEventType type);

private:
	void showCompare(cocos2d::ui::TextBMFont* text, int compare);
	void showCompare(cocos2d::ui::TextBMFont* text, float compare);
};

#endif