//
//  TeamData.h
//  TestSOS
//
//  Created by <PERSON><PERSON> on 14-2-11.
//
//

#ifndef __TestSOS__TeamData__
#define __TestSOS__TeamData__

#include "cocos2d.h"
#include "../SOSConfig.h"

typedef std::vector<std::string> PositionVector;

struct TeamPosition
{
	static const std::string EMPTY;
};

class TeamData : public cocos2d::Ref
{
public:
    FUNC_CREATE(TeamData)
    
public:
	enum FaceType : int
	{
		LEFT = 0,
		RIGHT = 1,
	};

    struct Info
    {
		int face;			//FaceType
        std::string pid;
		int skill;
    };

	struct User
	{
		std::string name;
	};
    
public:
	TeamData();
	virtual ~TeamData();

    inline Info& getInfo() { return _info; }
    inline void setInfo(Info& info) { _info = info; }

	inline User& getUser() { return _user; }
	inline void setUser(User& user) { _user = user; }
    
	inline PositionVector& getPositions() { return _positions; }
    inline void setPositions(const PositionVector& positions) { _positions = positions; }

	int getPositionCount();
    
private:
    Info _info;
	User _user;
    PositionVector _positions;
};

#endif /* defined(__TestSOS__TeamData__) */
