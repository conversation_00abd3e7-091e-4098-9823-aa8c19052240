//
//  CopyData.h
//  TestSOS
//
//  Created by <PERSON><PERSON> on 14-2-6.
//
//

#ifndef __TestSOS__CopyData__
#define __TestSOS__CopyData__

#include "cocos2d.h"
#include "../SOSConfig.h"

class CopiesRewardData : public cocos2d::Ref
{
public:
	FUNC_CREATE(CopiesRewardData);

public:
	struct Reward
	{
		int id;
		int value;
		bool isGet;
	};

	struct Info
	{
		int star;
		int diamond;
		std::vector<Reward> vecReward;
		bool isOpen;
	};

public:
	inline Info& getInfo() { return _info; }
	inline void setInfo(Info& info) { _info = info; }

	inline Reward& getReward() { return _reward; }
	inline void setReward(Reward& reward) { _reward = reward; }

private:
	Info _info;
	Reward _reward;
};

class ChapterData : public cocos2d::Ref
{
public:
	FUNC_CREATE(ChapterData);

public:
	enum EvaluateType : int
	{
		NO_STAR = 0,
		ONE_STAR = 1,
		TWO_STAR = 2,
		THREE_STAR = 3,
	};

	struct Info
	{
		int mid;
		int evaluate;
		int reqMobility;
		std::string name;
		std::string description;
		std::string start;
		std::string closing;
		int head;
		std::string suggest;
	};

	struct Reward
	{
		int type;
		int value;
	};

public:
	inline Info& getInfo() { return _info; }
	inline void setInfo(Info& info) { _info = info; }

	inline std::vector<ChapterData::Reward>& getRewards() { return _rewards; }

private:
	Info _info;
	std::vector<ChapterData::Reward> _rewards;
};

typedef cocos2d::Map<std::string, ChapterData *> ChapterMap;
typedef cocos2d::Map<std::string, CopiesRewardData *> CopiesRewardMap;

class CopyData : public cocos2d::Ref
{
public:
	FUNC_CREATE(CopyData);

public:
	struct Info
	{
		int mid;
		std::string name;
		int bgID;
		int level;
		int task;
		std::string suitLevel;
	};

public:
	inline Info& getInfo() { return _info; }
	inline void setInfo(Info& info) { _info = info; }

	inline ChapterMap& getChapters() { return _chapters; }
	inline CopiesRewardMap& getCopiesReward(){ return  _copiesRewards; }
private:
	Info _info;
	ChapterMap _chapters;
	CopiesRewardMap _copiesRewards;
};

#endif /* defined(__TestSOS__CopyData__) */
