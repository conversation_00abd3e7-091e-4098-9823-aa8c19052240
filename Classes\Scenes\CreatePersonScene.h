#ifndef __CREATEPERSON_SCENE_H__
#define __CREATEPERSON_SCENE_H__

#include "cocos2d.h"
#include "ui/CocosGUI.h"
#include "cocostudio/CocoStudio.h"
#include "../SOSConfig.h"
#include "../GameData.h"
#include "../Transactions/CreateTransaction.h"
#include "../LoadingScene.h"
#include "BaseScene.h"

//#if (CC_TARGET_PLATFORM == CC_PLATFORM_WIN32)
//#include <regex>  
//#endif  
//#if (CC_TARGET_PLATFORM == CC_PLATFORM_ANDROID||CC_TARGET_PLATFORM == CC_PLATFORM_IOS)  
//#include <regex.h>  
//#endif

class CreatePersonScene : public BaseScene
{
public:
	CREATE_FUNC(CreatePersonScene);
	static cocos2d::Scene* createScene();

public:
	CreatePersonScene();
	virtual ~CreatePersonScene();

	virtual bool init();  	
	virtual void onEnter() override;

	void menuCloseCallback(cocos2d::Ref* pSender);
	void touchButton(cocos2d::Ref* object, cocos2d::ui::TouchEventType type);

	void textFieldEvent(cocos2d::Ref* pSender, cocos2d::ui::TextFiledEventType type);
	
	void initUI2(int type);

private:
	Node* _node;
	int _seas;
	cocos2d::ui::TextField* _textFieldName;
};

#endif // __CREATEPERSON_SCENE_H__