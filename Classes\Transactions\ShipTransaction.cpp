#include "ShipTransaction.h"
#include "../Services/HTTPService.h"
#include "../GameData.h"

#include "ShipTransaction.h"

USING_NS_CC;
USING_STD;

static const string TRANS_GAME_AREILLERY_UPGRADE = "Game_Areillery_Upgrade.ashx";
static const string TRANS_GAME_AREILLERY_INLAY = "Game_Areillery_Inlay.ashx";
static const string TRANS_GAME_AREILLERY_GET = "Game_Areillery_Get.ashx";
static const string TRANS_GAME_HOUSE_APPOINT = "Game_House_Appoint.ashx";
static const string TRANS_GAME_HOUSE_UPGRADE = "Game_House_Upgrade.ashx";
static const string TRANS_GAME_HOUSE_QUERY = "Game_House_Query.ashx";
static const string TRANS_GAME_SHIP_USEING = "Game_Ship_Useing.ashx";
static const string TRANS_GAME_PALYER_TRAINING = "Game_Player_Training.ashx";
static const string TRANS_GAME_LEAVECITY = "Game_Leave_City.ashx";



FUNC_GET_INSTANCE(ShipTransaction);

ShipTransaction::ShipTransaction()
{
	Director::getInstance()->getEventDispatcher()->addCustomEventListener(TRANS_GAME_HOUSE_QUERY, [=](EventCustom* event) {

		Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_HOUSE_QUERY_SUCCESS, NULL);
	});
}

ShipTransaction::~ShipTransaction()
{
	Director::getInstance()->getEventDispatcher()->removeCustomEventListeners(TRANS_GAME_HOUSE_QUERY);
}

void ShipTransaction::use(std::string UID,std::string ShipGuid)
{
	auto data = HTTPData::create();
	data->write("UID", UID.c_str());
	data->write("ShipGuid",ShipGuid.c_str());
	auto url = WEB_HOST + TRANS_GAME_SHIP_USEING;
	HTTPService::getInstance()->request(url.c_str(), data,TRANS_GAME_SHIP_USEING.c_str());
}

void ShipTransaction::appoint()
{
	auto userData = GameData::getInstance()->getUserData();
	std::string shipGuid;

	for (auto ship : userData->getShips())
	{
		if (ship.second->getInfo().use)
		{
			shipGuid = ship.second->getInfo().id;
			break;
		}
	}

	std::vector<HTTPData *> value;
	auto data = HTTPData::create();
	data->write("ShipGuid", shipGuid.c_str());
	for (auto player : userData->getPlayers())
	{
		if (player->getInfo().appoint == 2 || player->getInfo().appoint == 3 || player->getInfo().appoint == 4)
		{
			auto data1 = HTTPData::create();
			data1->write("OnlyID", player->getInfo().id.c_str());
			data1->write("Type", player->getInfo().appoint);
			value.push_back(data1);
		}
	}
	data->write("List", value);
	auto url = WEB_HOST + TRANS_GAME_HOUSE_APPOINT;
	HTTPService::getInstance()->request(url.c_str(), data,TRANS_GAME_HOUSE_APPOINT.c_str());
}

void ShipTransaction::houseQuery(std::string UID)
{
	auto data = HTTPData::create();
	data->write("UID", UID.c_str());

	auto url = WEB_HOST + TRANS_GAME_HOUSE_QUERY;
	HTTPService::getInstance()->request(url.c_str(), data,TRANS_GAME_HOUSE_QUERY.c_str());

}

void ShipTransaction::playerTraining(std::string UID,std::string OnlyID,int training,int type,std::string ShipGuid)
{
	auto data = HTTPData::create();
	data->write("UID", UID.c_str());
	data->write("OnlyID", OnlyID.c_str());
	data->write("TrainType", training);
	data->write("Type", type);
	data->write("ShipGuid", ShipGuid.c_str());

	auto url = WEB_HOST + TRANS_GAME_PALYER_TRAINING;
	HTTPService::getInstance()->request(url.c_str(), data,TRANS_GAME_PALYER_TRAINING.c_str());
}

void ShipTransaction::houseUpgrade(std::string UID,std::string ShipGuid,int HouseType,int HouseID)
{
	auto data = HTTPData::create();
	data->write("UID", UID.c_str());
	data->write("ShipGuid", ShipGuid.c_str());
	data->write("HouseType", HouseType);
	data->write("HouseID", HouseID);

	auto url = WEB_HOST + TRANS_GAME_HOUSE_UPGRADE;
	HTTPService::getInstance()->request(url.c_str(), data,TRANS_GAME_HOUSE_UPGRADE.c_str());
}

void ShipTransaction::leaveCity(std::string UID)
{
	auto data = HTTPData::create();
	data->write("UID", UID.c_str());

	auto url = WEB_HOST + TRANS_GAME_LEAVECITY;
	HTTPService::getInstance()->request(url.c_str(), data, TRANS_GAME_LEAVECITY.c_str());
}