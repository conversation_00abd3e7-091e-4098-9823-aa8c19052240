#ifndef __SHIP_SCENE_H__
#define __SHIP_SCENE_H__

#include "cocos2d.h"
#include "ui/CocosGUI.h"
#include "cocostudio/CocoStudio.h"
#include "BaseScene.h"

class ShipScene : public BaseScene
{
public:
	static cocos2d::Scene* createScene();
	CREATE_FUNC(ShipScene);

public:
	ShipScene();
	virtual ~ShipScene();
	virtual bool init();  
	virtual void onEnter() override;
	void menuCloseCallback(cocos2d::Ref* pSender);
	void touchButton(cocos2d::Ref* object, cocos2d::ui::TouchEventType type);

	void refreshView();

public:
	cocos2d::Node* node;
	cocos2d::ui::Widget* _warehouse;
};

#endif // __SHIP_SCENE_H__