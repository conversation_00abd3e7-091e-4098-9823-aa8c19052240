﻿//
//  LoginTransaction.cpp
//  TestSOS
//
//  Created by <PERSON><PERSON> on 13-12-20.
//
//

#include "LoginTransaction.h"
#include "../Services/HTTPService.h"
#include "../GameData.h"
#include "../GlobalSchedule.h"

USING_NS_CC;
USING_STD;

static const char* TRANS_GAME_LOGIN = "Game_Login.ashx";
static const char* TRANS_GAME_VERSION = "Game_Version.ashx";
static const char* TRANS_GAME_VERIFY = "entry/login";
static const char* TRANS_GAME_BIND = "entry/bind";
static const char* TRANS_GAME_CM_LOGIN = "Game_CM_Login.ashx";
static const char* TRANS_GAME_REGISTER_GET = "Game_LoginRewards.ashx";
static const char* TRANS_GAME_BUY_STORE = "Game_BuyStore.ashx";
static const char* TRANS_GAME_USER_UPDATE = "Game_User_Update.ashx";

FUNC_GET_INSTANCE(LoginTransaction);

LoginTransaction::LoginTransaction()
{
	Director::getInstance()->getEventDispatcher()->addCustomEventListener(TRANS_GAME_REGISTER_GET, [=](EventCustom* event){

	});

	Director::getInstance()->getEventDispatcher()->addCustomEventListener(TRANS_GAME_LOGIN, [=](EventCustom* event) {
		auto data = static_cast<HTTPData *>(event->getUserData());

		GameData::getInstance()->randomName = data->readString("Name");

		auto userData = GameData::getInstance()->getUserData();

		UserData::Info& userInfo = userData->getInfo();
		userInfo.uid = data->readString("UID");
		userInfo.money = data->readInt("UserMoney");
		userInfo.diamond = data->readInt("Diamond");
		userInfo.mobility = data->readInt("Mobility");
		userInfo.sea = 1;
		userInfo.lastFightTime = data->readInt("LastFightTime");

		//GameData::getInstance()->mobilityTime = 0;
		GameData::getInstance()->shiproomTime = data->readInt("ShipCDTime");
		GameData::getInstance()->skillTime = data->readInt("MagicCDTime");
		GameData::getInstance()->jjcTime = data->readInt("JJcCDTime");
		userData->getInfo().rankId = data->readInt("TopID");
		userData->getInfo().property = data->readInt("ChallengeNumber");
		userData->getInfo().streakCount = data->readInt("StreakCount");
		userData->getInfo().face = data->readInt("FaceImg");
		userData->getInfo().loginCount = data->readInt("LoginCount");
		userData->getInfo().isReward = data->readBool("IsReward");

		auto inCity = data->readInt("InCity");
		userData->getInfo().city = data->readInt("CurrentCity");
		userData->getInfo().targetCity = data->readInt("TargetCity");
		userData->getInfo().inCity = inCity == 1 ? true : false;
		userData->getInfo().state = data->readInt("Guider");
		userData->getInfo().city = data->readInt("CurrentCity");
		userData->getGuidance().id = data->readInt("GuideSave");
		userData->getGuidance().action = 1;

		PlayerVector& players = userData->getPlayers();
		auto list = data->readDatas("PlayerList");
		for (auto i = 0; i < list.size(); i++)
		{
			auto element = list[i];
			auto mid = element->readInt("RoleID");
			auto playerData = PlayerData::create(mid);

			PlayerData::Info playerInfo = GameData::getInstance()->getPlayerInfo(mid);
			playerInfo.id = element->readString("OnlyID");
			playerInfo.name = element->readString("Name");
			playerInfo.playType = element->readInt("PlayType");
			playerInfo.isUse = static_cast<bool>(element->readInt("IsUse"));
			playerInfo.appoint = element->readInt("UseType");
			playerData->setInfo(playerInfo);

			PlayerData::Attribute playerAttri = GameData::getInstance()->getPlayerAttribute(mid);
			playerAttri.STR_U = element->readInt("UpStrengthValue");
			playerAttri.INC_U = element->readInt("UpBrainsValue");
			playerData->setAttribute(playerAttri);

			playerData->setLevel(element->readInt("Level"));
			playerData->getInfo().exp = element->readInt("Exp") - playerData->getInfo().allExp;

			auto defaultSkillID = playerInfo.defaultSkill;
			auto defaultSkillData = GameData::getInstance()->getSkill(defaultSkillID);
			playerData->setDefaultSkill(defaultSkillData);

			auto skillID = element->readInt("MagicID");
			auto skillData = GameData::getInstance()->getSkill(skillID);
			playerData->setSkill(skillData);

			auto teamSkillID = element->readInt("PassiveSkillsID");
			if (teamSkillID > 0)
			{
				//CCLOG("mid: %d, team skill id: %d", playerInfo.mid, passiveSkillsID);
				auto teamSkillData = GameData::getInstance()->getSkill(teamSkillID);
				playerData->setTeamSkill(teamSkillData);
			}

			auto equipList = element->readDatas("EquipList");
			for (auto equipData : equipList)
			{
				auto mid = equipData->readInt("TypeID");

				auto equipInfo = GameData::getInstance()->getItem(mid);
				equipInfo.id = equipData->readString("OnlyID");
				equipInfo.level = 1;
				equipInfo.count = 1;

				auto equip = ItemData::create();
				equip->setInfo(equipInfo);

				playerData->equip(equip);
			}

			if (i == 0) userInfo.role = playerInfo.id;

			players.pushBack(playerData);
			GameData::getInstance()->setPlayer(playerData);
		}

		auto cellList = data->readDatas("CellList");
		for (auto cell : cellList)
		{
			auto mid = cell->readInt("TypeID");

			auto itemInfo = GameData::getInstance()->getItem(mid);
			itemInfo.id = cell->readString("OnlyID");
			itemInfo.level = cell->readInt("PropsLevel");
			itemInfo.count = cell->readInt("Count");

			auto item = ItemData::create();
			item->setInfo(itemInfo);

			userData->addItem(item);
		}

		auto cargoList = data->readDatas("CargoList");
		for (auto cargoData : cargoList)
		{
			auto cargo = GameData::getInstance()->getCargo(cargoData->readInt("ID"));
			cargo.count = cargoData->readInt("Count");
			cargo.price = cargoData->readDouble("Price");
			userData->getCargos().push_back(cargo);
		}

		//        userInfo.userRenownList = data->readString("UserRenownList");

		if (players.size() == 0)
		{
			CCLOG("[HTTP] first login and need to create player ... ");
			Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_GAME_LOGIN_CREATE, NULL);
		}
		else
		{
			CCLOG("[HTTP] login success ...");
			Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_GAME_LOGIN_SUCCESS, NULL);
			GlobalSchedule::start(1, 0);
		}
		HTTPService::getInstance()->setHeadInfo("UDID", userInfo.uid);
	});

	Director::getInstance()->getEventDispatcher()->addCustomEventListener(TRANS_GAME_VERSION, [=](EventCustom* event) {
		auto data = static_cast<HTTPData *>(event->getUserData());

		auto isOK = data->readBool("IsOK");

		if (isOK)
		{
			Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_GAME_VERSION, NULL);
		}
		else
		{
			Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_GAME_NO_VERSION, NULL);
		}
	});

	Director::getInstance()->getEventDispatcher()->addCustomEventListener(TRANS_GAME_VERIFY, [=](EventCustom* event){
		auto data = static_cast<HTTPData *>(event->getUserData());

		auto token = data->readString("Token");
		auto uid = data->readString("UID");

		GameData::getInstance()->token = token;
		GameData::getInstance()->getUserData()->getInfo().uid = uid;
// 		UserDefault::getInstance()->setStringForKey("Token", token);
// 		UserDefault::getInstance()->flush();
		HTTPService::getInstance()->setHeadInfo("UDID", uid);

		CCLOG("[HTTP] verify success ...");
		Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_GAME_VERIFY_SUCCESS, NULL);
	});

	Director::getInstance()->getEventDispatcher()->addCustomEventListener(TRANS_GAME_REGISTER_GET, [=](EventCustom* event){
		auto userData = GameData::getInstance()->getUserData();
		auto data = static_cast<HTTPData *>(event->getUserData()); 
		auto propsList = data->readDatas("propsList");

		auto records = GameData::getInstance()->getRecordReward();
		for (auto record : records)
		{
			if (record.second.rewardID == GameData::getInstance()->getUserData()->getInfo().loginCount)
			{
				std::vector<std::string> result = split(record.second.reward, "|");
				std::map<int, int> maps;
				for (auto a : result)
				{
					std::vector<std::string> res = split(a, ",");
					maps[INT(res[0])] = INT(res[1]);
					if (INT(res[0]) == GameData::RewardType::PERSON)
					{
						auto player = data->readData("player");
						std::string onlyID =  player->readString("OnlyID");


						int magicID  = player->readInt("MagicID");
						int passiveSkillsID = player->readInt("PassiveSkillsID");

						auto playerData = PlayerData::create(INT(res[1]));
						PlayerData::Info playerInfo = GameData::getInstance()->getPlayerInfo(INT(res[1]));
						playerInfo.id = onlyID;
						playerInfo.isUse = false;
						playerInfo.appoint = PlayerData::AppointType::IDLE;
						playerInfo.playType = PlayerData::PlayType::PLAYING;
						playerInfo.exp = 0;
						playerData->setInfo(playerInfo);

						PlayerData::Attribute playerAttri = GameData::getInstance()->getPlayerAttribute(INT(res[1]));
						playerData->setAttribute(playerAttri);		
						playerData->setLevel(1);

						auto defaultSkillData = GameData::getInstance()->getSkill(playerInfo.defaultSkill);
						playerData->setDefaultSkill(defaultSkillData);

						playerData->setSkill(GameData::getInstance()->getSkill(magicID));
						if (passiveSkillsID > 0)
							playerData->setTeamSkill(GameData::getInstance()->getSkill(passiveSkillsID));

						GameData::getInstance()->getUserData()->getPlayers().pushBack(playerData);
						GameData::getInstance()->setPlayer(playerData);
					}
				}
				if (maps.size() > 0)
					GameData::getInstance()->addRewards(maps);
				break;
			}
		}
		for (auto props : propsList)
		{
			auto itemInfo = GameData::getInstance()->getItem(props->readInt("Typeid"));
			if (itemInfo.type == 5) //任务道具
			{
				bool b = false;
				for (auto& a : userData->getItems())
				{
					if (a.second->getInfo().mid == itemInfo.mid)
					{
						b = true;
						a.second->getInfo().count += props->readInt("Count");
						break;
					}
				}
				if (!b)
				{
					itemInfo.level = props->readInt("Level");
					itemInfo.count = props->readInt("Count");
					auto item = ItemData::create();
					item->setInfo(itemInfo);

					userData->addItem(item);
				}
			}
			else
			{
				itemInfo.id = props->readString("PropsGuid");
				itemInfo.level = 1;
				itemInfo.count = props->readInt("Count");
				auto item = ItemData::create();
				item->setInfo(itemInfo);

				userData->addItem(item);
			}
		}
		for (int i = GameData::MissionType::DIALOG; i <= GameData::MissionType::DEDUCTITEM; i++)
		{
			if (i != GameData::MissionType::APPOINTMENT&&i != GameData::MissionType::FORMATION&&i != GameData::MissionType::RANDOMPLAYER&&i != GameData::MissionType::SELL)
			{
				GameData::getInstance()->isTaskComplete(i, 0, 0);
			}
		}
		Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_GAME_LOGINREWARD, NULL);
	});

	Director::getInstance()->getEventDispatcher()->addCustomEventListener(TRANS_GAME_BUY_STORE, [=](EventCustom* event){
		auto data = static_cast<HTTPData *>(event->getUserData());
		auto mallId = data->readString("MallID");
		Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_GAME_PAY_MONEY_SUCCESS, &mallId);
	});
}

LoginTransaction::~LoginTransaction()
{
	Director::getInstance()->getEventDispatcher()->removeCustomEventListeners(TRANS_GAME_LOGIN);
	Director::getInstance()->getEventDispatcher()->removeCustomEventListeners(TRANS_GAME_VERSION);
	Director::getInstance()->getEventDispatcher()->removeCustomEventListeners(TRANS_GAME_VERIFY);
}

void LoginTransaction::login(string uid)
{
	auto data = HTTPData::create();
	data->write("UserID", uid.c_str());

	auto url = WEB_HOST + TRANS_GAME_LOGIN;

	HTTPService::getInstance()->request(url.c_str(), data, TRANS_GAME_LOGIN);
}

void LoginTransaction::getVersion(std::string version)
{
	auto data = HTTPData::create();
	data->write("Version", version.c_str());
	data->write("Platform", SOS_PLATFORM);

	auto url = WEB_HOST + TRANS_GAME_VERSION;

	HTTPService::getInstance()->request(url.c_str(), data, TRANS_GAME_VERSION);
}

void LoginTransaction::getLoginReward()
{
	auto data = HTTPData::create();
	//data->write("", "");

	auto url = WEB_HOST + TRANS_GAME_REGISTER_GET;

	HTTPService::getInstance()->request(url.c_str(), data, TRANS_GAME_REGISTER_GET);
}

void LoginTransaction::verify(std::string token)
{
	auto url = ENTRY_SERVER + TRANS_GAME_VERIFY + "?token=" + token + "&platform=" + STRING(SOS_PLATFORM);
	HTTPService::getInstance()->request(url.c_str(), TRANS_GAME_VERIFY);
}

void LoginTransaction::bind(std::string token, std::string uid)
{
	auto url = ENTRY_SERVER + TRANS_GAME_BIND + "?token=" + token + "&uid=" + uid + "&platform=" + STRING(SOS_PLATFORM);
	HTTPService::getInstance()->request(url.c_str(), TRANS_GAME_BIND);
}

void LoginTransaction::cmLogin(std::string cmUID)
{
	auto data = HTTPData::create();
	data->write("CMUserID", cmUID.c_str());
	data->write("Platform", SOS_PLATFORM);

	auto url = WEB_HOST + TRANS_GAME_CM_LOGIN;
	HTTPService::getInstance()->request(url.c_str(), data, TRANS_GAME_CM_LOGIN);
}

void LoginTransaction::payMoney(std::string billing, std::string tradeId)
{
	auto data = HTTPData::create();
	data->write("TradeID", tradeId.c_str());

	auto url = WEB_HOST + TRANS_GAME_BUY_STORE;
	HTTPService::getInstance()->request(url.c_str(), data, TRANS_GAME_BUY_STORE);
}

void LoginTransaction::updateInfo()
{
	auto user = GameData::getInstance()->getUserData();
	
	auto data = HTTPData::create();
	data->write("InCity", INT(user->getInfo().inCity));
	data->write("CurrentCity", user->getInfo().city);
	data->write("TargetCity", user->getInfo().targetCity);
	data->write("Guider", user->getInfo().state);
	data->write("GuiderSave", user->getGuidance().save);

	auto url = WEB_HOST + TRANS_GAME_USER_UPDATE;
	HTTPService::getInstance()->request(url.c_str(), data, TRANS_GAME_USER_UPDATE);
}