#include "ResourceService.h"

USING_NS_CC;

FUNC_GET_INSTANCE(ResourceService);

// std::vector<ValueMap> ResourceService::getCSVConfigFromZip(const char* zipFileName, const char* csvFileName)
// {
// 	long fZize = 0;
// 	unsigned char* buff = CCFileUtils::sharedFileUtils()->getFileDataFromZip(zipFileName, csvFileName, &fZize);
// 	auto csv = String::createWithData(buff, fZize)->getCString();
// 	auto rows = split(csv, "\n");
// 	auto heads = split(rows[0], ",");
// 	std::vector<ValueMap> configs;
// 	for (int i = 1; i < rows.size(); i++)
// 	{
// 		auto cols = split(rows[i], ",");
// 
// 		ValueMap maps;
// 
// 		for (int j = 0; j < cols.size(); j++)
// 		{
// 			auto data = cols[j];
// 			auto head = heads[j];
// 
// 			maps[head] = Value(data);
// 		}
// 
// 		configs.push_back(maps);
// 	}
// 
// 	return configs;
// }

std::vector<ValueMap> ResourceService::getCSVConfig(const char* csvFileName)
{
	auto csv = FileUtils::getInstance()->getStringFromFile(csvFileName);
	auto rows = split(csv, "\n");
	auto heads = split(rows[0], ",");
	std::vector<ValueMap> configs;
	for (int i = 1; i < rows.size(); i++)
	{
		auto cols = split(rows[i], ",");

		ValueMap maps;

		for (int j = 0; j < cols.size(); j++)
		{
			auto data = cols[j];
			auto head = heads[j];

			maps[head] = Value(data);
		}

		configs.push_back(maps);
	}

	return configs;
}

