﻿#ifndef __PARTNER_SCENE_H__
#define __PARTNER_SCENE_H__

#include "cocos2d.h"
#include "ui/CocosGUI.h"
#include "cocostudio/CocoStudio.h"
#include "../SOSConfig.h"
#include "../GameData.h"
#include "../Transactions/PartnerTransaction.h"
#include "BaseScene.h"

class PartnerScene : public BaseScene
{
public:
	CREATE_FUNC(PartnerScene);
	static cocos2d::Scene* createScene();

public:
	PartnerScene();
	virtual ~PartnerScene();
	
	virtual bool init();  
	virtual void onEnter() override;
	void menuCloseCallback(cocos2d::Ref* pSender);

	void touchButton(cocos2d::Ref* object, cocos2d::ui::TouchEventType type);
	void touchScrollview(cocos2d::Ref* object, cocos2d::ui::ScrollviewEventType type);

	virtual bool onTouchBegan(cocos2d::Touch* touch, cocos2d::Event* event);  
	virtual void onTouchMoved(cocos2d::Touch* touch, cocos2d::Event* event);  
	virtual void onTouchEnded(cocos2d::Touch* touch, cocos2d::Event* event);  

private:
	void initScrollview();

private:
	cocos2d::Node* node;
	cocos2d::EventListenerTouchOneByOne* _listener;
};

#endif // __PARTNER_SCENE_H__