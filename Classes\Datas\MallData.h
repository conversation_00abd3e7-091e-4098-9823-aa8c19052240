#ifndef __TestSOS__MallData__
#define __TestSOS__MallData__

#include "cocos2d.h"
#include "../SOSConfig.h"

class MallData :public cocos2d::Ref
{
public:
	FUNC_CREATE(MallData);
public:
	struct Info
	{
		std::string billingIndex;
		int propsType;
		int propsValue;
		float currencyValue;
		int sorting;
	};

public:
	inline Info& getInfo() { return _info; }
	inline void setInfo(Info info) { _info = info; }
private:
	Info _info;
};

#endif