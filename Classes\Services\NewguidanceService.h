#ifndef __NEWGUIDANCE_SERVICE__
#define __NEWGUIDANCE_SERVICE__

#include "cocos2d.h"
#include "../SOSConfig.h"

static const std::string EVENT_NEWGUIDANCE_START = "event_newguidance_start";
static const std::string EVENT_NEWGUIDANCE_CHANGED = "event_newguidance_changed";
static const std::string EVENT_NEWGUIDANCE_TOUCHED = "event_button_touch_ended";

class NewguidanceService : public cocos2d::Ref
{
public:
	FUNC_INSTANCE(NewguidanceService);

public:
	NewguidanceService();
	~NewguidanceService();

	bool createLayer();
	bool createLayer(cocos2d::Node* parent);
	bool createLayer(cocos2d::Node* parent, int type);

private:
	bool _isFinshed;
};

#endif //__NEWGUIDANCE_SERVICE__
