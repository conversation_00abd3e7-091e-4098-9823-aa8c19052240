﻿#include "CityScene.h"
#include "ShopScene.h"
#include "GovernmentScene.h"
#include "FormationScene.h"
#include "PartnerScene.h"
#include "PubScene.h"
#include "WeaponShopScene.h"
#include "../Views/MissionView.h"
#include "DockScene.h"
#include "ShipScene.h"
#include "ArenaScene.h"
#include "MallScene.h"
#include "HonorScene.h"
#include "MinimapScene.h"
#include "../GameData.h"
#include "../MapScene.h"
#include "../Views/NPCView.h"
#include "../Transactions/DockTransaction.h"
#include "../Transactions/RecruitTransaction.h"
#include "../Transactions/FormationTransaction.h"
#include "../Transactions/GovernmentTransaction.h"
#include "../Services/LocalizeService.h"
#include "../Services/NewguidanceService.h"
#include "../Views/AlertView.h"
#include "../Transactions/ArenaTransaction.h"
#include "../Views/RegisterView.h"
#include "../Views/HeadView.h"
#include "../Views/NoticeView.h"
#include "../Services/EffectService.h"
#include "../Services/SoundService.h"

USING_NS_CC;
USING_NS_CC_CCS;
USING_NS_CC_UI;

enum TagCity
{
	UI_CITYVIEW_1 = 10009,
	UI_CITYVIEW_2 = 10010,
	UI_CITYVIEW_3 = 10011,

	UI_ANIMATION_MAP = 10035,
	UI_ANIMATION_MARK = 10014,

	UI_TEXT_CITY = 15,
	UI_TEXT_MONEY = 19,
	UI_TEXT_DIAMOND = 21,
	UI_BUTTON_GOVERNMENT = 40001,	//总督
	UI_BUTTON_SHOP = 40002,	//交易所
	UI_BUTTON_PUB = 40003,	//酒馆
	UI_BUTTON_WEAPON = 40004,	//铁匠铺
	UI_BUTTON_SHIP = 40005,	//造船厂
	UI_BUTTON_TERMINAL = 40006,	//码头
	UI_BUTTON_ADD = 22,	//添加
	UI_BUTTON_GOTOSHOP = 23,	//商城
	UI_BUTTON_PARTNER = 25,	//伙伴
	UI_BUTTON_MYSHIP = 26,	//我的船只
	UI_BUTTON_FORMATION = 27,	//阵型
	UI_BUTTON_SOCIAL = 28,	//社交
	UI_BUTTON_HONOR = 29,	//铸造
	//UI_BUTTON_INFO          = 30,	//攻略
	UI_BUTTON_HEAD = 32,	//头像
	UI_BUTTON_MISSION = 35,	//任务
	UI_BUTTON_RANK = 36,	//竞技场
	UI_TEXT_LVTEXT = 34,
	UI_BUTTON_SIGN = 80001,	//签到

	UI_IMAGEVIEW_MAP = 14,

	UI_IMAGEVIEW_1_1 = 20036,
	UI_IMAGEVIEW_1_2 = 20037,
	UI_IMAGEVIEW_1_3 = 20038,
	UI_IMAGEVIEW_1_4 = 20039,
	UI_IMAGEVIEW_1_5 = 20040,

	UI_ANIMATION_CLOUD = 999999,
	UI_ANIMATION_NEWTASK = 999998,

	UI_BUTTON_NOTICE = 1181, //公告or 设置


};

std::string r_cityBgName;
Armature* r_newTask;
bool r_cloud;
//static cocos2d::ui::Widget *s_cloud;

Scene* CityScene::createScene(bool cloud)
{
	r_cloud = cloud;

	// 'scene' is an autorelease object
	auto scene = Scene::create();

	// 'layer' is an autorelease object
	auto layer = CityScene::create();

	// add layer as a child to scene
	scene->addChild(layer);

// 	s_cloud = CCS_CREATE_LAYER("UI_LoadingCloud/UI_LoadingCloud_1");
// 	s_cloud->setTouchEnabled(false);
// 	s_cloud->setVisible(false);
// 	scene->addChild(s_cloud, UI_ANIMATION_CLOUD, UI_ANIMATION_CLOUD);
// 
// 	if (cloud)
// 	{
// 		s_cloud->setVisible(true);
// 		ActionManagerEx::getInstance()->playActionByName("UI_LoadingCloud_1.json", "Animation0");
// 	}

	// return the scene
	return scene;
}

Widget* CityScene::getCloudWidget()
{
	return nullptr;
}

CityScene::CityScene()
: _cloud(nullptr)
{
	r_newTask = nullptr;
}

CityScene::~CityScene()
{
	_cloud = nullptr;
	r_newTask = nullptr;

	Director::getInstance()->getTextureCache()->removeTextureForKey(r_cityBgName);

	this->getEventDispatcher()->removeCustomEventListeners(EVENT_LEVEL_CHANGED);
	this->getEventDispatcher()->removeCustomEventListeners(EVENT_NPCVIEW_CHANGED);
	this->getEventDispatcher()->removeCustomEventListeners(EVENT_MONEY_CHANGED);
	this->getEventDispatcher()->removeCustomEventListeners(EVENT_GAME_RANKGET_SUCCESS);
	this->getEventDispatcher()->removeCustomEventListeners(EVENT_TASK_CREATED);
	this->getEventDispatcher()->removeCustomEventListeners(EVENT_FACE_CHANGED);
	this->getEventDispatcher()->removeCustomEventListeners(EVENT_DIAMOND_CHANGED);
	this->getEventDispatcher()->removeCustomEventListeners(EVENT_CITY_SCENE_CLOUD_CLOSE);

	ActionManagerEx::getInstance()->releaseActions();
}

bool CityScene::init()
{
	if (!BaseScene::init())
	{
		return false;
	}

	node = CCS_CREATE_SCENE("Scene_CityView");
	node->setTag(LAYER);
	this->addChild(node);

	auto size = Director::getInstance()->getWinSize();

	Texture2D::setDefaultAlphaPixelFormat(Texture2D::PixelFormat::RGB888);
	_cloud = Sprite::create("ss_loadingcloud_new.png");
	_cloud->setPosition(size.width / 2, size.height / 2);
	this->addChild(_cloud, 99999999);
	Texture2D::setDefaultAlphaPixelFormat(Texture2D::PixelFormat::DEFAULT);

	if (r_cloud)
	{
		auto action = FadeOut::create(40.0f / 60);
		_cloud->runAction(action);
	}
	else
	{
		_cloud->setVisible(false);
		_cloud->setOpacity(0);
	}

	auto userData = GameData::getInstance()->getUserData();
	auto city = GameData::getInstance()->getCity(userData->getInfo().city);

	CCS_GET_COMPONENT_FROM_SCENE(node, Layer, closeLayer, 11000);
	CCS_GET_CHILD(closeLayer, Button, closeBtn, 298);
	closeBtn->removeFromParent();

	CCS_GET_COMPONENT_FROM_SCENE(node, Layer, funsLayer, UI_CITYVIEW_1);

	for (auto fun : city->getInfo().funs)
	{
		CCS_GET_CHILD(funsLayer, Button, button, 40000 + INT(fun));
		button->setOpacity(255);
		button->setTouchEnabled(true);
		button->setPressedActionEnabled(true);
		button->addTouchEventListener(this, toucheventselector(CityScene::touchButton));
	}

	CCS_GET_COMPONENT_FROM_SCENE(node, Layer, layer2, UI_CITYVIEW_2);
	CCS_GET_CHILD(layer2, Button, addBtn, UI_BUTTON_ADD);
	CCS_GET_CHILD(layer2, Text, cityText, UI_TEXT_CITY);
	CCS_GET_CHILD(layer2, Text, cointext, UI_TEXT_MONEY);
	CCS_GET_CHILD(layer2, Text, diamondtext, UI_TEXT_DIAMOND);
	CCS_GET_CHILD(layer2, Button, gotoshopBtn, UI_BUTTON_GOTOSHOP);
	CCS_GET_CHILD(layer2, Button, mapImg, UI_IMAGEVIEW_MAP);
	CCS_GET_CHILD(layer2, Button, headbgBtn, UI_BUTTON_HEAD);
	CCS_GET_CHILD(layer2, Text, lvtext, UI_TEXT_LVTEXT);
	cityText->setText(LocalizeService::getInstance()->getString(city->getInfo().name));
	cointext->setText(STRING(userData->getInfo().money));
	diamondtext->setText(STRING(userData->getInfo().diamond));
	addBtn->addTouchEventListener(this, toucheventselector(CityScene::touchButton));
	mapImg->setTouchEnabled(true);
	mapImg->addTouchEventListener(this, toucheventselector(CityScene::touchButton));

	CCS_GET_COMPONENT_FROM_SCENE(node, Layer, layer3, UI_CITYVIEW_3);
	CCS_GET_CHILD(layer3, Button, partnerBtn, UI_BUTTON_PARTNER);
	CCS_GET_CHILD(layer3, Button, myshipBtn, UI_BUTTON_MYSHIP);
	CCS_GET_CHILD(layer3, Button, formationBtn, UI_BUTTON_FORMATION);
	//(layer3, Button, socialBtn, UI_BUTTON_SOCIAL); 
	CCS_GET_CHILD(layer3, Button, honorBtn, UI_BUTTON_HONOR);
	CCS_GET_CHILD(layer3, Button, missionBtn, UI_BUTTON_MISSION);
	CCS_GET_CHILD(layer3, Button, rankBtn, UI_BUTTON_RANK);
	CCS_GET_CHILD(layer3, Button, signBtn, UI_BUTTON_SIGN);
	CCS_GET_CHILD(layer3, Button, noticeBtn, UI_BUTTON_NOTICE);
	gotoshopBtn->addTouchEventListener(this, toucheventselector(CityScene::touchButton));
	partnerBtn->addTouchEventListener(this, toucheventselector(CityScene::touchButton));
	myshipBtn->addTouchEventListener(this, toucheventselector(CityScene::touchButton));
	formationBtn->addTouchEventListener(this, toucheventselector(CityScene::touchButton));
	//socialBtn->addTouchEventListener(this, toucheventselector(CityScene::touchButton));
	honorBtn->addTouchEventListener(this, toucheventselector(CityScene::touchButton));
	headbgBtn->addTouchEventListener(this, toucheventselector(CityScene::touchButton));
	//int id = userData->getPlayer(userData->getInfo().role)->getInfo().mid;
	headbgBtn->loadTextures(GET_PLAYER_ROLE(GameData::getInstance()->getUserData()->getInfo().face), GET_PLAYER_ROLE(GameData::getInstance()->getUserData()->getInfo().face), "", TextureResType::PLIST);
	missionBtn->addTouchEventListener(this, toucheventselector(CityScene::touchButton));
	rankBtn->addTouchEventListener(this, toucheventselector(CityScene::touchButton));
	signBtn->addTouchEventListener(this, toucheventselector(CityScene::touchButton));
	noticeBtn->addTouchEventListener(this, toucheventselector(CityScene::touchButton));
	
	gotoshopBtn->setPressedActionEnabled(true);
	partnerBtn->setPressedActionEnabled(true);
	myshipBtn->setPressedActionEnabled(true);
	formationBtn->setPressedActionEnabled(true);
	//socialBtn->setPressedActionEnabled(true);
	honorBtn->setPressedActionEnabled(true);
	headbgBtn->setPressedActionEnabled(true);
	missionBtn->setPressedActionEnabled(true);
	rankBtn->setPressedActionEnabled(true);
	signBtn->setPressedActionEnabled(true);
	noticeBtn->setPressedActionEnabled(true);

	auto players = userData->getPlayers();
	for (auto player : players)
	{
		if (player->getInfo().type <= 4)
		{
			lvtext->setText(STRING(player->getInfo().level));
			break;
		}
	}

	//rankBtn->runAction(RepeatForever::create(Sequence::create(RotateTo::create(0.5, 180), RotateTo::create(0.5, 360), NULL)));

	Texture2D::setDefaultAlphaPixelFormat(Texture2D::PixelFormat::RGB888);
	r_cityBgName = String::createWithFormat("ss_city_%d.jpg", city->getInfo().bg)->getCString();
	auto imgBg = Sprite::create(r_cityBgName);
	imgBg->setAnchorPoint(Point(0, 0));
	this->addChild(imgBg);
	Texture2D::setDefaultAlphaPixelFormat(Texture2D::PixelFormat::DEFAULT);

	initMission();

	// 	NewguidanceService::getInstance()->checkCreate();
	// 	if (GameData::getInstance()->isCreate)
	// 	{
	// 		this->addChild(NewguidanceService::getInstance()->createLayer(), this->getChildrenCount() + 1);
	// 	}

	//_label = Label::create();
	//_label->setPosition(this->getContentSize().width / 2, this->getContentSize().height / 2);
	//this->addChild(_label, 999);

	//LayerColor* pLayer = LayerColor::create(ccc4(0, 0, 0, 100));
	//ClippingNode *clippingNode = ClippingNode::create();
	//clippingNode->setInverted(true);
	//this->addChild(clippingNode, 99999);
	//clippingNode->addChild(pLayer);

	//DrawNode *pStencil = DrawNode::create();
	//Point rect[4] = { ccp(-50, 50), ccp(50, 50), ccp(50, -50), ccp(-50, -50) };
	//pStencil->drawPolygon(rect, 4, Color4F(1, 0, 0, 1), 0, Color4F(1, 0, 0, 1));
	//pStencil->setPosition(480, 320);
	//clippingNode->setStencil(pStencil);
	return true;
}

void CityScene::onEnter()
{
	this->getEventDispatcher()->addCustomEventListener(EVENT_TASK_CREATED, [=](EventCustom* event){
		if (r_newTask != nullptr)
		{
			return;
		}
		
		CCS_GET_COMPONENT_FROM_SCENE(node, Layer, layer3, UI_CITYVIEW_3);
		CCS_GET_CHILD(layer3, Button, missionBtn, UI_BUTTON_MISSION);
// 		auto emitter = ParticleSystemQuad::create("Efficiency/energy.plist");
// 		emitter->setPosition(missionBtn->getPosition());

		ArmatureDataManager::getInstance()->addArmatureFileInfo("Animation_Light.ExportJson");
		r_newTask = Armature::create("Animation_Light");
		r_newTask->getAnimation()->play("Animation1");
		r_newTask->setPosition(missionBtn->getPosition());
		r_newTask->setScaleX(1.0f);
		r_newTask->setScaleY(2.5f);
		layer3->addChild(r_newTask, layer3->getChildrenCount() + 1, UI_ANIMATION_NEWTASK);
	});

	this->getEventDispatcher()->addCustomEventListener(EVENT_LEVEL_CHANGED, [=](EventCustom* event){
		auto pid = static_cast<std::string *>(event->getUserData());
		if (*pid == GameData::getInstance()->getUserData()->getInfo().role)
		{
			EffectService::getInstance()->play("Animation_Task", "Animation3");
			SoundService::getInstance()->playSFX(SOUND_EFFECT_LEVELUP);

			CCS_GET_COMPONENT_FROM_SCENE(node, Layer, layer3, UI_CITYVIEW_2);
			CCS_GET_CHILD(layer3, Text, lvtext, UI_TEXT_LVTEXT);
			lvtext->setText(STRING(GameData::getInstance()->getUserData()->getPlayer(*pid)->getInfo().level));
		}

		
	});

	this->getEventDispatcher()->addCustomEventListener(EVENT_NPCVIEW_CHANGED, [this](EventCustom* event){
		this->scheduleOnce(schedule_selector(CityScene::removeNpcView), 1.0f / 60);
		initMission();
	});

	this->getEventDispatcher()->addCustomEventListener(EVENT_DIAMOND_CHANGED, [this](EventCustom* event){
		auto diamond = static_cast<int *>(event->getUserData());
		CCS_GET_COMPONENT_FROM_SCENE(node, Layer, layer2, UI_CITYVIEW_2);
		CCS_GET_CHILD(layer2, Text, diamondtext, UI_TEXT_DIAMOND);
		diamondtext->setText(STRING(*diamond));
	});

	this->getEventDispatcher()->addCustomEventListener(EVENT_MONEY_CHANGED, [this](EventCustom* event){
		auto money = static_cast<int *>(event->getUserData());
		CCS_GET_COMPONENT_FROM_SCENE(node, Layer, layer2, UI_CITYVIEW_2);
		CCS_GET_CHILD(layer2, Text, cointext, UI_TEXT_MONEY);
		cointext->setText(STRING(*money));
	});

	this->getEventDispatcher()->addCustomEventListener(EVENT_GAME_RANKGET_SUCCESS, [=](EventCustom* event) {
		this->removeChildByTag(999999);
		GameData::getInstance()->getUserData()->getInfo().property = 10;
		Director::getInstance()->replaceScene(ArenaScene::createScene());
	});

	this->getEventDispatcher()->addCustomEventListener(EVENT_FACE_CHANGED, [=](EventCustom* event) {
		auto face = static_cast<std::string *>(event->getUserData());
		auto userData = GameData::getInstance()->getUserData();
		userData->getInfo().face = INT(*face);
		CCS_GET_COMPONENT_FROM_SCENE(node, Layer, layer3, UI_CITYVIEW_2);
		CCS_GET_CHILD(layer3, Button, headbgBtn, UI_BUTTON_HEAD);
		headbgBtn->loadTextures(GET_PLAYER_ROLE(GameData::getInstance()->getUserData()->getInfo().face), GET_PLAYER_ROLE(GameData::getInstance()->getUserData()->getInfo().face), "", TextureResType::PLIST);
	});

	this->getEventDispatcher()->addCustomEventListener(EVENT_CITY_SCENE_CLOUD_CLOSE, [=](EventCustom* event) {
		_cloud->setVisible(true);

		auto fadein = FadeIn::create(40.0f / 60);
		auto callfunc = CallFunc::create(CC_CALLBACK_0(CityScene::onCloudClose, this));
		_cloud->runAction(Sequence::create(fadein, callfunc, nullptr));
	});
	
	// 	this->getEventDispatcher()->addCustomEventListener(EVENT_GUIDANCE_FINISHED, [=](EventCustom* event) {
	// 		if (GameData::getInstance()->getUserData()->getInfo().state == UserData::StateType::GUIDER)
	// 		{
	// 			NewguidanceService::getInstance()->createLayer(this);
	// 		}
	// 	});


	//this->schedule(schedule_selector(CityScene::update), 1.0f);
// 	if (GameData::getInstance()->getUserData()->getInfo().state == UserData::StateType::GUIDER)
// 	{
// 		//this->addChild(NewguidanceService::getInstance()->createLayer(), this->getChildrenCount() + 1);
// 		NewguidanceService::getInstance()->createLayer(this);
// 	}

	// 	SpriteFrameCache::getInstance()->removeUnusedSpriteFrames();
	// 	Director::getInstance()->getTextureCache()->removeUnusedTextures();
	CCLOG("%s", Director::getInstance()->getTextureCache()->getCachedTextureInfo().c_str());

	BaseScene::onEnter();
}

void CityScene::touchButton(Ref* obj, cocos2d::ui::TouchEventType eventType)
{

	auto button = dynamic_cast<Widget*>(obj);
	int tag = button->getTag();
	switch (eventType)
	{
	case TouchEventType::TOUCH_EVENT_BEGAN:
		break;
	case TouchEventType::TOUCH_EVENT_ENDED:
		auto userData = GameData::getInstance()->getUserData();

		if (tag == UI_BUTTON_GOVERNMENT)
		{
			this->addChild(NPCView::createScene(NPCType::GOVERNMENT), this->getChildrenCount() + 1, TagBaseScene::NPC_VIEW);
		}
		else if (tag == UI_BUTTON_SHOP)
		{
			this->addChild(NPCView::createScene(NPCType::SHOP), this->getChildrenCount() + 1, TagBaseScene::NPC_VIEW);
		}
		else if (tag == UI_BUTTON_PUB)
		{
			this->addChild(NPCView::createScene(NPCType::PUB), this->getChildrenCount() + 1, TagBaseScene::NPC_VIEW);
		}
		else if (tag == UI_BUTTON_PARTNER)
		{
			Director::getInstance()->replaceScene(PartnerScene::createScene());
		}
		else if (tag == UI_BUTTON_FORMATION)
		{
			Director::getInstance()->replaceScene(FormationScene::createScene());
		}
		else if (tag == UI_BUTTON_WEAPON)
		{
			this->addChild(NPCView::createScene(NPCType::WEAPON), this->getChildrenCount() + 1, TagBaseScene::NPC_VIEW);
		}
		else if (tag == UI_BUTTON_MISSION)
		{
			if (r_newTask != nullptr)
			{
				r_newTask->removeFromParent();
				r_newTask = nullptr;
			}

			auto view = MissionView::createScene();
			view->setLocalZOrder(1);
			addChild(view);
		}
		else if (tag == UI_BUTTON_SHIP)
		{
			this->addChild(NPCView::createScene(NPCType::DOCK), this->getChildrenCount() + 1, TagBaseScene::NPC_VIEW);
		}
		else if (tag == UI_BUTTON_MYSHIP)
		{
			Director::getInstance()->replaceScene(ShipScene::createScene());
		}
		else if (tag == UI_BUTTON_RANK)
		{
			//if(userData->getInfo().level>9)
			if (userData->getRanks().size() > 0)
			{
				Director::getInstance()->replaceScene(ArenaScene::createScene());
			}
			else
			{
				ArenaTransaction::getInstance()->rankGet(userData->getInfo().uid, 1);
				this->addChild(AlertView::create(LocalizeService::getInstance()->getString("632"), AlertType::LOADING), 999999);
			}

		}
		else if (tag == UI_BUTTON_GOTOSHOP)
		{
			//this->addChild(AlertView::create(LocalizeService::getInstance()->getString("4001"), AlertType::IDLE), 999999);
			Director::getInstance()->replaceScene(MallScene::createScene());
		}
		else if (tag == UI_BUTTON_HONOR)
		{
			this->addChild(AlertView::create(LocalizeService::getInstance()->getString("4001"), AlertType::IDLE), 999999);
			//Director::getInstance()->replaceScene(HonorScene::createScene());
		}
		else if (tag == UI_BUTTON_TERMINAL)
		{
			this->addChild(NPCView::createScene(NPCType::WHARF), this->getChildrenCount() + 1, TagBaseScene::NPC_VIEW);
		}
		else if (tag == UI_BUTTON_ADD)
		{
			//CCLOG(LocalizeService::getInstance()->getString("4001").c_str());
			this->addChild(AlertView::create(LocalizeService::getInstance()->getString("4001"), AlertType::IDLE), 999999);
		}
		else if (tag == UI_BUTTON_SOCIAL)
		{
			this->addChild(AlertView::create(LocalizeService::getInstance()->getString("4001"), AlertType::IDLE), 999999);
		}
		else if (tag == UI_IMAGEVIEW_MAP)
		{
			if (userData->getInfo().inCity)
			{
				Director::getInstance()->replaceScene(MinimapScene::createScene());
			}
			else
			{
				this->addChild(MinimapScene::create(), UI_ANIMATION_CLOUD);
			}
			//this->addChild(MapView::createScene(), this->getChildrenCount() + 1);
		}
		else if (tag == UI_BUTTON_SIGN)
		{
			this->addChild(RegisterView::create(), this->getChildrenCount() + 1, TagBaseScene::SIGNIN_VIEW);
		}
		else if (tag == UI_BUTTON_NOTICE)
		{

			this->addChild(AlertView::create("", AlertType::SETUP), 999999);

			//this->addChild(NoticeView::create(), this->getChildrenCount() + 1);
		}
		else if(tag == UI_BUTTON_HEAD)
		{
			this->addChild(HeadView::create(), this->getChildrenCount() + 1);
		}
		break;
	}
}

void CityScene::removeNpcView(float dt)
{
	// 	NewguidanceService::getInstance()->checkCreate();
	// 	if (GameData::getInstance()->isCreate)
	// 		this->addChild(NewguidanceService::getInstance()->createLayer(), this->getChildrenCount() + 3);
}

void CityScene::initMission()
{
	// 	CCS_GET_ARMATURE_FROM_SCENE(node, Armature, mark1, UI_ANIMATION_MARK);
	// 
	// 	CCS_GET_COMPONENT_FROM_SCENE(node, Layer, layer, UI_CITYVIEW_1);
	// 	CCS_GET_CHILD(layer, ImageView, img1, UI_IMAGEVIEW_1_1);
	// 	CCS_GET_CHILD(layer, ImageView, img2, UI_IMAGEVIEW_1_2);
	// 	CCS_GET_CHILD(layer, ImageView, img3, UI_IMAGEVIEW_1_3);
	// 	CCS_GET_CHILD(layer, ImageView, img5, UI_IMAGEVIEW_1_4);
	// 	CCS_GET_CHILD(layer, ImageView, img6, UI_IMAGEVIEW_1_5);
	// 	img1->setVisible(false);
	// 	img2->setVisible(false);
	// 	img3->setVisible(false);
	// 	img5->setVisible(false);
	// 	img6->setVisible(false);
	// 	std::vector<ImageView*> imgList;
	// 	imgList.push_back(img1);
	// 	imgList.push_back(img2);
	// 	imgList.push_back(img3);
	// 	imgList.push_back(ImageView::create());
	// 	imgList.push_back(img5);
	// 	imgList.push_back(img6);

	for (int i = 1; i <= 6; i++)
	{
		if (i == 4)
		{
			continue;
		}

		CCS_GET_ARMATURE_FROM_SCENE(node, Armature, mark, UI_ANIMATION_MARK + i);
		mark->getAnimation()->stop();
		mark->setVisible(false);
	}



	auto userData = GameData::getInstance()->getUserData();
	auto keys = userData->getTasks().keys();
	std::map<int, int> maps;
	for (int i = 1; i < 7; i++)
		maps[i] = -1;
	for (auto key : keys)
	{
		auto task = userData->getTasks().at(key);
		if (task->getInfo().state != 3)
		{
			if (task->getInfo().state == 0 && task->getInfo().city == userData->getInfo().city)
			{
				if (maps[task->getInfo().receivenpc] == 1)
					maps[task->getInfo().receivenpc] = 0;
				else if (maps[task->getInfo().receivenpc] < task->getInfo().state)
					maps[task->getInfo().receivenpc] = task->getInfo().state;
			}
			else if (task->getInfo().state == 1 && task->getInfo().deliveryCityID == userData->getInfo().city)
			{
				if (maps[task->getInfo().paynpc] == 0){}
				else if (maps[task->getInfo().paynpc] < task->getInfo().state)
					maps[task->getInfo().paynpc] = task->getInfo().state;
			}
			else if (task->getInfo().state == 2 && task->getInfo().deliveryCityID == userData->getInfo().city)
			{
				if (maps[task->getInfo().paynpc] < task->getInfo().state)
					maps[task->getInfo().paynpc] = task->getInfo().state;
			}
		}
	}

	for (auto map : maps)
	{
		if (map.second != -1)
		{

			if (map.second == 1)
			{
				// 			imgList[task.second->getInfo().paynpc - 1]->loadTexture("ss_task_uncomplettask.png", TextureResType::PLIST);
				// 			imgList[task.second->getInfo().paynpc - 1]->setVisible(true);
				CCS_GET_ARMATURE_FROM_SCENE(node, Armature, mark, UI_ANIMATION_MARK + map.first);
				mark->getAnimation()->play("Animation_Mark3");
				mark->setVisible(true);
			}
			else if (map.second == 0)
			{
				// 			imgList[task.second->getInfo().receivenpc - 1]->loadTexture("ss_task_newtask.png", TextureResType::PLIST);
				// 			imgList[task.second->getInfo().receivenpc - 1]->setVisible(true);
				CCS_GET_ARMATURE_FROM_SCENE(node, Armature, mark, UI_ANIMATION_MARK + map.first);
				mark->getAnimation()->play("Animation_Mark1");
				mark->setVisible(true);
			}
			else if (map.second == 2)
			{
				// 			imgList[task.second->getInfo().paynpc - 1]->loadTexture("ss_task_complettask.png", TextureResType::PLIST);
				// 			imgList[task.second->getInfo().paynpc - 1]->setVisible(true);
				CCS_GET_ARMATURE_FROM_SCENE(node, Armature, mark, UI_ANIMATION_MARK + map.first);
				mark->getAnimation()->play("Animation_Mark2");
				mark->setVisible(true);
			}
		}
	}

	//for (auto task : userData->getTasks())
	//{
	//	if (task.second->getInfo().state != 3)
	//	{
	//		if (task.second->getInfo().state == 0 && task.second->getInfo().city == userData->getInfo().city)
	//		{
	//			// 			imgList[task.second->getInfo().receivenpc - 1]->loadTexture("ss_task_newtask.png", TextureResType::PLIST);
	//			// 			imgList[task.second->getInfo().receivenpc - 1]->setVisible(true);
	//			CCS_GET_ARMATURE_FROM_SCENE(node, Armature, mark, UI_ANIMATION_MARK + task.second->getInfo().receivenpc);
	//			mark->getAnimation()->play("Animation_Mark1");
	//			mark->setVisible(true);
	//		}
	//		else if (task.second->getInfo().state == 1 && task.second->getInfo().deliveryCityID == userData->getInfo().city)
	//		{
	//			// 			imgList[task.second->getInfo().paynpc - 1]->loadTexture("ss_task_uncomplettask.png", TextureResType::PLIST);
	//			// 			imgList[task.second->getInfo().paynpc - 1]->setVisible(true);
	//			CCS_GET_ARMATURE_FROM_SCENE(node, Armature, mark, UI_ANIMATION_MARK + task.second->getInfo().paynpc);
	//			mark->getAnimation()->play("Animation_Mark3");
	//			mark->setVisible(true);
	//		}
	//		else if (task.second->getInfo().state == 2 && task.second->getInfo().deliveryCityID == userData->getInfo().city)
	//		{
	//			// 			imgList[task.second->getInfo().paynpc - 1]->loadTexture("ss_task_complettask.png", TextureResType::PLIST);
	//			// 			imgList[task.second->getInfo().paynpc - 1]->setVisible(true);
	//			CCS_GET_ARMATURE_FROM_SCENE(node, Armature, mark, UI_ANIMATION_MARK + task.second->getInfo().paynpc);
	//			mark->getAnimation()->play("Animation_Mark2");
	//			mark->setVisible(true);
	//		}
	//	}
	//}
}

void CityScene::onCloudClose()
{
	Director::getInstance()->replaceScene(MapScene::create());
}
