#include "BaseScene.h"
#include "../GameData.h"
#include "../Services/NewguidanceService.h"

USING_NS_CC;

bool BaseScene::init()
{
	if (!Layer::init())
	{
		return false;
	}

	this->setTag(SCENE);

// 	auto size = Director::getInstance()->getWinSizeInPixels();
// 
// 	auto scaleX = size.width / 960;
// 	auto scaleY = size.height / 640;
// 
// 	this->setScale(scaleX > scaleY ? scaleY : scaleX);
// 	CCLOG("[SOS] game scale: %f", this->getScale());
// 
// 	auto positionX = (size.width - 960) * 0.5;
// 	auto positionY = (size.height - 640) * 0.5;
// 
// 	//position = scaleX > scaleY ? Point(positionX, 0) : Point(0, positionY);
// 	this->setAnchorPoint(Point::ZERO);
// 	this->setPosition(Point(480, 0));

// 	this->setScale(GameData::getInstance()->scale);
// 	this->setPosition(GameData::getInstance()->position);

	return true;
}

void BaseScene::onEnter()
{
	this->scheduleOnce(schedule_selector(BaseScene::onEventGuidance), 1.0f / 60);

	Layer::onEnter();
}

void BaseScene::onEventGuidance(float dt)
{
	if (GameData::getInstance()->getUserData()->getInfo().state == UserData::StateType::GUIDER)
	{
		//Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_NEWGUIDANCE_START);
		NewguidanceService::getInstance()->createLayer(this);
	}
}