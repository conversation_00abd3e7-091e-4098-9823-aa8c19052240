//
//  TeamData.cpp
//  TestSOS
//
//  Created by <PERSON><PERSON> on 14-2-11.
//
//

#include "TeamData.h"

const std::string TeamPosition::EMPTY("0");

TeamData::TeamData()
{
	for (int i = 0; i < 9; i++)
	{
		_positions.push_back(TeamPosition::EMPTY);
	}
}

TeamData::~TeamData()
{

}

int TeamData::getPositionCount()
{
	auto count = 0;

	for (auto position : _positions)
	{
		if (position != TeamPosition::EMPTY)
		{
			count++;
		}
	}
	return count;
}