#ifndef __SOS_ALERT_VIEW__
#define __SOS_ALERT_VIEW__

#include "cocos2d.h"
#include "PopupView.h"
#include "ui/CocosGUI.h"

static const std::string EVENT_BUTTON_YES = "event_button_yes";
static const std::string EVENT_BUTTON_NO = "event_button_no";

enum class AlertType
{
	IDLE,
	LOADING,
	DIALOG,
	SETUP,
};

class AlertView : public PopupView
{
public:
	CREATE_FUNC(AlertView);

	static AlertView* create(std::string content, AlertType type, void* data = nullptr);
	static void createAndAdded(std::string content, AlertType type, void* data = nullptr);

public:
	AlertView(){};
	virtual ~AlertView();

	virtual bool init() override;
	virtual void onEnter() override;

	void initView();
	void touchButton(cocos2d::Ref* object, cocos2d::ui::TouchEventType type);
	void setTouchEnabled(bool enabled);
};

#endif