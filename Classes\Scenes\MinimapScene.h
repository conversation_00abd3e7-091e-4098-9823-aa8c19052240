#ifndef __SOS_MINIMAP_SCENE__
#define __SOS_MINIMAP_SCENE__

#include "cocos2d.h"
#include "BaseScene.h"
#include "ui/CocosGUI.h"
#include "cocostudio/CocoStudio.h"
#include "../SOSConfig.h"
#include "../Datas/TaskData.h"

class MinimapScene : public BaseScene
{
public:
	CREATE_FUNC(MinimapScene);
	static cocos2d::Scene* createScene();
	static cocos2d::Scene* createScene(TaskData* taskData);

public:
	MinimapScene();
	virtual ~MinimapScene();

	virtual bool init() override;
	virtual void update(float dt) override;
	void touchButton(cocos2d::Ref* object, cocos2d::ui::TouchEventType type);
};

#endif //__SOS_MINIMAP_SCENE__