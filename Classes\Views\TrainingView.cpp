#include "TrainingView.h"
#include "../Transactions/ShipTransaction.h"
#include "../Services/LocalizeService.h"
#include "AlertView.h"

USING_NS_CC;
USING_NS_CC_CCS; 
USING_NS_CC_UI;

enum tagTraining
{
	UI8_BUTTON_CLOSE = 1109,
	UI8_SCROLLVIEW = 1110,
	UI8_BUTTON_NORMAL = 1111,
	UI8_TEXT_NTIME =	1112,
	UI8_BUTTON_SUPER = 1113,
	UI8_TEXT_STIME = 1114,
	UI8_BUTTON_VIP = 1115,
	UI8_TEXT_VTIME = 1116,


	UI18_CHECKBOX = 702,
	UI18_IMAGEVIEW_HEAD = 704,
	UI18_TEXT_NAME = 705,
	UI18_TEXT_LEVEL = 706,
	UI18_TEXT_INFO = 707,

	LAYER1_IMAGEVIEW_COIN = 1291,
	LAYER1_TEXTBMFONT_PRICE = 1292,
	LAYER1_TEXTBMFONT_EXP = 1293,
	LAYER1_BUTTON_TRAIN = 1294,

	LAYER2_TEXTBMFONT_EXP = 1296,
	LAYER2_BUTTON_STOP = 1297,
	LAYER2_BUTTON_SPEED = 1298,


};

std::vector<CheckBox*> training_checkBoxList;

TrainingView::TrainingView()
{
	_trainType = 1;
}

TrainingView::~TrainingView()
{
	training_checkBoxList.clear();
}

Layer* TrainingView::createScene()
{
	auto layer = TrainingView::create();
	return layer;
}

bool TrainingView::init()
{
	if ( !PopupView::init() )
	{
		return false;
	}
	_autoClose = false;
	_training = CCS_CREATE_LAYER("UI_ship_8");
	addChild(_training);

	CCS_GET_CHILD(_training ,Button,closeBtn8 , UI8_BUTTON_CLOSE);
	closeBtn8->addTouchEventListener(this,toucheventselector(TrainingView::touchButton));
	CCS_GET_CHILD(_training,Button,nBtn,UI8_BUTTON_NORMAL);
	CCS_GET_CHILD(_training,Button,sBtn,UI8_BUTTON_SUPER);
	CCS_GET_CHILD(_training,Button,vBtn,UI8_BUTTON_VIP);
	nBtn->addTouchEventListener(this,toucheventselector(TrainingView::touchButton));
	sBtn->addTouchEventListener(this,toucheventselector(TrainingView::touchButton));
	vBtn->addTouchEventListener(this,toucheventselector(TrainingView::touchButton));
	CCS_GET_CHILD(_training,cocos2d::ui::ScrollView,scrollView,UI8_SCROLLVIEW);


	auto userData = GameData::getInstance()->getUserData();
	auto players = userData->getPlayers();
	int num = 0;
	int width = 0;
	for (auto player : players)
	{
		auto playerInfo = player->getInfo();
		if(playerInfo.appoint!=1&&playerInfo.type>4)
		{
			auto uiLayer = CCS_CREATE_LAYER("UI_Other_18");
			CCS_GET_CHILD(uiLayer,CheckBox,checkbox,UI18_CHECKBOX);
			checkbox->setUserObject(player);
			training_checkBoxList.push_back(checkbox);
			checkbox->addTouchEventListener(this,toucheventselector(TrainingView::touchButton)); 
			CCS_GET_CHILD(uiLayer,ImageView,head,UI18_IMAGEVIEW_HEAD);
			head->loadTexture(GET_PLAYER_HEAD2(playerInfo.type),TextureResType::PLIST);
			CCS_GET_CHILD(uiLayer,Text,name,UI18_TEXT_NAME);
			name->setText(playerInfo.name);
			CCS_GET_CHILD(uiLayer,Text,level,UI18_TEXT_LEVEL);
			level->setText(UTF8("Lv"+STRING(playerInfo.level)));
			CCS_GET_CHILD(uiLayer,Text,info,UI18_TEXT_INFO);
			info->setText(LocalizeService::getInstance()->getString(PLAYER_APPOINTMENT_STATE[playerInfo.appoint]));
			if(playerInfo.appoint!=0)
				info->setColor(Color3B(88,37,2));
			width = uiLayer->getContentSize().width;
			num++;
			scrollView->addChild(uiLayer);
		}
	}
	scrollView->setInnerContainerSize(Size(width*num, scrollView->getContentSize().height));

	_layer2 = CCS_CREATE_LAYER("UI_Other1_3");
	CCS_GET_CHILD(_layer2,Button,stopBtn,LAYER2_BUTTON_STOP);
	stopBtn->addTouchEventListener(this,toucheventselector(TrainingView::touchButton));
	CCS_GET_CHILD(_layer2,Button,speedBtn,LAYER2_BUTTON_SPEED);
	speedBtn->addTouchEventListener(this,toucheventselector(TrainingView::touchButton));
	CCS_GET_CHILD(_layer2,TextBMFont,exp2,LAYER2_TEXTBMFONT_EXP);
	_layer2->setVisible(false);
	addChild(_layer2);

	_layer1 = CCS_CREATE_LAYER("UI_Other1_2");
	CCS_GET_CHILD(_layer1,ImageView,coin,LAYER1_IMAGEVIEW_COIN);
	coin->setVisible(false);
	CCS_GET_CHILD(_layer1,TextBMFont,price,LAYER1_TEXTBMFONT_PRICE);
	price->setVisible(false);
	CCS_GET_CHILD(_layer1,TextBMFont,exp1,LAYER1_TEXTBMFONT_EXP);
	exp1->setVisible(false);
	CCS_GET_CHILD(_layer1,Button,trainBtn,LAYER1_BUTTON_TRAIN);
	trainBtn->addTouchEventListener(this,toucheventselector(TrainingView::touchButton));
	addChild(_layer1);
	return true;
}

void TrainingView::menuCloseCallback(Ref* pSender)
{
	Director::getInstance()->end();

#if (CC_TARGET_PLATFORM == CC_PLATFORM_IOS)
	exit(0);
#endif
}

void TrainingView::onEnter()
{
	Layer::onEnter();
}

void TrainingView::touchButton(Ref* obj, cocos2d::ui::TouchEventType eventType)
{
	auto userData = GameData::getInstance()->getUserData();
	auto widget = dynamic_cast<Widget*>(obj);
	int tag = widget->getTag();
	switch (eventType)
	{
	case TouchEventType::TOUCH_EVENT_BEGAN:
		break;
	case TouchEventType::TOUCH_EVENT_ENDED:
		if(tag==UI8_BUTTON_CLOSE)
		{
			this->removeFromParent();
		}
		else if(tag==UI8_BUTTON_NORMAL||tag==UI8_BUTTON_SUPER||tag==UI8_BUTTON_VIP)
		{
			for (auto item : training_checkBoxList)
			{
				if(item->getSelectedState())
				{
					auto playerInfo = dynamic_cast<PlayerData*>(item->getUserObject())->getInfo();
					auto configs = Configuration::getInstance()->getValue(PLAYER_TRAINING_CONFIG).asValueMap();
					auto config = configs[STRING(playerInfo.level)].asValueMap();
					if(playerInfo.appoint==5)
					{
						_layer1->setVisible(false);
						_layer2->setVisible(true);
					}
					else
					{
						_layer2->setVisible(false);
						_layer1->setVisible(true);
						CCS_GET_CHILD(_layer1,ImageView,coin,LAYER1_IMAGEVIEW_COIN);
						CCS_GET_CHILD(_layer1,TextBMFont,price,LAYER1_TEXTBMFONT_PRICE);
						CCS_GET_CHILD(_layer1,TextBMFont,exp1,LAYER1_TEXTBMFONT_EXP);
						coin->setVisible(true);
						price->setVisible(true);
						exp1->setVisible(true);
						if(tag==UI8_BUTTON_NORMAL)
						{
							if(config["OrdinaryTrainReqType"].asInt()==1)
							{
								coin->loadTexture("ss_ty_gold.png",TextureResType::PLIST);
								if(userData->getInfo().money<config["OrdinaryTrainReqValue"].asInt())
									price->setColor(Color3B(255,0,0));
								else
									price->setColor(Color3B(255,255,255));
							}
							else
							{
								coin->loadTexture("ss_ty_diamond.png",TextureResType::PLIST);
								if(userData->getInfo().diamond<config["OrdinaryTrainReqValue"].asInt())
									price->setColor(Color3B(255,0,0));
								else
									price->setColor(Color3B(255,255,255));
							}
							price->setText(CUTF8(STRING(config["OrdinaryTrainReqValue"].asInt())));
							//exp1->setText(UTF8("+"+STRING(config["OrdinaryTrainGet"].asInt())).c_str());
							_trainType = 1;
						}
						else if(tag==UI8_BUTTON_SUPER)
						{
							if(config["AdvancedTrainReqType"].asInt()==1)
							{
								coin->loadTexture("ss_ty_gold.png",TextureResType::PLIST);
								if(userData->getInfo().money<config["AdvancedTrainReqValue"].asInt())
									price->setColor(Color3B(255,0,0));
								else
									price->setColor(Color3B(255,255,255));
							}
							else
							{
								coin->loadTexture("ss_ty_diamond.png",TextureResType::PLIST);
								if(userData->getInfo().diamond<config["AdvancedTrainReqValue"].asInt())
									price->setColor(Color3B(255,0,0));
								else
									price->setColor(Color3B(255,255,255));
							}
							price->setText(CUTF8(STRING(config["AdvancedTrainReqValue"].asInt())));
							//exp1->setText(UTF8("+"+STRING(config["AdvancedTrainGet"].asInt())).c_str());
							_trainType = 2;
						}
						else if(tag==UI8_BUTTON_VIP)
						{
							if(config["BestTrainReqType"].asInt()==1)
							{
								coin->loadTexture("ss_ty_gold.png",TextureResType::PLIST);
								if(userData->getInfo().money<config["BestTrainReqValue"].asInt())
									price->setColor(Color3B(255,0,0));
								else
									price->setColor(Color3B(255,255,255));
							}
							else
							{
								coin->loadTexture("ss_ty_diamond.png",TextureResType::PLIST);
								if(userData->getInfo().diamond<config["BestTrainReqValue"].asInt())
									price->setColor(Color3B(255,0,0));
								else
									price->setColor(Color3B(255,255,255));
							}
							price->setText(CUTF8(STRING(config["BestTrainReqValue"].asInt())));
							//exp1->setText(UTF8("+"+STRING(config["BestTrainGet"].asInt())).c_str());
							_trainType = 3;
						}
					}
					break;
				}
			}
		}
		else if(tag==LAYER1_BUTTON_TRAIN)
		{
			bool b = true;
			for (auto item : training_checkBoxList)
			{
				if(item->getSelectedState())
				{
					b = true;
					_layer1->setVisible(false);
					_layer2->setVisible(true);

					CCS_GET_CHILD(_layer2,Button,speedBtn,LAYER2_BUTTON_SPEED);
					speedBtn->setTouchEnabled(true);
					CCS_GET_CHILD(_layer1,Button,trainBtn,LAYER1_BUTTON_TRAIN);
					trainBtn->setTouchEnabled(false);


					auto& playerInfo = dynamic_cast<PlayerData*>(item->getUserObject())->getInfo();
					playerInfo.appoint = 5;
					CCS_GET_CHILD(item->getParent(),Text,info,UI18_TEXT_INFO);
					info->setText(LocalizeService::getInstance()->getString(PLAYER_APPOINTMENT_STATE[playerInfo.appoint]));
					if(playerInfo.appoint!=0)
						info->setColor(Color3B(88,37,2));
					else
						info->setColor(Color3B(0,138,0));
					auto ships = userData->getShips();
					for (auto item :ships)
					{
						auto ship = item.second->getInfo();
						if (ship.use)
						{
							ShipTransaction::getInstance()->playerTraining(userData->getInfo().uid,playerInfo.id,_trainType,1,ship.id);
							break;
						}
					}
					break;
				}
				else
				{
					b = false;
				}
			}
			if(!b)
				//MessageBox(LocalizeService::getInstance()->getString("4014").c_str(), "Tips");
				this->addChild(AlertView::create(LocalizeService::getInstance()->getString("4014"), AlertType::IDLE), 999999);
		}
		else if(tag==LAYER2_BUTTON_STOP||tag == LAYER2_BUTTON_SPEED)
		{
			_layer2->setVisible(false);
			_layer1->setVisible(true);

			CCS_GET_CHILD(_layer2,Button,speedBtn,LAYER2_BUTTON_SPEED);
			speedBtn->setTouchEnabled(false);
			CCS_GET_CHILD(_layer1,Button,trainBtn,LAYER1_BUTTON_TRAIN);
			trainBtn->setTouchEnabled(true);

			int type;
			if(tag==LAYER2_BUTTON_STOP)
				type = 2;
			else
				type = 3;
			for (auto item : training_checkBoxList)
			{
				if(item->getSelectedState())
				{

					auto& playerInfo = dynamic_cast<PlayerData*>(item->getUserObject())->getInfo();
					playerInfo.appoint = type == 3?5:0;
					CCS_GET_CHILD(item->getParent(),Text,info,UI18_TEXT_INFO);
					info->setText(LocalizeService::getInstance()->getString(PLAYER_APPOINTMENT_STATE[playerInfo.appoint]));
					if(playerInfo.appoint!=0)
						info->setColor(Color3B(88,37,2));
					else
						info->setColor(Color3B(0,138,0));
					auto ships = userData->getShips();
					for (auto item :ships)
					{
						auto ship = item.second->getInfo();
						if(ship.use)
						{
							ShipTransaction::getInstance()->playerTraining(userData->getInfo().uid,playerInfo.id,_trainType,type,ship.id);
							break;
						}
					}
					break;
				}
			}
		}

		else
		{
			for (auto item : training_checkBoxList)
			{
				item->setSelectedState(false);
			}
			auto checkbox = dynamic_cast<CheckBox*>(widget);
			auto playerInfo = dynamic_cast<PlayerData*>(checkbox->getUserObject())->getInfo();

			auto configs = Configuration::getInstance()->getValue(PLAYER_TRAINING_CONFIG).asValueMap();
			auto config = configs[STRING(playerInfo.level)].asValueMap();
			CCS_GET_CHILD(_training ,Text,nText , UI8_TEXT_NTIME);
			nText->setText(UTF8(STRING(config["OrdinaryTrainReqTime"].asInt())));
			CCS_GET_CHILD(_training ,Text,sText , UI8_TEXT_STIME);
			sText->setText(UTF8(STRING(config["AdvancedTrainTime"].asInt())));
			CCS_GET_CHILD(_training ,Text,vText , UI8_TEXT_VTIME);
			vText->setText(UTF8(STRING(config["BestTrainReqTime"].asInt())));

			if(playerInfo.appoint==5)
			{
				_layer1->setVisible(false);
				_layer2->setVisible(true);

				CCS_GET_CHILD(_layer2,Button,speedBtn,LAYER2_BUTTON_SPEED);
				speedBtn->setTouchEnabled(true);
				CCS_GET_CHILD(_layer1,Button,trainBtn,LAYER1_BUTTON_TRAIN);
				trainBtn->setTouchEnabled(false);


				CCS_GET_CHILD(_layer2,TextBMFont,exp2,LAYER2_TEXTBMFONT_EXP);
				switch(_trainType)
				{
				case 1:
					//exp2->setText(UTF8("+"+STRING(config["OrdinaryTrainGet"].asInt())).c_str());
					break;
				case 2:
					//exp2->setText(UTF8("+"+STRING(config["AdvancedTrainGet"].asInt())).c_str());
					break;
				case 3:
					//exp2->setText(UTF8("+"+STRING(config["BestTrainGet"].asInt())).c_str());
					break;
				}
			}
			else if(playerInfo.appoint!=5&&playerInfo.appoint!=1)
			{
				_layer2->setVisible(false);
				_layer1->setVisible(true);

				CCS_GET_CHILD(_layer2,Button,speedBtn,LAYER2_BUTTON_SPEED);
				speedBtn->setTouchEnabled(false);
				CCS_GET_CHILD(_layer1,Button,trainBtn,LAYER1_BUTTON_TRAIN);
				trainBtn->setTouchEnabled(true);		

				CCS_GET_CHILD(_layer1,ImageView,coin,LAYER1_IMAGEVIEW_COIN);
				CCS_GET_CHILD(_layer1,TextBMFont,price,LAYER1_TEXTBMFONT_PRICE);
				CCS_GET_CHILD(_layer1,TextBMFont,exp1,LAYER1_TEXTBMFONT_EXP);

				coin->setVisible(true);
				price->setVisible(true);
				exp1->setVisible(true);

				if(config["OrdinaryTrainReqType"].asInt()==1)
				{
					coin->loadTexture("ss_ty_gold.png",TextureResType::PLIST);
					if(userData->getInfo().money<config["OrdinaryTrainReqValue"].asInt())
						price->setColor(Color3B(255,0,0));
					else
						price->setColor(Color3B(255,255,255));
				}
				else
				{
					coin->loadTexture("ss_ty_diamond.png",TextureResType::PLIST);
					if(userData->getInfo().diamond<config["OrdinaryTrainReqValue"].asInt())
						price->setColor(Color3B(255,0,0));
					else
						price->setColor(Color3B(255,255,255));
				}

				price->setText(CUTF8(STRING(config["OrdinaryTrainReqValue"].asInt())));
				//exp1->setText(UTF8("+"+STRING(config["OrdinaryTrainGet"].asInt())).c_str());
			}
		}
		break;
	}
}
