#include "MapTipsView.h"
#include "../GameData.h"
#include "../Services/LocalizeService.h"

USING_NS_CC;
USING_NS_CC_CCS;
USING_NS_CC_UI;

int cityid = 0;

enum tagMapTips
{
	UI_TEXT_CITYNAME = 20051,
	UI_PANEL_ITEMS2 = 20053,
	UI_PANEL_ITEMS1 = 20530,
	UI_PANEL_COPIES = 20054,

	UI_BUTTON_CLOSE = 21001,
};

MapTipsView::MapTipsView()
{

}

MapTipsView::~MapTipsView()
{
	cityid = 0;
}

Layer* MapTipsView::createScene(int cityID)
{
	cityid = cityID;
	auto layer = MapTipsView::create();
	return layer;
}

bool MapTipsView::init()
{
	if (!PopupView::init())
	{
		return false;
	}

	auto ui = CCS_CREATE_LAYER("UI_Tips_1");
	this->setCenter(ui);
	this->addChild(ui);

	CCS_GET_CHILD(ui, Layout, cargoLayer1, UI_PANEL_ITEMS1);
	CCS_GET_CHILD(ui, Layout, cargoLayer2, UI_PANEL_ITEMS2);
	CCS_GET_CHILD(ui, Layout, copieLayer, UI_PANEL_COPIES);
	CCS_GET_CHILD(ui, Text, cityName, UI_TEXT_CITYNAME);
	CCS_GET_CHILD(ui, Button, btnClose, UI_BUTTON_CLOSE);
	btnClose->setPressedActionEnabled(true);
	btnClose->addTouchEventListener(this, toucheventselector(MapTipsView::touchButton));
	auto cityData = GameData::getInstance()->getCity(cityid);
	cityName->setText(LocalizeService::getInstance()->getString(cityData->getInfo().name));

	int iCopie = 1;
	for (auto copie : cityData->getCopies())
	{
		auto textBMFont = TextBMFont::create(LocalizeService::getInstance()->getString(copie.second->getInfo().name) + copie.second->getInfo().suitLevel, "Fonts/Price_Font.fnt");
		textBMFont->setAnchorPoint(Point(0, 0));
		textBMFont->setPosition(Point(0, copieLayer->getContentSize().height - iCopie * textBMFont->getContentSize().height - 5));
		copieLayer->addChild(textBMFont);
		iCopie++;
	}
	int iCargo = 0;
	for (auto cargo : cityData->getCargos())
	{
		if (cargo.buy > 0)
		{
			auto img = ImageView::create(GET_CARGO_ICON(cargo.mid), TextureResType::PLIST);
			img->setAnchorPoint(Point(0, 0));
			img->setScale(0.4f);
			/*if (iCargo % 3 == 0)
			{
			row++;
			img->setPosition(Point(0, row * img->getContentSize().height*0.4f + 20));
			}
			else
			{
			img->setPosition(Point(iCargo % 3 * img->getContentSize().width*0.4f, row * img->getContentSize().height*0.4f + 20));
			}*/
			img->setPosition(Point(iCargo % 3 * img->getContentSize().width*0.4f + 10, 0));
			if (iCargo < 3)
				cargoLayer1->addChild(img);
			else
				cargoLayer2->addChild(img);

			iCargo++;
		}
	}

	return true;
}


void MapTipsView::menuCloseCallback(Ref* pSender)
{
	Director::getInstance()->end();

#if (CC_TARGET_PLATFORM == CC_PLATFORM_IOS)
	exit(0);
#endif
}

void MapTipsView::touchButton(Ref* object, TouchEventType type)
{
	auto button = static_cast<Widget*>(object);

	switch (type)
	{
	case TouchEventType::TOUCH_EVENT_ENDED:
		if (button->getTag() == UI_BUTTON_CLOSE)
		{
			this->removeFromParent();
		}
		break;
	default:
		break;
	}
}
