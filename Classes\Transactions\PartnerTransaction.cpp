﻿//
//  PartnerTransaction.cpp
//  TestSOS
//
//  Created by <PERSON><PERSON> on 13-12-21.
//
//

#include "PartnerTransaction.h"
#include "../Services/HTTPService.h"


USING_NS_CC;
USING_STD;

static const char* TRANS_GAME_PLAYER_GET = "Game_Player_Get.ashx";
static const char* TRANS_GAME_EQUIP_PUT_ON = "Game_Equip_PutOn.ashx";
static const char* TRANS_GAME_EQUIP_PUT_DOWN = "Game_Equip_PutDown.ashx";
static const char* TRANS_GAME_MAGIC_INFO_GET = "Game_MagicInfo_Get.ashx";
static const char* TRANS_GAME_MAGIC_USE = "Game_Magic_Use.ashx";
static const char* TRANS_GAME_MAGIC_UPGRADE = "Game_Magic_Upgrade.ashx";
static const char* TRANS_GAME_PLAYER_LEAVE = "Game_Player_Leave.ashx";
static const char* TRANS_GAME_PLAYER_GET_ALL = "Game_Player_GetAll.ashx";
static const char* TRANS_GAME_EQUIP_INFO_GET = "Game_EquipInfo_Get.ashx";
static const char* TRANS_GAME_PROPS_INFO_GET = "Game_PropsInfo_Get.ashx";
static const char* TRANS_GAME_EQUIP_VIEW_UPDATE = "Game_EquipView_Update.ashx";
static const char* TRANS_GAME_PASSIVE_SKILLS_GET = "Game_PassiveSkills_Get.ashx";
static const char* TRANS_GAME_PASSIVE_SKILLS_CHANGE = "Game_PassiveSkills_Change.ashx";
static const char* TRANS_GAME_REFRESH_CDTIME = "Game_Refresh_CDTime.ashx";

FUNC_GET_INSTANCE(PartnerTransaction);

PartnerTransaction::PartnerTransaction()
{
	Director::getInstance()->getEventDispatcher()->addCustomEventListener(TRANS_GAME_PLAYER_LEAVE, [=](EventCustom* event) {
		Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_PARTNER_LEAVE_SUCCESS, NULL);
	});
	Director::getInstance()->getEventDispatcher()->addCustomEventListener(TRANS_GAME_PASSIVE_SKILLS_GET, [=](EventCustom* event) {
		auto data = static_cast<HTTPData *>(event->getUserData());
		std::string onlyID = data->readString("OnlyID");
		Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_PASSIVE_SKILLS_GET_SUCCESS,  &onlyID);
	});

	Director::getInstance()->getEventDispatcher()->addCustomEventListener(TRANS_GAME_PASSIVE_SKILLS_CHANGE, [=](EventCustom* event) {
		Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_PASSIVE_SKILLS_CHANGE_SUCCESS, NULL);
	});
}

PartnerTransaction::~PartnerTransaction()
{
	Director::getInstance()->getEventDispatcher()->removeCustomEventListeners(TRANS_GAME_PLAYER_GET);
	Director::getInstance()->getEventDispatcher()->removeCustomEventListeners(TRANS_GAME_PLAYER_LEAVE);
	Director::getInstance()->getEventDispatcher()->removeCustomEventListeners(TRANS_GAME_PASSIVE_SKILLS_GET);
	Director::getInstance()->getEventDispatcher()->removeCustomEventListeners(TRANS_GAME_PASSIVE_SKILLS_CHANGE);
}

void PartnerTransaction::getPlayerInfo(string uid, string onlyID)
{
	auto data = HTTPData::create();
	data->write("UID", uid.c_str());
	data->write("OnlyID", onlyID.c_str());
	auto url = WEB_HOST + TRANS_GAME_PLAYER_GET;
	HTTPService::getInstance()->request(url.c_str(), data, TRANS_GAME_PLAYER_GET);
}

void PartnerTransaction::getPlayerInfoAll(string uid)
{

}

//type:0:离队 1:召回
void PartnerTransaction::leavePlayer(string uid, string onlyID, int type)
{
	auto data = HTTPData::create();
	data->write("UID", uid.c_str());
	data->write("OnlyID", onlyID.c_str());
	data->write("Type", type);

	auto url = WEB_HOST + TRANS_GAME_PLAYER_LEAVE;
	HTTPService::getInstance()->request(url.c_str(), data,TRANS_GAME_PLAYER_LEAVE);
}

void PartnerTransaction::getMagicInfo(string uid, string onlyID)
{
	auto data = HTTPData::create();
	data->write("UID", uid.c_str());
	data->write("OnlyID", onlyID.c_str());

	auto url = WEB_HOST + TRANS_GAME_MAGIC_INFO_GET;
	HTTPService::getInstance()->request(url.c_str(), data, TRANS_GAME_MAGIC_INFO_GET);
}

void PartnerTransaction::useMagic(string uid, string onlyID, int magicID)
{
	auto data = HTTPData::create();
	data->write("UID", uid.c_str());
	data->write("OnlyID", onlyID.c_str());
	data->write("MagicID", magicID);

	auto url = WEB_HOST + TRANS_GAME_MAGIC_USE;
	HTTPService::getInstance()->request(url.c_str(), data);
}

void PartnerTransaction::upgradeMagic(string uid, string onlyID, int magicID)
{
	auto data = HTTPData::create();
	data->write("UID", uid.c_str());
	data->write("OnlyID", onlyID.c_str());
	data->write("MagicID", magicID);

	auto url = WEB_HOST + TRANS_GAME_MAGIC_UPGRADE;
	HTTPService::getInstance()->request(url.c_str(), data,TRANS_GAME_MAGIC_UPGRADE);
}

void PartnerTransaction::getPassiveSkills(std::string uid)
{
	auto data = HTTPData::create();
	data->write("UID",uid.c_str());

	auto url = WEB_HOST + TRANS_GAME_PASSIVE_SKILLS_GET;
	HTTPService::getInstance()->request(url.c_str(), data,TRANS_GAME_PASSIVE_SKILLS_GET);
}

void PartnerTransaction::changeSkill(string uid, string onlyID)
{
	auto data = HTTPData::create();
	data->write("UID",uid.c_str());
	data->write("OnlyID", onlyID.c_str());

	auto url = WEB_HOST + TRANS_GAME_PASSIVE_SKILLS_CHANGE;
	HTTPService::getInstance()->request(url.c_str(), data,TRANS_GAME_PASSIVE_SKILLS_CHANGE);
}

void PartnerTransaction::refreshCDTime(int type)
{
	auto data = HTTPData::create();
	data->write("Type", type);

	auto url = WEB_HOST + TRANS_GAME_REFRESH_CDTIME;
	HTTPService::getInstance()->request(url.c_str(), data, TRANS_GAME_REFRESH_CDTIME);
}

