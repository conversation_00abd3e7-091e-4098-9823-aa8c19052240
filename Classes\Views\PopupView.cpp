#include "PopupView.h"
#include "ui/CocosGUI.h"
#include "../SOSConfig.h"
#include "../GameData.h"
#include "../Services/NewguidanceService.h"

USING_NS_CC;
USING_NS_CC_UI;

bool PopupView::init()
{
	if (!Layer::init())
	{
		return false;
	}

	auto size = Director::getInstance()->getWinSize();
	auto layer = LayerColor::create(Color4B(0, 0, 0, 200), size.width, size.height);
	this->addChild(layer);

	auto listener = EventListenerTouchOneByOne::create();
	listener->setSwallowTouches(true);
	listener->onTouchBegan = [=](Touch *touch, Event *event) {
		return true;
	};
	listener->onTouchMoved = [](Touch *touch, Event *event) {
		
	};
	listener->onTouchEnded = [&](Touch *touch, Event *event) {
		if (_autoClose) this->removeFromParent();
	};

	this->getEventDispatcher()->addEventListenerWithSceneGraphPriority(listener, layer);

	return true;
}

void PopupView::onEnter()
{
	this->scheduleOnce(schedule_selector(PopupView::onEventGuidance), 1.0f / 60);

	Layer::onEnter();
}

void PopupView::onEventGuidance(float dt)
{
	if (GameData::getInstance()->getUserData()->getInfo().state == UserData::StateType::GUIDER)
	{
		Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_NEWGUIDANCE_START);
	}
}

void PopupView::setCenter()
{
	this->setCenter(this);
}

void PopupView::setCenter(cocos2d::Node* node)
{
	auto size = Director::getInstance()->getWinSize();
	node->setPosition(Point((size.width - 960) / 2, (size.height - 640) / 2));
}