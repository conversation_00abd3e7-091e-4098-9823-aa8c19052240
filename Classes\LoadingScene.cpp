#include "LoadingScene.h"
#include "Services/HTTPService.h"
#include "Transactions/LoginTransaction.h"
#include "Transactions/FormationTransaction.h"
#include "Transactions/DockTransaction.h"
#include "Transactions/PartnerTransaction.h"
#include "GameData.h"
#include "Scenes/CityScene.h"
#include "MapScene.h"
#include "Services/LocalizeService.h"
#include "Scenes/LoadingResourceScene.h"
#include "Scenes/StartScene.h"

USING_NS_CC;
USING_NS_CC_CCS;
USING_NS_CC_UI;

enum tagLoading
{
	UI_LOADING_1 = 10004,
	UI_LOADING_2 = 10005,
	UI_LOADINGBAR = 4,
	UI_TEXT = 5,
	UI_TEXT_TIPS = 7,
	UI_ARMATURE_SHIP = 10007,
};

int g_tips = 2001;
int	r_uiCount = 0;
float r_ui = 0;
LoadingScene::SceneType r_sceneType;
std::vector<std::string> r_loadings;

Scene* LoadingScene::createScene()
{
	auto scene = Scene::create();

	auto layer = LoadingScene::create();
	scene->addChild(layer);

	return scene;
}

Scene* LoadingScene::createScene(SceneType sceneType)
{
	r_sceneType = sceneType;
	return LoadingScene::createScene();
}

Scene* LoadingScene::createScene(SceneType sceneType, std::vector<std::string> loadings)
{
	r_loadings = loadings;
	return LoadingScene::createScene(sceneType);
}

LoadingScene::LoadingScene()
: _processType(LoadingProcessType::SHIPS)
{

}

LoadingScene::~LoadingScene()
{
	Director::getInstance()->getTextureCache()->removeTextureForKey("UI_Loading/UI_Loading.png");
	SpriteFrameCache::getInstance()->removeSpriteFramesFromFile("UI_Loading/UI_Loading.plist");

	r_loadings.clear();
}

bool LoadingScene::init()
{
	if (!BaseScene::init())
	{
		return false;
	}

	node = CCS_CREATE_SCENE("Scene_Loading");
	this->addChild(node);

	CCS_GET_COMPONENT_FROM_SCENE(node, Layer, layer, UI_LOADING_1);
	CCS_GET_CHILD(layer, LoadingBar, loading, UI_LOADINGBAR);
	CCS_GET_CHILD(layer, Text, txt, UI_TEXT);

	loading->setPercent(0);
	txt->setText("0%");

	for (auto name : r_loadings)
	{
		r_uiCount++;
		Director::getInstance()->getTextureCache()->addImageAsync(name, CC_CALLBACK_1(LoadingScene::loadingCallBack, this));
	}

	return true;
}

void LoadingScene::onEnter()
{
	Layer::onEnter();
}

void LoadingScene::start(float dt)
{
	switch (r_sceneType)
	{
	case LoadingScene::SceneType::CITY:
		Director::getInstance()->replaceScene(LoadingResourceScene::createScene(false));
		break;
	case LoadingScene::SceneType::MAP:
		Director::getInstance()->replaceScene(MapScene::create());
		break;
	case LoadingScene::SceneType::START:
		Director::getInstance()->replaceScene(StartScene::createScene());
		break;
	default:
		Director::getInstance()->replaceScene(MapScene::create());
		break;
	}
}

void LoadingScene::update(float dt)
{
	switch (_processType)
	{
	case LoadingProcessType::SHIPS:
		CCLOG("start load ships animation ...");
		for (int i = 1; i <= SHIP_COUNT; i++)
		{
			ArmatureDataManager::getInstance()->addArmatureFileInfoAsync(GET_SHIP_FILE(i), this, schedule_selector(LoadingScene::running));
		}
		_processType = LoadingProcessType::SHIPS_LOADING;
		showProcess(_processType);
		break;
	case LoadingProcessType::PLAYERS:
		CCLOG("start load players animation ...");
		for (auto playerFile : GAME_PLAYERS_ARMATURES)
		{
			ArmatureDataManager::getInstance()->addArmatureFileInfoAsync(playerFile, this, schedule_selector(LoadingScene::running));
		}
		_processType = LoadingProcessType::PLAYERS_LOADING;
		showProcess(_processType);
		break;
	case LoadingProcessType::ANIMATIONS:
		CCLOG("start load skill animation ...");
		for (auto skillFile : GAME_SKILLS_ARMATURES)
		{
			ArmatureDataManager::getInstance()->addArmatureFileInfoAsync(skillFile, this, schedule_selector(LoadingScene::running));
		}
		_processType = LoadingProcessType::ANIMATIONS_LOADING;
		showProcess(_processType);
	default:
		break;
	}
}

void LoadingScene::updateTips(float dt)
{
	CCS_GET_COMPONENT_FROM_SCENE(node, Layer, layer, UI_LOADING_2);
	CCS_GET_CHILD(layer, Text, tipsLabel, UI_TEXT_TIPS);

	tipsLabel->setText("Tips: " + LocalizeService::getInstance()->getString(STRING(g_tips)));
	g_tips++;
	if (g_tips > 2005)
	{
		g_tips = 2001;
	}
}

void LoadingScene::running(float percent)
{
	showProcess(100 * percent);
	if (percent >= 1.0f)
	{
		_processType = LoadingProcessType::FINISH;
	}
}

void LoadingScene::showProcess(int current)
{
	auto child = node->getChildByTag(UI_LOADING_1);
	auto reader = (ComRender*)child->getComponent("GUIComponent");
	auto layer = (Layer*)reader->getNode();
	auto loading = dynamic_cast<LoadingBar*>(layer->getChildByTag(UI_LOADINGBAR));
	loading->setPercent(current);
	auto ArmatureShip = node->getChildByTag(UI_ARMATURE_SHIP);
	auto arm = ArmatureShip->getChildren();
	for (auto item : arm)
	{
		auto a = dynamic_cast<Armature*>(item);
		a->setPositionX(current*(loading->getContentSize().width / 100));
	}
	auto txt = dynamic_cast<Text*>(layer->getChildByTag(UI_TEXT));
	txt->setText(STRING(current) + "%");

	if (current >= LoadingProcessType::FINISH)
	{
		txt->setText(UTF8(LocalizeService::getInstance()->getString("632")));

		switch (r_sceneType)
		{
		case LoadingScene::SceneType::CITY:
			Director::getInstance()->replaceScene(LoadingResourceScene::createScene());
			break;
		case LoadingScene::SceneType::MAP:
			Director::getInstance()->replaceScene(MapScene::create());
			break;
		case LoadingScene::SceneType::START:
			Director::getInstance()->replaceScene(StartScene::createScene());
			break;
		default:
			Director::getInstance()->replaceScene(MapScene::create());
			break;
		}
	}
}

void LoadingScene::loadingCallBack(Texture2D *texture)
{
	r_ui++;

	int current = float(r_ui / r_uiCount) * 100;

	CCLOG("current percent: %d", current);

	CCS_GET_COMPONENT_FROM_SCENE(node, Layer, layer, UI_LOADING_1);
	CCS_GET_CHILD(layer, LoadingBar, loadingBar, UI_LOADINGBAR);
	loadingBar->setPercent(current);

	CCS_GET_ARMATURE_FROM_SCENE(node, Armature, ship, UI_ARMATURE_SHIP);
	ship->setPositionX(current*(loadingBar->getContentSize().width / 100));

	CCS_GET_CHILD(layer, Text, txt, UI_TEXT);
	txt->setText(STRING(current) + "%");

	if (r_ui >= r_uiCount)
	{
		txt->setText(UTF8(LocalizeService::getInstance()->getString("632")));

		this->scheduleOnce(schedule_selector(LoadingScene::start), 1.0f / 60);
	}
}