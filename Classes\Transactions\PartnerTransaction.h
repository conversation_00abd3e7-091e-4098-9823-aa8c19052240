//
//  PartnerTransaction.h
//  TestSOS
//
//  Created by <PERSON><PERSON> on 13-12-21.
//
//

#ifndef __TestSOS__PartnerTransaction__
#define __TestSOS__PartnerTransaction__

#include "cocos2d.h"
#include "../SOSConfig.h"
#include "../GameData.h"

static const char* EVENT_PLAYERGET_SUCCESS = "event_palyerget_success";
static const char* EVENT_PARTNER_LEAVE_SUCCESS = "event_partner_leave";
static const char* EVENT_PARTNER_MAGICUPGRADE_SUCCESS = "event_partner_magicupgrade";
static const char* EVENT_PASSIVE_SKILLS_GET_SUCCESS = "event_passive_skill_get";
static const char* EVENT_PASSIVE_SKILLS_CHANGE_SUCCESS = "event_passive_skill_change";

class PlayerEquipInfo;

class PartnerTransaction : public cocos2d::Ref
{
public:
	FUNC_INSTANCE(PartnerTransaction);

public:
    PartnerTransaction();
    virtual ~PartnerTransaction();

    void getPlayerInfo(std::string uid, std::string onlyID);
    
    void getPlayerInfoAll(std::string uid);
    
    void leavePlayer(std::string uid, std::string onlyID, int type);
    
    void getMagicInfo(std::string uid, std::string onlyID);
    
    void useMagic(std::string uid, std::string onlyID, int magicID);
    void upgradeMagic(std::string uid, std::string onlyID, int magicID);
    
    void getPassiveSkills(std::string uid);
    
	void changeSkill(std::string uid, std::string onlyID);
	
	void refreshCDTime(int type);
};

#endif /* defined(__TestSOS__PartnerTransaction__) */
