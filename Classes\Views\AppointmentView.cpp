#include "AppointmentView.h"
#include "../Services/LocalizeService.h"
#include "../Services/NewguidanceService.h"
#include "../Scenes/BaseScene.h"

USING_NS_CC;
USING_NS_CC_CCS;
USING_NS_CC_UI;

enum tagAppointment
{
	UI_APPOINTMENT = 10005,
	UI_OTHER_13 = 10006,
	UI_OTHER_14 = 10007,
	UI_OTHER_12 = 10008,

	UI_BUTTON_CLOSE = 956,
	UI_BUTTON_AUTOAPP = 957,
	UI_SCROLLVIEW = 960,

	UI_OTHER1_TEXT1 = 1284,
	UI_OTHER1_TEXT2 = 1285,
	UI_OTHER1_TEXT3 = 1286,
	UI_OTHER1_TEXT4 = 1287,
	UI_OTHER1_TEXT5 = 1288,
	UI_OTHER1_CHECKBOX = 1289,

	UI_OTHER13_LEVEL = 681,
	UI_OTHER13_CHECKBOX = 677,
	UI_OTHER14_LEVEL = 686,
	UI_OTHER14_CHECKBOX = 682,
	UI_OTHER12_LEVEL = 676,
	UI_OTHER12_CHECKBOX = 672,

};

std::vector<Widget*> checkboxList;

AppointmentView::AppointmentView()
{

}

AppointmentView::~AppointmentView()
{
	checkboxList.clear();
}

Layer* AppointmentView::createScene()
{
	auto layer = AppointmentView::create();
	return layer;
}

bool AppointmentView::init()
{
	if (!PopupView::init())
	{
		return false;
	}

	_autoClose = false;

	node = CCS_CREATE_SCENE("Scene_Ship_1");
	node->setTag(TagBaseScene::SHIP_APPOINT_VIEW);
	addChild(node);

	CCS_GET_COMPONENT_FROM_SCENE(node, Layer, layer, UI_APPOINTMENT);
	CCS_GET_CHILD(layer, Button, closeBtn, UI_BUTTON_CLOSE);
	closeBtn->addTouchEventListener(this, toucheventselector(AppointmentView::touchButton));
	CCS_GET_CHILD(layer, Button, autoBtn, UI_BUTTON_AUTOAPP);
	autoBtn->setPressedActionEnabled(true);
	autoBtn->addTouchEventListener(this, toucheventselector(AppointmentView::touchButton));

	//CCS_GET_COMPONENT_FROM_SCENE(node,Layer,captainLayer,UI_OTHER_13);
	CCS_GET_CHILD(node, Node, cLayer, UI_OTHER_13);
	CCS_GET_COMPONENT(cLayer, Layer, captainLayer);
	CCS_GET_CHILD(captainLayer, CheckBox, captainBtn, UI_OTHER13_CHECKBOX);
	_captainBtn = captainBtn;
	_captainBtn->addTouchEventListener(this, toucheventselector(AppointmentView::touchButton));
	_captainBtn->setTouchEnabled(false);
	_captainBtn->setSelectedState(true);
	//CCS_GET_COMPONENT_FROM_SCENE(node,Layer,businesslayer,UI_OTHER_14);
	CCS_GET_CHILD(node, Node, bLayer, UI_OTHER_14);
	CCS_GET_COMPONENT(bLayer, Layer, businesslayer);
	CCS_GET_CHILD(businesslayer, CheckBox, operateBtn, UI_OTHER14_CHECKBOX);
	_operateBtn = operateBtn;
	_operateBtn->addTouchEventListener(this, toucheventselector(AppointmentView::touchButton));
	_operateBtn->setTouchEnabled(false);
	//CCS_GET_COMPONENT_FROM_SCENE(node,Layer,sailLayer,UI_OTHER_12);
	CCS_GET_CHILD(node, Node, sLayer, UI_OTHER_12);
	CCS_GET_COMPONENT(sLayer, Layer, sailLayer);
	CCS_GET_CHILD(sailLayer, CheckBox, navigationBtn, UI_OTHER12_CHECKBOX);
	_navigationBtn = navigationBtn;
	_navigationBtn->addTouchEventListener(this, toucheventselector(AppointmentView::touchButton));
	_navigationBtn->setTouchEnabled(false);


	auto userData = GameData::getInstance()->getUserData();
	auto ships = userData->getShips();
	for (auto ship : ships)
	{
		if (ship.second->getInfo().use)
		{

			auto cabs = ship.second->getCabins();
			for (auto cab : cabs)
			{
				if (cab->getInfo().type == CabinData::CabinType::CAPTAIN_HOUSE)
				{
					CCS_GET_CHILD(captainLayer, TextBMFont, captainLevel, UI_OTHER13_LEVEL);
					captainLevel->setText(UTF8("Lv" + STRING(cab->getInfo().level)).c_str());
					cLayer->setVisible(true);
					//_captainBtn->setTouchEnabled(true);
				}
				else if (cab->getInfo().type == CabinData::CabinType::OPERATE_HOUSE)
				{
					CCS_GET_CHILD(businesslayer, TextBMFont, operateLevel, UI_OTHER14_LEVEL);
					operateLevel->setText(UTF8("Lv" + STRING(cab->getInfo().level)).c_str());
					bLayer->setVisible(true);
					_operateBtn->setTouchEnabled(true);
				}
				else if (cab->getInfo().type == CabinData::CabinType::NAVIGATION_HOUSE)
				{
					CCS_GET_CHILD(sailLayer, TextBMFont, navigationLevel, UI_OTHER12_LEVEL);
					navigationLevel->setText(UTF8("Lv" + STRING(cab->getInfo().level)).c_str());
					sLayer->setVisible(true);
					_navigationBtn->setTouchEnabled(true);
				}
			}
			break;
		}
	}

	loadScroolView();
	return true;
}

void AppointmentView::touchButton(Ref* obj, cocos2d::ui::TouchEventType eventType)
{
	auto widget = dynamic_cast<Widget*>(obj);
	int tag = widget->getTag();

	CCS_GET_CHILD(node, Node, sLayer, UI_OTHER_12);
	CCS_GET_COMPONENT(sLayer, Layer, sailLayer);
	CCS_GET_CHILD(node, Node, cLayer, UI_OTHER_13);
	CCS_GET_COMPONENT(cLayer, Layer, captainLayer);
	CCS_GET_CHILD(node, Node, bLayer, UI_OTHER_14);
	CCS_GET_COMPONENT(bLayer, Layer, businesslayer);

	switch (eventType)
	{
	case TouchEventType::TOUCH_EVENT_BEGAN:
		break;
	case TouchEventType::TOUCH_EVENT_ENDED:
		if (tag == UI_OTHER13_CHECKBOX)
		{
			_captainBtn->setTouchEnabled(false);
			if (sLayer->isVisible())
			{
				_navigationBtn->setSelectedState(false);
				_navigationBtn->setTouchEnabled(true);
			}
			if (bLayer->isVisible())
			{
				_operateBtn->setSelectedState(false);
				_operateBtn->setTouchEnabled(true);
			}
			checkSeleteState(2);
		}
		else if (tag == UI_OTHER14_CHECKBOX)
		{

			_operateBtn->setTouchEnabled(false);
			if (cLayer->isVisible())
			{
				_captainBtn->setSelectedState(false);
				_captainBtn->setTouchEnabled(true);
			}
			if (sLayer->isVisible())
			{
				_navigationBtn->setSelectedState(false);
				_navigationBtn->setTouchEnabled(true);
			}
			checkSeleteState(3);

		}
		else if (tag == UI_OTHER12_CHECKBOX)
		{
			_navigationBtn->setTouchEnabled(false);
			if (cLayer->isVisible())
			{
				_captainBtn->setSelectedState(false);
				_captainBtn->setTouchEnabled(true);
			}
			if (bLayer->isVisible())
			{
				_operateBtn->setSelectedState(false);
				_operateBtn->setTouchEnabled(true);
			}
			checkSeleteState(4);
		}
		else if (tag == UI_BUTTON_CLOSE)
		{
			auto userData = GameData::getInstance()->getUserData();
			int i = 0;
			for (auto player : userData->getPlayers())
			{
				if (player->getInfo().appoint == 2 || player->getInfo().appoint == 3 || player->getInfo().appoint == 4)
					i++;
			}
			if (i > 0)
				ShipTransaction::getInstance()->appoint();

			Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_NEWGUIDANCE_START);
			this->removeFromParent();
		}
		else if (tag == UI_BUTTON_AUTOAPP)
		{
			CCS_GET_COMPONENT_FROM_SCENE(node, Layer, layer, UI_APPOINTMENT);
			CCS_GET_CHILD(layer, cocos2d::ui::ScrollView, scrollView, UI_SCROLLVIEW);

			auto userData = GameData::getInstance()->getUserData();
			auto players = userData->getPlayers();

			int cp = 0;
			int op = 0;
			int np = 0;

			int cpcount = 0;
			int opcount = 0;
			int npcount = 0;

			auto ship = userData->getMyShip();
			for (auto& cabin : ship->getCabins())
			{
				if (cabin->getInfo().type == CabinData::CabinType::CAPTAIN_HOUSE)
				{
					cpcount = cabin->getInfo().appointCount;
				}
				if (cabin->getInfo().type == CabinData::CabinType::OPERATE_HOUSE)
				{
					opcount = cabin->getInfo().appointCount;
				}
				if (cabin->getInfo().type == CabinData::CabinType::NAVIGATION_HOUSE)
				{
					npcount = cabin->getInfo().appointCount;
				}
			}
			if (cpcount > 0)
			{
				for (auto player : players)
				{
					auto& playerInfo = player->getInfo();
					if (playerInfo.appoint != 1 && playerInfo.appoint != 3 && playerInfo.appoint != 4)
					{
						if (cp < player->getCP())
						{
							cp = player->getCP();
							playerInfo.appoint = 2;
							checkAppointment(player->getInfo().id, 2);
							for (auto& cabin : ship->getCabins())
							{
								if (playerInfo.appoint == 2 && cabin->getInfo().type == CabinData::CabinType::CAPTAIN_HOUSE)
									cabin->getInfo().appoint = playerInfo.id;

								if (playerInfo.appoint == 3 && cabin->getInfo().type == CabinData::CabinType::OPERATE_HOUSE)
									cabin->getInfo().appoint = playerInfo.id;

								if (playerInfo.appoint == 4 && cabin->getInfo().type == CabinData::CabinType::NAVIGATION_HOUSE)
									cabin->getInfo().appoint = playerInfo.id;
							}
						}
					}
				}
			}
			if (opcount > 0)
			{
				for (auto player : players)
				{
					auto& playerInfo = player->getInfo();
					if (playerInfo.appoint != 1 && playerInfo.appoint != 2 && playerInfo.appoint != 4)
					{
						if (op < player->getOP())
						{
							op = player->getOP();
							playerInfo.appoint = 3;
							checkAppointment(player->getInfo().id, 3);
							for (auto& cabin : ship->getCabins())
							{
								if (playerInfo.appoint == 2 && cabin->getInfo().type == CabinData::CabinType::CAPTAIN_HOUSE)
									cabin->getInfo().appoint = playerInfo.id;

								if (playerInfo.appoint == 3 && cabin->getInfo().type == CabinData::CabinType::OPERATE_HOUSE)
									cabin->getInfo().appoint = playerInfo.id;

								if (playerInfo.appoint == 4 && cabin->getInfo().type == CabinData::CabinType::NAVIGATION_HOUSE)
									cabin->getInfo().appoint = playerInfo.id;
							}
						}
					}
				}
			}
			if (npcount > 0)
			{
				for (auto player : players)
				{
					auto& playerInfo = player->getInfo();
					if (playerInfo.appoint != 1 && playerInfo.appoint != 2 && playerInfo.appoint != 3)
					{
						if (np < player->getNP())
						{
							np = player->getNP();
							playerInfo.appoint = 4;
							checkAppointment(player->getInfo().id, 4);
							for (auto& cabin : ship->getCabins())
							{
								if (playerInfo.appoint == 2 && cabin->getInfo().type == CabinData::CabinType::CAPTAIN_HOUSE)
									cabin->getInfo().appoint = playerInfo.id;

								if (playerInfo.appoint == 3 && cabin->getInfo().type == CabinData::CabinType::OPERATE_HOUSE)
									cabin->getInfo().appoint = playerInfo.id;

								if (playerInfo.appoint == 4 && cabin->getInfo().type == CabinData::CabinType::NAVIGATION_HOUSE)
									cabin->getInfo().appoint = playerInfo.id;
							}

						}

					}
			for (auto& cabin : ship->getCabins())
			{
				if (playerInfo.appoint == 2 && cabin->getInfo().type == CabinData::CabinType::CAPTAIN_HOUSE)
					cabin->getInfo().appoint = playerInfo.id;

				if (playerInfo.appoint == 3 && cabin->getInfo().type == CabinData::CabinType::OPERATE_HOUSE)
					cabin->getInfo().appoint = playerInfo.id;

				if (playerInfo.appoint == 4 && cabin->getInfo().type == CabinData::CabinType::NAVIGATION_HOUSE)
					cabin->getInfo().appoint = playerInfo.id;
			}
				}
			}
			loadScroolView();

			this->getEventDispatcher()->dispatchCustomEvent(EVENT_NEWGUIDANCE_START);
		}
		else
		{
			auto& playerInfo = dynamic_cast<PlayerData*>(widget->getUserObject())->getInfo();
			auto userData = GameData::getInstance()->getUserData();
			if (_captainBtn->getSelectedState())
			{
				playerInfo.appoint = 2;
				checkAppointment(playerInfo.id, 2);
			}
			else if (_operateBtn->getSelectedState())
			{
				playerInfo.appoint = 3;
				checkAppointment(playerInfo.id, 3);
			}
			else if (_navigationBtn->getSelectedState())
			{
				playerInfo.appoint = 4;
				checkAppointment(playerInfo.id, 4);
			}
			auto ship = userData->getMyShip();
			for (auto& cabin : ship->getCabins())
			{
				if (playerInfo.appoint == 2 && cabin->getInfo().type == CabinData::CabinType::CAPTAIN_HOUSE)
					cabin->getInfo().appoint = playerInfo.id;

				if (playerInfo.appoint == 3 && cabin->getInfo().type == CabinData::CabinType::OPERATE_HOUSE)
					cabin->getInfo().appoint = playerInfo.id;

				if (playerInfo.appoint == 4 && cabin->getInfo().type == CabinData::CabinType::NAVIGATION_HOUSE)
					cabin->getInfo().appoint = playerInfo.id;
			}
			//loadScroolView();
		}
		break;
	}
}

void AppointmentView::onEnter()
{
	PopupView::onEnter();
}

void AppointmentView::checkSeleteState(int type)
{
	for (auto item : checkboxList)
	{
		CCS_GET_CHILD(item, CheckBox, checkbox, UI_OTHER1_CHECKBOX);
		checkbox->setSelectedState(false);
		auto playerInfo = dynamic_cast<PlayerData*>(checkbox->getUserObject())->getInfo();
		if (playerInfo.appoint == type)
		{
			CCS_GET_CHILD(item, Text, text5, UI_OTHER1_TEXT5);
			text5->setText(LocalizeService::getInstance()->getString(PLAYER_APPOINTMENT_STATE[playerInfo.appoint]));
			checkbox->setSelectedState(true);
		}
	}
}

void AppointmentView::checkAppointment(std::string pID, int type)
{
	auto userData = GameData::getInstance()->getUserData();
	auto players = userData->getPlayers();
	for (auto player : players)
	{
		if (player->getInfo().id != pID)
		{
			auto& playerInfo = player->getInfo();
			if (playerInfo.appoint == type)
				playerInfo.appoint = 0;
		}
	}
	auto ship = userData->getMyShip();
	for (auto& cabin : ship->getCabins())
	{
		switch (type)
		{
		case 2:
			if (cabin->getInfo().type == CabinData::CabinType::CAPTAIN_HOUSE)
				cabin->getInfo().appoint = "";
			break;
		case 3:
			if (cabin->getInfo().type == CabinData::CabinType::OPERATE_HOUSE)
				cabin->getInfo().appoint = "";
			break;
		case 4:
			if (cabin->getInfo().type == CabinData::CabinType::NAVIGATION_HOUSE)
				cabin->getInfo().appoint = "";
			break;
		default:
			break;
		}
	}
	for (auto item : checkboxList)
	{
		CCS_GET_CHILD(item, CheckBox, checkbox, UI_OTHER1_CHECKBOX);
		checkbox->setSelectedState(false);
		auto playerInfo = dynamic_cast<PlayerData*>(checkbox->getUserObject())->getInfo();
		if (playerInfo.appoint == 0 || playerInfo.appoint == type)
		{
			CCS_GET_CHILD(item, Text, text5, UI_OTHER1_TEXT5);
			text5->setText(LocalizeService::getInstance()->getString(PLAYER_APPOINTMENT_STATE[playerInfo.appoint]));
		}
	}
}

void AppointmentView::loadScroolView()
{
	CCS_GET_COMPONENT_FROM_SCENE(node, Layer, layer, UI_APPOINTMENT);
	CCS_GET_CHILD(layer, cocos2d::ui::ScrollView, scrollView, UI_SCROLLVIEW);
	checkboxList.clear();
	scrollView->removeAllChildren();
	auto userData = GameData::getInstance()->getUserData();
	auto players = userData->getPlayers();
	int i = 0;
	int height = 0;
	for (auto player : players)
	{
		auto playerInfo = player->getInfo();
		if (playerInfo.appoint != 1)
		{
			auto uiLayer = CCS_CREATE_LAYER("UI_Other1_1");
			CCS_GET_CHILD(uiLayer, Text, text1, UI_OTHER1_TEXT1);
			CCS_GET_CHILD(uiLayer, Text, text2, UI_OTHER1_TEXT2);
			CCS_GET_CHILD(uiLayer, Text, text3, UI_OTHER1_TEXT3);
			CCS_GET_CHILD(uiLayer, Text, text4, UI_OTHER1_TEXT4);
			CCS_GET_CHILD(uiLayer, Text, text5, UI_OTHER1_TEXT5);
			CCS_GET_CHILD(uiLayer, CheckBox, checkbox, UI_OTHER1_CHECKBOX);
			text1->setText(playerInfo.name);
			text2->setText(UTF8(STRING(player->getCP())));
			text3->setText(UTF8(STRING(player->getNP())));
			text4->setText(UTF8(STRING(player->getOP())));
			text5->setText(LocalizeService::getInstance()->getString(PLAYER_APPOINTMENT_STATE[playerInfo.appoint]));
			i++;
			height = uiLayer->getContentSize().height;
			checkbox->addTouchEventListener(this, toucheventselector(AppointmentView::touchButton));
			if (_captainBtn->getSelectedState() && playerInfo.appoint == 2)
				checkbox->setSelectedState(true);
			else if (_operateBtn->getSelectedState() && playerInfo.appoint == 3)
				checkbox->setSelectedState(true);
			else if (_navigationBtn->getSelectedState() && playerInfo.appoint == 4)
				checkbox->setSelectedState(true);
			checkbox->setUserObject(player);
			checkboxList.push_back(uiLayer);
			scrollView->addChild(uiLayer);
		}
	}
	scrollView->setInnerContainerSize(Size(scrollView->getContentSize().width, i*height));
}