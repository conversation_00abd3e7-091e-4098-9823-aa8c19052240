#include "SkillData.h"
#include "../GameData.h"

USING_NS_CC;

SkillData* SkillData::create(int mid)
{
    auto data = new SkillData(mid);
    if (data)
    {
        data->autorelease();
        return data;
    }
    
    CC_SAFE_DELETE(data);
    return nullptr;
}

SkillData::SkillData()
{
    
}

SkillData::SkillData(int mid)
{
	auto configs = Configuration::getInstance()->getValue(SKILL_CONFIG).asValueMap();
	auto &config = configs[STRING(mid)].asValueMap();

	_info.mid = mid;

	_info.icon = config["Icon"].asInt();
	_info.level = config["CurrentLevel"].asInt();
	_info.next = config["OnLevel"].asInt();
	_info.name = config["Name"].asString();
	_info.extent = config["Extent"].asInt();

	_effect.type = config["EffectID"].asInt();
	_effect.value = config["EffectValue"].asFloat();

	_need.level = config["NextLevel"].asInt();
	_need.money = config["NextCoin"].asInt();

	_trigger.type = config["TriggerType"].asInt();
	_trigger.extent = config["TriggerAreaType"].asInt();
	_trigger.effect = config["TriggerEffectId"].asString();
	_trigger.count = config["TriggerCount"].asInt();

	_shooter.mid = config["ShooterId"].asString();
	_shooter.extent = config["ShooterAreaType"].asInt();
	_shooter.penetration = config["IsPenetration"].asBool();

	_hitting.effect = config["HittingEffectId"].asString();
	_hitting.extent = config["HittingAreaType"].asInt();
}

SkillData::~SkillData()
{
    
}

