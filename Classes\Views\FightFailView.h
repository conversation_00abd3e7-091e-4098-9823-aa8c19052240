#ifndef __SOS_FIGHT_FAIL_VIEW__
#define __SOS_FIGHT_FAIL_VIEW__

#include "cocos2d.h"
#include "PopupView.h"
#include "../SOSConfig.h"
#include "ui/CocosGUI.h"
#include "../Datas/FightData.h"

class FightFailView : public PopupView
{
public:
	CREATE_FUNC(FightFailView);
	static FightFailView* create(FightData* fight);
public:
	virtual bool init() override;

	void touchButton(cocos2d::Ref* object, cocos2d::ui::TouchEventType type);
};
#endif