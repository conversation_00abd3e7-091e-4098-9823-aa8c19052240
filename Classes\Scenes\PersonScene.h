#ifndef __SOS_PERSON_SCENE_
#define __SOS_PERSON_SCENE_

#include "cocos2d.h"
#include "ui/CocosGUI.h"
#include "cocostudio/CocoStudio.h"
#include "../SOSConfig.h"
#include "../GameData.h"
#include "BaseScene.h"

class PersonScene : public BaseScene
{
public:
	CREATE_FUNC(PersonScene);
	static cocos2d::Scene* createScene();
	static cocos2d::Scene* createScene(PlayerData* playerInfo);

public:
	PersonScene();
	virtual ~PersonScene();

	virtual bool init() override;
	virtual void onEnter() override;
// 	virtual bool onTouchBegan(cocos2d::Touch* touch, cocos2d::Event* event) override;
// 	virtual void onTouchMoved(cocos2d::Touch* touch, cocos2d::Event* event) override;
// 	virtual void onTouchEnded(cocos2d::Touch* touch, cocos2d::Event* event) override;

	void menuCloseCallback(cocos2d::Ref* pSender);
	void touchButton(cocos2d::Ref* object, cocos2d::ui::TouchEventType type);
	void touchPageView(cocos2d::Ref* object, cocos2d::ui::PageViewEventType type);

	void update(float dt) override;
private:
	void initEquipments();
	void initBagLayer(cocos2d::Node *layer);
	void initPropertyLayer(cocos2d::Node *layer);
	void initSkillLayer(cocos2d::Node *layer);
	void initTrainLayer(cocos2d::Node *layer);
	bool equipItem(ItemData *item);
	bool unequipItem(ItemData *item);

private:
	//cocos2d::EventListenerTouchOneByOne* _listener;
	cocos2d::Node* node;
	cocos2d::Node* _bagLayer;
	cocos2d::ui::Widget* _propertyLayer;
	cocos2d::ui::Widget* _trainLayer;
	cocos2d::ui::Widget* _skillLayer;
	cocos2d::ui::ImageView* _movedTarget;
	cocos2d::ui::Widget* _touchTarget;
	cocos2d::ui::Widget* _equipTarget;

	int _refresh;
	int _cd;
	bool _isUp;
};

#endif //__SOS_PERSON_SCENE_