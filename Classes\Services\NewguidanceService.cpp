#include "NewguidanceService.h"
#include "../GameData.h"
#include "../Views/Newguidance.h"
#include "../Views/NPCView.h"
#include "../Views/DialogView.h"
#include "../Views/AlertView.h"
#include "../Scenes/BaseScene.h"
#include "LocalizeService.h"
#include "cocostudio/CocoStudio.h"
#include "../Transactions/LoginTransaction.h"

USING_NS_CC;
USING_NS_CC_CCS;

FUNC_GET_INSTANCE(NewguidanceService);

Layer* r_guideUI;

NewguidanceService::NewguidanceService()
: _isFinshed(false)
{
	Director::getInstance()->getEventDispatcher()->addCustomEventListener(EVENT_GUIDANCE_FINISHED, [=](EventCustom* event) {
		if (GameData::getInstance()->getUserData()->getInfo().state == UserData::StateType::GUIDER)
		{
			_isFinshed = true;
		}
	});

	Director::getInstance()->getEventDispatcher()->addCustomEventListener(EVENT_NEWGUIDANCE_START, [=](EventCustom* event) {
		if (_isFinshed && GameData::getInstance()->getUserData()->getInfo().state == UserData::StateType::GUIDER)
		{
			this->createLayer();
		}
	});

	Director::getInstance()->getEventDispatcher()->addCustomEventListener(EVENT_NEWGUIDANCE_TOUCHED, [=](EventCustom* event){
		if (GameData::getInstance()->getUserData()->getInfo().state == UserData::StateType::GUIDER)
		{
			auto tag = static_cast<int*>(event->getUserData());
			auto user = GameData::getInstance()->getUserData();
			auto guidances = user->getNovices();
			auto guidance = guidances[user->getGuidance().id][user->getGuidance().action];

			if (!user->getInfo().inCity && guidance->getInfo().ui == -1)
			{
				return;
			}

			if (guidance->getInfo().ui == -2 || guidance->getInfo().ui == -1 || guidance->getInfo().ui == 0 || (tag != nullptr && *tag == guidance->getInfo().ui))
			{
				if (tag != nullptr) CCLOG("[GUIDANCE] touch guidance ui from [%d]", *tag);

// 				ActionManagerEx::getInstance()->releaseActionByName("UI_Arrow_1.ExportJson");
// 				ActionManagerEx::getInstance()->releaseActionByName("UI_Arrow_2.ExportJson");
// 				ActionManagerEx::getInstance()->releaseActionByName("UI_Arrow_3.ExportJson");
// 				ActionManagerEx::getInstance()->releaseActionByName("UI_Arrow_4.ExportJson");

				if (r_guideUI != nullptr)
				{
					r_guideUI->removeFromParent();
					r_guideUI = nullptr;
				}

				CCLOG("[GUIDANCE] current id: %d, action: %d", user->getGuidance().id, user->getGuidance().action);

				if (guidance->getInfo().save > 0)
				{
					user->getGuidance().save = guidance->getInfo().save;
					LoginTransaction::getInstance()->updateInfo();
					//UserDefault::getInstance()->setIntegerForKey("GuideSave", guidance->getInfo().save);
				}

				if (user->getGuidance().action < user->getNovices().at(user->getGuidance().id).size())
				{
					user->getGuidance().action++;
					_isFinshed = true;

					if (guidance->getInfo().ui == -2 || guidance->getInfo().ui == 0)
					{
						//this->getEventDispatcher()->dispatchCustomEvent(EVENT_NEWGUIDANCE_START);
						this->createLayer();
					}

					//UserDefault::getInstance()->setIntegerForKey("GuideAction", user->getGuidance().action);
					//this->createLayer();
				}
				else
				{
					user->getGuidance().id++;
					user->getGuidance().action = 1;

					if (user->getGuidance().id <= guidances.size())
					{
						auto nextGuidance = guidances[user->getGuidance().id][user->getGuidance().action];
						if (guidance->getInfo().scene == nextGuidance->getInfo().scene)
						{
							_isFinshed = true;

							if (guidance->getInfo().ui == -2 || guidance->getInfo().ui == 0)
							{
								//this->getEventDispatcher()->dispatchCustomEvent(EVENT_NEWGUIDANCE_START);
								this->createLayer();
							}
							//this->getEventDispatcher()->dispatchCustomEvent(EVENT_GUIDANCE_FINISHED);
							//this->createLayer();
						}
					}
				}

// 				auto nextGuidance = guidances[user->getGuidance().id][user->getGuidance().action];
// 
// 				if (nextGuidance->getInfo().save > 0)
// 				{
// 					user->getGuidance().save = nextGuidance->getInfo().save;
// 					LoginTransaction::getInstance()->updateInfo();
// 				}
			}
		}
	});
}

NewguidanceService::~NewguidanceService()
{

}

bool NewguidanceService::createLayer()
{
	auto scene = Director::getInstance()->getRunningScene();
	auto layer = scene->getChildByTag(TagBaseScene::SCENE);
	return NewguidanceService::createLayer(layer);
}

bool NewguidanceService::createLayer(Node* parent)
{
	_isFinshed = false;

	auto user = GameData::getInstance()->getUserData();
	auto guidances = user->getNovices();

	if (user->getGuidance().id == guidances.size() && user->getGuidance().action >= guidances[user->getGuidance().id].size())
	{
		//MessageBox("Congratulate for you, you have finished all guidance. Have fun~", "WARNING");
		parent->addChild(AlertView::create(LocalizeService::getInstance()->getString("1000016"), AlertType::IDLE), 99999999);
		user->getInfo().state = UserData::StateType::IDLE;
		//UserDefault::getInstance()->setBoolForKey("Guider", false);
		LoginTransaction::getInstance()->updateInfo();

		Director::getInstance()->getTextureCache()->removeTextureForKey("UI_Newguidance/UI_Newguidance.png");
		SpriteFrameCache::getInstance()->removeSpriteFramesFromFile("UI_Newguidance/UI_Newguidance.plist");

		ArmatureDataManager::getInstance()->removeArmatureFileInfo("Animation_Light.ExportJson");
		ArmatureDataManager::getInstance()->removeArmatureFileInfo("Animation_Guide.ExportJson");
		ArmatureDataManager::getInstance()->removeArmatureFileInfo("Animation_word.ExportJson");

		return false;
	}

	auto guidance = guidances[user->getGuidance().id][user->getGuidance().action];

	if (guidance->getInfo().save > 0)
	{
		user->getGuidance().save = guidance->getInfo().save;
		LoginTransaction::getInstance()->updateInfo();
	}

	switch (guidance->getInfo().type)
	{
	case NoviceData::NoviceType::DEFAULT:
		if (user->getGuidance().action < guidances.at(user->getGuidance().id).size())
		{
			user->getGuidance().action++;
		}
		else
		{
			user->getGuidance().id++;
			user->getGuidance().action = 1;
		}
		return false;
		break;
	case NoviceData::NoviceType::DIALOG:
		//parent->addChild(NPCView::createScene(), parent->getChildrenCount() + 1);
		parent->addChild(DialogView::create(guidance->getInfo().dialog), parent->getChildrenCount() + 1);
		break;
	default:
		break;
	}

// 	auto node = parent->getChildByTag(TagBaseScene::LAYER);
// 	CCS_GET_COMPONENT_FROM_SCENE(node, Layer, layer, 10009);
// 	CCS_GET_CHILD(layer, Button, button, 8);
// 	button->addTouchEventListener(this, toucheventselector(NewguidanceService::touchButton));

	r_guideUI = Newguidance::createScene();
	parent->addChild(r_guideUI, 9999999);

	return true;
}

bool NewguidanceService::createLayer(cocos2d::Node* parent, int type)
{
	return NewguidanceService::createLayer(parent);
}
