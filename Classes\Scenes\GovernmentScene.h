#ifndef __GOVERNMENT_SCENE_H__
#define __GOVERNMENT_SCENE_H__

#include "cocos2d.h"
#include "ui/CocosGUI.h"
#include "cocostudio/CocoStudio.h"
#include "BaseScene.h"

class GovernmentScene : public BaseScene
{
public:
	CREATE_FUNC(GovernmentScene);

	static cocos2d::Scene* createScene();
	static cocos2d::Scene* createScene(int z);

public:
	GovernmentScene();
	virtual ~GovernmentScene();

	virtual bool init() override;
	virtual void update(float dt) override;
	virtual void onEnter() override;
	virtual bool onTouchBegan(cocos2d::Touch* touch, cocos2d::Event* event);
	virtual void onTouchMoved(cocos2d::Touch* touch, cocos2d::Event* event);
	virtual void onTouchEnded(cocos2d::Touch* touch, cocos2d::Event* event);

	void touchButton(cocos2d::Ref* object, cocos2d::ui::TouchEventType type);
	void initCopies();
	cocos2d::ui::Widget* createReward(int id, int value);
	
private:
	cocos2d::Node* node;
	cocos2d::Layer* _myLayout;
	cocos2d::ui::Widget* _fbLayout;
	cocos2d::EventListenerTouchOneByOne* _touchListener;
	cocos2d::Sprite* _imgBG;
	cocos2d::Layer* _layer1;

	int _checkpointID;
	int _copiesID;
	int _reqMobility;

	int _st = 0;
};

#endif // __GOVERNMENT_SCENE_H__