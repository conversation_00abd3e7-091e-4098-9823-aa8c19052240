﻿#include "CreatePersonScene.h"
#include "../Services/LocalizeService.h"
#include "../Transactions/LoginTransaction.h"
#include "../Transactions/DockTransaction.h"
#include "../Transactions/FormationTransaction.h"
#include "../Transactions/PartnerTransaction.h"
#include "../Views/AlertView.h"

USING_NS_CC;
USING_NS_CC_CCS; 
USING_NS_CC_UI;

enum TagCreatePerson
{
	UI_BUTTON_DICE = 39,			//随机姓名
	UI_TEXTBUTTON_SERVER = 40,		//选择服务器
	UI_BUTTON_PLAY = 41,			//进入游戏
	UI_BUTTON_ACCOUNT = 42,			//账号绑定
	UI_BUTTON_ROLE1 = 51,			//东海男主
	UI_BUTTON_ROLE2 = 50,			//北海女主
	UI_TEXTFIELD_NAME = 38,			//NAME
	
	UI_CREATE_UI1 = 10005,
	UI_CREATE_UI2 = 10003,
	UI_CREATE_UI3 = 10004,

	UI2_IMAGEVIEW_ROLE = 43,
	UI2_TEXTFONT_JOB = 44,
	UI2_TEXTFONT_EFFECT = 45,
	UI2_TEXTAREA = 46,
};

Node* r_randomAlert;

CreatePersonScene::CreatePersonScene()
: _seas(1)
{
	r_randomAlert = nullptr;
}

CreatePersonScene::~CreatePersonScene()
{
	this->getEventDispatcher()->removeCustomEventListeners(EVENT_GET_RANDOM_NAME);
	this->getEventDispatcher()->removeCustomEventListeners(EVENT_CREATE_SUCCESS);
	this->getEventDispatcher()->removeCustomEventListeners(EVENT_GAME_LOGIN_SUCCESS);
	this->getEventDispatcher()->removeCustomEventListeners(EVENT_DOCK_GET_SUCCESS);
	this->getEventDispatcher()->removeCustomEventListeners(EVENT_FORMATION_GET_SUCCESS);
	this->getEventDispatcher()->removeCustomEventListeners(EVENT_PASSIVE_SKILLS_GET_SUCCESS);
	this->getEventDispatcher()->removeCustomEventListeners(EVENT_GAME_MISSION_QUERY);

	ActionManagerEx::getInstance()->releaseActions();

	for (int i = 1; i <= 2; i++)
	{
		auto name = String::createWithFormat("UI_CreatePerson/UI_CreatePerson_%d.png", i)->getCString();
		Director::getInstance()->getTextureCache()->removeTextureForKey(name);

		auto frameName = String::createWithFormat("UI_CreatePerson/UI_CreatePerson_%d.plist", i)->getCString();
		SpriteFrameCache::getInstance()->removeSpriteFramesFromFile(frameName);
	}
}

Scene* CreatePersonScene::createScene()
{
	auto scene = Scene::create();
	auto layer = CreatePersonScene::create();
	scene->addChild(layer);

	return scene;
}

bool CreatePersonScene::init()
{
	if ( !BaseScene::init() )
	{
		return false;
	}

	_node = CCS_CREATE_SCENE("Scene_CreatePerson");
	this->addChild(_node);

	CCS_GET_COMPONENT_FROM_SCENE(_node, Layer, layer, UI_CREATE_UI1);
	CCS_GET_CHILD(layer, Button, diceBtn, UI_BUTTON_DICE);
	CCS_GET_CHILD(layer, Button, playBtn, UI_BUTTON_PLAY);
	//CCS_GET_CHILD(layer, TextField, _textFieldName, UI_TEXTFIELD_NAME);

	diceBtn->addTouchEventListener(this,toucheventselector(CreatePersonScene::touchButton));  
	playBtn->addTouchEventListener(this,toucheventselector(CreatePersonScene::touchButton));

	_textFieldName = dynamic_cast<TextField*>(layer->getChildByTag(UI_TEXTFIELD_NAME));
	//_textFieldName->setTouchEnabled(true);
	_textFieldName->setMaxLengthEnabled(true);
	_textFieldName->setMaxLength(12);
	_textFieldName->setText(GameData::getInstance()->randomName);
	//_textFieldName->addEventListenerTextField(this, textfieldeventselector(CreatePersonScene::textFieldEvent));

	CCS_GET_COMPONENT_FROM_SCENE(_node, Layer, ui3, UI_CREATE_UI3);
	CCS_GET_CHILD(ui3, Button, role1Btn, UI_BUTTON_ROLE1);
	CCS_GET_CHILD(ui3, Button, role2Btn, UI_BUTTON_ROLE2);

	role1Btn->addTouchEventListener(this,toucheventselector(CreatePersonScene::touchButton));
	role2Btn->addTouchEventListener(this,toucheventselector(CreatePersonScene::touchButton));

	return true;
}

void CreatePersonScene::onEnter()
{
	auto userData = GameData::getInstance()->getUserData();

	this->getEventDispatcher()->addCustomEventListener(EVENT_GET_RANDOM_NAME, [=](EventCustom* event) {
		//this->removeChildByTag(999999);
		r_randomAlert->removeFromParent();
		r_randomAlert = nullptr;
		_textFieldName->setText(GameData::getInstance()->randomName);
	});
	this->getEventDispatcher()->addCustomEventListener(EVENT_CREATE_SUCCESS, [=](EventCustom* event) {
		//LoginTransaction::getInstance()->bind(GameData::getInstance()->token, GameData::getInstance()->getUserData()->getInfo().uid);
		LoginTransaction::getInstance()->login(userData->getInfo().uid);
	});
	this->getEventDispatcher()->addCustomEventListener(EVENT_GAME_LOGIN_SUCCESS, [=](EventCustom* event) {
		userData->getInfo().login = true;
		DockTransaction::getInstance()->getShip(userData->getInfo().uid);
	});
	this->getEventDispatcher()->addCustomEventListener(EVENT_DOCK_GET_SUCCESS, [=](EventCustom* event) {
		FormationTransaction::getInstance()->Get(userData->getInfo().uid);
	});
	this->getEventDispatcher()->addCustomEventListener(EVENT_FORMATION_GET_SUCCESS, [=](EventCustom* event) {
		MissionTransaction::getInstance()->query(userData->getInfo().uid, userData->getInfo().city);
	});
	this->getEventDispatcher()->addCustomEventListener(EVENT_GAME_MISSION_QUERY, [=](EventCustom* event){
		PartnerTransaction::getInstance()->getPassiveSkills(userData->getInfo().uid);
	});
	this->getEventDispatcher()->addCustomEventListener(EVENT_PASSIVE_SKILLS_GET_SUCCESS, [=](EventCustom* event) {
		auto onlyID = static_cast<std::string *>(event->getUserData());
		auto players = userData->getPlayers();
		for (auto player : players)
		{
			if (player->getInfo().id == *onlyID)
			{
				userData->getTeam()->getInfo().pid = player->getInfo().id;
				break;
			}
		}

		std::vector<std::string> loadings;
		for (auto i = 1; i <= 16; i++)
		{
			loadings.push_back(String::createWithFormat("ALLUI_%d.png", i)->getCString());
		}

		Director::getInstance()->replaceScene(LoadingScene::createScene(LoadingScene::SceneType::START, loadings));
	});

	CCLOG("%s", Director::getInstance()->getTextureCache()->getCachedTextureInfo().c_str());

	Layer::onEnter();
}

void CreatePersonScene::menuCloseCallback(Ref* pSender)
{
	Director::getInstance()->end();

#if (CC_TARGET_PLATFORM == CC_PLATFORM_IOS)
	exit(0);
#endif
}

void CreatePersonScene::touchButton(Ref* obj,cocos2d::ui::TouchEventType eventType)  
{  
	auto userData = GameData::getInstance()->getUserData();
	auto button = dynamic_cast<Button*>(obj);  
	int tag = button->getTag();  
	switch (eventType)
	{
	case TouchEventType::TOUCH_EVENT_BEGAN: 
		break;
	case TouchEventType::TOUCH_EVENT_ENDED: 
		if(tag==UI_BUTTON_ROLE1||tag==UI_BUTTON_ROLE2)
		{
			if(tag==UI_BUTTON_ROLE1)
				_seas = 1;
			else
				_seas = 2;
			initUI2(_seas);
		}
		else if(tag==UI_BUTTON_DICE)
		{
			r_randomAlert = AlertView::create(LocalizeService::getInstance()->getString("632"), AlertType::LOADING);
			this->addChild(r_randomAlert, 999999);
			CreateTransaction::getInstance()->getName(userData->getInfo().uid,_seas);
		}
		else if(tag==UI_BUTTON_PLAY)
		{
			this->addChild(AlertView::create(LocalizeService::getInstance()->getString("632"), AlertType::LOADING), 999999);
			CreateTransaction::getInstance()->create(userData->getInfo().uid, _textFieldName->getStringValue(), _seas, 0);
		}
		break;
	default:
		break;
	}
}

void CreatePersonScene::initUI2(int type)
{
	CCS_GET_COMPONENT_FROM_SCENE(_node, Layer, layer, UI_CREATE_UI2);
	CCS_GET_CHILD(layer, ImageView, img, UI2_IMAGEVIEW_ROLE);
	CCS_GET_CHILD(layer, TextBMFont, professionfont, UI2_TEXTFONT_JOB);
	CCS_GET_CHILD(layer, TextBMFont, infofont, UI2_TEXTFONT_EFFECT);
	CCS_GET_CHILD(layer, Text, des, UI2_TEXTAREA);

	img->loadTexture(String::createWithFormat("ss_role_%d.png",_seas)->getCString(),TextureResType::PLIST);
	professionfont->setText(LocalizeService::getInstance()->getString(PLAYER_JOB_NAMES[_seas]).c_str());
	infofont->setText(LocalizeService::getInstance()->getString(PLAYER_JOB_EFFECT[_seas]).c_str());
	des->setText(LocalizeService::getInstance()->getString(PLAYER_JOB_DESCRIPTION[_seas]));

	auto action = ActionManagerEx::getInstance()->getActionByName("UI_CreatePerson_2.json", "Animation0");
	action->play();
}

void CreatePersonScene::textFieldEvent(Ref *pSender, TextFiledEventType type)
{
	auto name = dynamic_cast<TextField*>(pSender);
	switch (type)
	{
	case TEXTFIELD_EVENT_ATTACH_WITH_IME:
		CCLOG("text attach");
		//name->setText(String::createWithFormat("点击")->getCString());
		break;   
	case TEXTFIELD_EVENT_DETACH_WITH_IME:
		CCLOG("text detach");
		//name->setText(String::createWithFormat("移开")->getCString());
		break;
	case TEXTFIELD_EVENT_INSERT_TEXT:
		CCLOG("insert text");
		//name->setText(String::createWithFormat("增加字段")->getCString());
		break;
	case TEXTFIELD_EVENT_DELETE_BACKWARD:
		CCLOG("delete backward");
		///name->setText(String::createWithFormat("删除字段")->getCString());
		break;
	}

//#if (CC_TARGET_PLATFORM == CC_PLATFORM_WIN32) 
//
//	std::string str = "习近平|江泽民|胡锦涛";
//	std::regex pattern(str);
//	if (regex_match("习近", pattern )||str.find("习近") )  
//	{  
//		CCLOG("包含敏感字符");
//		return;  
//	}
//
//#endif
//
//
//#if (CC_TARGET_PLATFORM == CC_PLATFORM_ANDROID||CC_TARGET_PLATFORM == CC_PLATFORM_IOS)  
//	char ss[100] = {};
//	sprintf(ss, "%s", "习近");
//	regmatch_t pmatch[4];
//	regex_t match_regex;
//	std::string str = "习近平|江泽民|胡锦涛";
//	regcomp(&match_regex,
//		str,
//		REG_EXTENDED);
//	if (regexec(&match_regex, ss, 4, pmatch, 0) != 0 ||)
//	{
//		Tools::showToast(this, Tools::a2u("包含敏感字符"), 3.0f);
//		return;
//	}
//	regfree(&match_regex);
//#endif


}
