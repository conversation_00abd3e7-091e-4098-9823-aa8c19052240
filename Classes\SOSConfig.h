﻿//
//  SOSConfig.h
//  TestSOSCpp
//
//  Created by <PERSON><PERSON> on 14-1-14.
//
//

#ifndef __TestSOSCpp__SOSConfig__
#define __TestSOSCpp__SOSConfig__

#include "cocos2d.h"

static const std::string PLAYER_CONFIG = "S_PlayerPoolInfoSet";
static const std::string SKILL_CONFIG = "S_MagicInfoSet";
static const std::string SHIP_CONFIG = "S_ShipInfoSet";
static const std::string PLAYER_UPGRADE_CONFIG = "S_PlayerUpgradeConfigSet";
static const std::string PLAYER_CULTIVATE_DIAMOND_CONFIG = "S_CultivateDiamondCostInfoSet";
static const std::string TEAM_SKILL_REFRESH_COST_CONFIG = "S_PassiveSkillsRefreshCostSet";
static const std::string RECRUIT_REFRESH_COST_CONFIG = "S_RefreshCostInfoSet";
static const std::string COPIES_CONFIG = "S_CopiesInfoSet";
static const std::string CHECKPOINT_CONFIG = "S_CheckpointInfoSet";
static const std::string PROPS_CONFIG = "S_PropsInfoSet";
static const std::string PLAYER_TRAINING_CONFIG = "S_PlayerTrainingSet";
static const std::string SHIPROOM_UPGRADE_CONFIG = "S_ShipRoomConfigInfoSet";
static const std::string CARGOS_CONFIG = "S_TradablesInfoSet";
static const std::string TASK_CONFIG = "S_TaskInfoSet";
static const std::string CITY_CONFIG = "S_CityInfoSet";
static const std::string NEWGUIDANCE_CONFIG = "S_NewguidanceSet";
static const std::string TRADABLESHOUSE_CONFIG = "S_TradablesHouseInfoSet";
static const std::string QUICKEN_CONFIG = "S_QuickenConfigSet";
static const std::string SMITHYINFO_CONFIG = "S_SmithyInfoSet";
static const std::string RECORDREWARD_CONFIG = "S_RecordRewardInfoSet";

static const std::string GAME_CONFIGS[] = {
	//"Configs/" + PLAYER_CONFIG + ".plist",
	//"Configs/" + SKILL_CONFIG + ".plist",
	"Configs/" + SHIP_CONFIG + ".plist",
	"Configs/" + PLAYER_UPGRADE_CONFIG + ".plist",
	"Configs/" + PLAYER_CULTIVATE_DIAMOND_CONFIG + ".plist",
	"Configs/" + TEAM_SKILL_REFRESH_COST_CONFIG + ".plist",
	"Configs/" + RECRUIT_REFRESH_COST_CONFIG + ".plist",
	"Configs/" + COPIES_CONFIG + ".plist",
	"Configs/" + CHECKPOINT_CONFIG + ".plist",
	"Configs/" + PROPS_CONFIG + ".plist",
	"Configs/" + PLAYER_TRAINING_CONFIG + ".plist",
	"Configs/" + SHIPROOM_UPGRADE_CONFIG + ".plist",
	"Configs/" + TASK_CONFIG + ".plist",
	"Configs/" + CARGOS_CONFIG + ".plist",
	"Configs/" + CITY_CONFIG + ".plist",
	"Configs/" + NEWGUIDANCE_CONFIG + ".plist",
	"Configs/" + TRADABLESHOUSE_CONFIG + ".plist",
	"Configs/" + QUICKEN_CONFIG + ".plist",
	"Configs/" + SMITHYINFO_CONFIG + ".plist",
	"Configs/" + RECORDREWARD_CONFIG + ".plist",
};

static const std::string GAME_SKILLS_ARMATURES[] = {
	"Animations/Animation_Aoe.ExportJson",
	"Animations/Animation_Effect.ExportJson",
	"Animations/Animation_Shooter.ExportJson",
};

static const std::string GAME_PLAYERS_ARMATURES[] = {
	"Players/Animation_1_5.ExportJson",
	"Players/Animation_1_6.ExportJson",
	"Players/Animation_2_5.ExportJson",
	"Players/Animation_2_6.ExportJson",
	"Players/Animation_5_4.ExportJson",
	"Players/Animation_5_5.ExportJson",
	"Players/Animation_5_6.ExportJson",
	"Players/Animation_6_4.ExportJson",
	"Players/Animation_6_5.ExportJson",
	"Players/Animation_6_6.ExportJson",
	"Players/Animation_7_5.ExportJson",
	"Players/Animation_7_6.ExportJson",
	"Players/Animation_8_1.ExportJson",
	"Players/Animation_8_4.ExportJson",
	"Players/Animation_8_5.ExportJson",
	"Players/Animation_9_6.ExportJson",
	"Players/Animation_10_4.ExportJson",
	"Players/Animation_10_5.ExportJson",
	"Players/Animation_10_6.ExportJson",
	"Players/Animation_11_1.ExportJson",
	"Players/Animation_11_4.ExportJson",
	"Players/Animation_11_5.ExportJson",
	"Players/Animation_12_1.ExportJson",
	"Players/Animation_12_4.ExportJson",
	"Players/Animation_12_5.ExportJson",
	"Players/Animation_13_1.ExportJson",
	"Players/Animation_13_4.ExportJson",
	"Players/Animation_13_5.ExportJson",
	"Players/Animation_14_1.ExportJson",
	"Players/Animation_14_4.ExportJson",
	"Players/Animation_14_5.ExportJson",
	"Players/Animation_15_1.ExportJson",
	"Players/Animation_15_4.ExportJson",
	"Players/Animation_15_5.ExportJson",
	"Players/Animation_16_1.ExportJson",
	"Players/Animation_16_4.ExportJson",
	"Players/Animation_16_5.ExportJson",
	"Players/Animation_17_6.ExportJson",
	"Players/Animation_18_5.ExportJson",
	"Players/Animation_18_6.ExportJson",
	"Players/Animation_19_1.ExportJson",
	"Players/Animation_19_4.ExportJson",
	"Players/Animation_19_5.ExportJson",
	"Players/Animation_20_4.ExportJson",
	"Players/Animation_20_5.ExportJson",
	"Players/Animation_20_6.ExportJson",
};

static const int CULTIVATE_COUNT = 5;
static const int APPRAISAL_COUNT = 6;
static const int TEAM_SKILL_NEED_COUNT = 2;
static const int SHIP_COUNT = 14;
static const int PLAYER_COUNT = 20;

//static const std::string PLAYER_JOB_NAMES[] = { "", "狂战士", "魔导士", "冒险家", "龙骑士", "守护者" };
static const std::string PLAYER_JOB_NAMES[] = { "100020", "100021", "100022", "100023", "100024", "100025" };
//static const std::string SHIP_CABIN_NAMES[] = { "", "船长室", "经营室", "补给室", "航海室", "休息室", "医疗室", "训练室", "货舱" };
static const std::string SHIP_CABIN_NAMES[] = { "", "100072", "100073", "100074", "100075", "100076", "100077", "100078", "100079" };
//static const std::string SKILL_EFFECT_NAMES[] = { "", "", "", "", "", "伤害值", "生命值", "物理攻击力", "物理防御力", "魔法攻击力", "魔法防御力", "暴击率", "闪避率", "破击率", "格挡率", "幸运率", "智力", "力量" };
//static const std::string SKILL_EFFECT_NAMES[] = { "100031", "100032", "100033", "100034", "", "100035", "100036", "100037", "100038", "100039", "100040", "100041", "100042", "100043", "100044", "100045", "100046", "100047" };
//static const std::string SKILL_EXTENT_NAMES[] = { "", "单体", "纵向", "横向", "全体", "随机" };
static const std::string SKILL_EXTENT_NAMES[] = { "", "100026", "100027", "100028", "100029", "100030" };
//static const std::string PLAYER_JOB_EFFECT[] = { "", "全体物攻＋2％", "全体魔防＋3％", "", "" };
static const std::string PLAYER_JOB_EFFECT[] = { "", "100070", "100071", "", "" };
//static const std::string PLAYER_JOB_DESCRIPTION[] = { "", "　　孤儿，自小被东岛武士收留，练就一手好剑术。", "　　北岛，天生对于魔法就有一种过人的领悟能力。", "", "" };
static const std::string PLAYER_JOB_DESCRIPTION[] = { "", "100068", "100069", "", "" };
static const std::string PLAYER_NAME_COLOR[] = { "", "#595959", "#008500", "#0052e8", "#a900e8", "#9b9200", "#ff3600" };
//static const std::string PLAYER_APPRAISAL[] = { "", "E级", "D级", "C级", "B级", "A级", "S级" };
static const std::string PLAYER_APPRAISAL[] = { "", "100048", "100049", "100050", "100051", "100052", "100053" };
//static const std::string PLAYER_APPOINTMENT_STATE[] = { "休息", "遣散", "船长室任命中", "经营室任命中", "航海室任命中", "训练中" };
static const std::string PLAYER_APPOINTMENT_STATE[] = { "100054", "100055", "100056", "100057", "100058", "100059" };
static const std::string SHIPROOM_UIFILE[] = { "", "UI_Other_13", "UI_Other_14", "", "UI_Other_12", "UI_Other_15", "", "UI_Other_16", "UI_Other_11" };
static int SHIPROOM_CHECKBOX_TAG[] = { 0, 677, 682, 0, 672, 687, 0, 692, 667 };
static int SHIPROOM_NAME_TAG[] = { 0, 680, 685, 0, 675, 690, 0, 695, 670 };
static int SHIPROOM_LEVEL_TAG[] = { 0, 681, 686, 0, 676, 691, 0, 696, 671 };
//static const std::string NPC_NAME[] = { "", "总督", "交易所", "酒馆", "铁匠铺", "船坞", "码头" };
//static const std::string NPC_NAME[] = { "", "100011", "100012", "100013", "100014", "100015", "100016" };
//static const std::string NPC_TALK[] = { "", "勇敢的少年，见到你犹如见到了国家的希望。", "我的商品物美价廉，一分钱一分货。", "不喝酒怎么才能真汉子呢？", "", "船是有生命，船之精灵。", "大海是变化莫测的，需要多准备一点补给。" };
//static const std::string NPC_TALK[] = { "", "100001", "100002", "100003", "", "100004", "100005" };
//static const std::string TASK_TYPE[] = { "", "[主]", "[支]", "[探索]" };
static const std::string TASK_TYPE[] = { "", "100017", "100018", "100019" };
//static const std::string MISSION_STATE[] = { "（可接）", "（未完成）", "（已完成）", "（已交涉）" };
static const std::string MISSION_STATE[] = { "100006", "100007", "100008", "100009" };
//static const std::string CABINEFFECT_TYPE[] = { "", "提升统帅值", "提升经营值","", "提升航海值", "容纳人员", "", "训练人数", "货物载重" };
static const std::string CABINEFFECT_TYPE[] = { "", "100060", "100061", "", "100062", "100063", "", "100064", "100065" };

//static const std::string PLAYER_SHIP_LEVEL[] = { "所需主角等级:", "所需船长室等级:" };
static const std::string PLAYER_SHIP_LEVEL[] = { "100066", "100067" };

#define WEB_HOST GameData::getInstance()->server
#define ENTRY_SERVER GameData::getInstance()->entryServer
#define USING_NS_CC_UI using namespace ui
#define USING_NS_CC_CD using namespace CocosDenshion
#define USING_NS_CC_CCS using namespace cocostudio
#define USING_STD using namespace std

#define SOS_PLATFORM_XAMI 0
#define SOS_PLATFORM_ANDROID 1
#define SOS_PLATFORM SOS_PLATFORM_XAMI

#define SOS_SOS_FORMAT_JSON ".json"
#define SOS_SOS_FORMAT_BINARY ".csb"
#define SOS_CCS_FORMAT SOS_SOS_FORMAT_JSON

#define UTF8(__VALUE__) toUTF8(__VALUE__)
#define CUTF8(__VALUE__) toUTF8(__VALUE__).c_str()
#define INT(__VALUE__) cocos2d::Value(__VALUE__).asInt()
#define BOOL(__VALUE__) cocos2d::Value(__VALUE__).asBool()
#define FLOAT(__VALUE__) cocos2d::Value(__VALUE__).asFloat()
#define STRING(__VALUE__) cocos2d::Value(__VALUE__).asString()

#define SKILL_EFFECT_NAMES(__TYPE__) STRING(100030 + __TYPE__)
#define NPC_TALK(__TYPE__, __STATE__) STRING(600000 + __TYPE__)
#define NPC_NAME(__TYPE__) STRING(1000 + INT(__TYPE__))

#define SOUND_MAP "Sounds/bg_map.mp3"
#define SOUND_CITY "Sounds/bg_city.mp3"
#define SOUND_FIGHT "Sounds/bg_fight.mp3"
#define SOUND_COPIES "Sounds/bg_copies.mp3"
#define SOUND_EFFECT_SELL "Sounds/fx_sell.mp3"
#define SOUND_EFFECT_SHIPUP "Sounds/fx_shipup.mp3"
#define SOUND_EFFECT_RECRUIT "Sounds/fx_recruit.mp3"
#define SOUND_EFFECT_PAGE "Sounds/fx_page.mp3"
#define SOUND_EFFECT_LEVELUP "Sounds/fx_levelup.mp3"

#define GET_MAPVIEW_FILE(__TYPE__) \
	cocos2d::String::createWithFormat("UI_Map_%d", __TYPE__)->getCString()

#define GET_CITY_FILE(__TYPE__) \
	cocos2d::String::createWithFormat("scene_wg_%d.png", __TYPE__)->getCString()

#define GET_SHIP_FILE(__TYPE__) \
	cocos2d::String::createWithFormat("Ships/Ship%02d.ExportJson", __TYPE__)->getCString()

#define GET_SHIP_NAME(__TYPE__) \
	cocos2d::String::createWithFormat("Ship%02d", __TYPE__)->getCString()

// #define GET_PLAYER_FILE(__TYPE__, __APPRAISAL__) \
// 	cocos2d::String::createWithFormat("Players/Animation_%d_%d.ExportJson", __TYPE__, __APPRAISAL__ <= 3 ? 1 : __APPRAISAL__)->getCString()
// 
// #define GET_PLAYER_NAME(__TYPE__, __APPRAISAL__) \
// 	cocos2d::String::createWithFormat("Animation_%d_%d", __TYPE__, __APPRAISAL__ <= 3 ? 1 : __APPRAISAL__)->getCString()
// 
// #define GET_PLAYER_IDLE(__TYPE__, __APPRAISAL__) \
// 	cocos2d::String::createWithFormat("player_%d_%d_show_1.png", __TYPE__, __APPRAISAL__ <= 3 ? 1 : __APPRAISAL__)->getCString()

#define GET_PLAYER_FILE(__TYPE__, __APPRAISAL__) \
	cocos2d::String::createWithFormat("Players/Animation_%d_%d.ExportJson", __TYPE__, __APPRAISAL__)->getCString()

#define GET_PLAYER_NAME(__TYPE__, __APPRAISAL__) \
	cocos2d::String::createWithFormat("Animation_%d_%d", __TYPE__,__APPRAISAL__)->getCString()

#define GET_PLAYER_IDLE(__TYPE__, __APPRAISAL__) \
	cocos2d::String::createWithFormat("player_%d_%d_show_1.png", __TYPE__, __APPRAISAL__)->getCString()

#define GET_PLAYER_IMAGE(__TYPE__) \
	cocos2d::String::createWithFormat("player_%d_jn.png", __TYPE__)->getCString()

#define GET_NPC_IMAGE(__TYPE__) \
	cocos2d::String::createWithFormat("player_%s_jn.png", __TYPE__)->getCString()

#define GET_SKILL_ICON_FILE(__TYPE__) \
	cocos2d::String::createWithFormat("icon_skill_%d.png", __TYPE__)->getCString()

#define GET_TEAM_SKILL_BG(__TYPE__) \
	cocos2d::String::createWithFormat("fight_ShowID_%d.png", __TYPE__)->getCString()

#define GET_SKILL_FILE(__TYPE__, __MID__) \
	cocos2d::String::createWithFormat("Animations/Player_%s_%s.ExportJson", __TYPE__, __MID__)->getCString()

#define GET_SKILL_NAME(__TYPE__, __MID__) \
	cocos2d::String::createWithFormat("Player_%s_%s", __TYPE__, __MID__)->getCString()

#define GET_FB_FILE(__TYPE__) \
	cocos2d::String::createWithFormat("UI_FB_%d", __TYPE__)->getCString()

#define GET_PROP_ICON(__ID__) \
	cocos2d::String::createWithFormat("prop_icon_%d.png", __ID__)->getCString()

#define GET_PROP_ICON_MASK(__ID__) \
	cocos2d::String::createWithFormat("prop_icon_%d_mask.png", __ID__)->getCString()

#define GET_CARGO_ICON(__ID__) \
	cocos2d::String::createWithFormat("icon_item_%d.png", __ID__)->getCString()

#define GET_PLAYER_HEAD2(__ID__) \
	cocos2d::String::createWithFormat("ss_head2_id%d.png", __ID__)->getCString()

#define GET_SHIPROOM_BG(__ID__) \
	cocos2d::String::createWithFormat("ss_myship_cabin%d-2.png", __ID__)->getCString()

#define GET_SHIP_SHOP(__ID__) \
	cocos2d::String::createWithFormat("ss_shipshop_ship%d.png", __ID__)->getCString()

#define GET_PLAYER_ROLE(__ID__) \
	cocos2d::String::createWithFormat("ss_head3_id%d.png", __ID__)->getCString()

#define GET_PLAYER_STAR(__ID__) \
	cocos2d::String::createWithFormat("ss_partner_character1_%d.png", __ID__)->getCString()

#define GET_PLAYER_JOB(__ID__) \
	cocos2d::String::createWithFormat("ss_partner_job%d.png", __ID__)->getCString()

#define GET_PLAYER_HEAD(__ID__) \
	cocos2d::String::createWithFormat("player_%d_head.png", __ID__)->getCString()

#define GET_LOGIN_DAY(__ID__) \
	cocos2d::String::createWithFormat("ss_reward_%dday.png", __ID__)->getCString()

#define GET_PLAYER_BIAS(__BIAS__) \
	cocos2d::String::createWithFormat("ss_bias_%d.png", __BIAS__)->getCString()

#define FUNC_INSTANCE(__TYPE__) \
	static __TYPE__* getInstance();

#define FUNC_GET_INSTANCE(__TYPE__) \
	static __TYPE__* s_Shared##__TYPE__; \
	__TYPE__* __TYPE__::getInstance() \
{ \
if (!s_Shared##__TYPE__) \
{ \
	s_Shared##__TYPE__ = new __TYPE__(); \
} \
	return s_Shared##__TYPE__; \
}

#define FUNC_CREATE(__TYPE__) \
	static __TYPE__* create() \
{ \
	__TYPE__ *pRet = new __TYPE__(); \
if (pRet) \
{ \
	pRet->autorelease(); \
	return pRet; \
} \
		else \
{ \
	delete pRet; \
	pRet = NULL; \
	return NULL; \
} \
}

#define CCS_CREATE_SCENE(__SCENE_NAME__) \
	cocostudio::SceneReader::getInstance()->createNodeWithSceneFile(String::createWithFormat("%dx%d/%s%s", INT(cocos2d::Director::getInstance()->getWinSize().width), INT(cocos2d::Director::getInstance()->getWinSize().height), __SCENE_NAME__, SOS_CCS_FORMAT)->getCString())

#define CCS_CREATE_LAYER(__LAYER_NAME__) \
	cocostudio::GUIReader::getInstance()->widgetFromJsonFile(String::createWithFormat("%s%s", __LAYER_NAME__, SOS_CCS_FORMAT)->getCString());

#define CCS_GET_COMPONENT_FROM_SCENE(__SCENE__, __TYPE__, __NODE__, __TAG__) \
	__TYPE__ *__NODE__ = nullptr; \
	__NODE__ = static_cast<__TYPE__ *>(static_cast<cocostudio::ComRender *>(__SCENE__->getChildByTag(__TAG__)->getComponent("GUIComponent"))->getNode())

#define CCS_GET_ARMATURE_FROM_SCENE(__SCENE__, __TYPE__, __NODE__, __TAG__) \
	__TYPE__ *__NODE__ = nullptr; \
	__NODE__ = static_cast<__TYPE__ *>(static_cast<cocostudio::ComRender *>(__SCENE__->getChildByTag(__TAG__)->getComponent("CCArmature"))->getNode())

#define CCS_GET_COMPONENT(__LAYER__, __TYPE__, __NODE__) \
	__TYPE__ *__NODE__ = nullptr; \
	__NODE__ = static_cast<__TYPE__ *>(static_cast<cocostudio::ComRender *>(__LAYER__->getComponent("GUIComponent"))->getNode())

#define CCS_GET_CHILD(__LAYER__, __TYPE__, __NODE__,  __TAG__) \
	__TYPE__ *__NODE__ = nullptr; \
	__NODE__ = dynamic_cast<__TYPE__ *>(__LAYER__->getChildByTag(__TAG__))

#define CCS_GET_NODE(__LAYER__, __TYPE__, __NODE__,  __TAG__) \
	__TYPE__ *__NODE__ = nullptr; \
	__NODE__ = dynamic_cast<__TYPE__ *>(__LAYER__->getNodeByTag(__TAG__))

#define CCS_COMPONENT_FROM_SCENE(__SCENE__, __TYPE__, __TAG__) \
	static_cast<__TYPE__ *>(static_cast<cocostudio::ComRender *>(__SCENE__->getChildByTag(__TAG__)->getComponent("GUIComponent"))->getNode())

#define CCS_COMPONENT(__LAYER__, __TYPE__) \
	static_cast<__TYPE__ *>(static_cast<cocostudio::ComRender *>(__LAYER__->getComponent("GUIComponent"))->getNode())

#define CCS_CHILD(__LAYER__, __TYPE__,  __TAG__) \
	dynamic_cast<__TYPE__ *>(__LAYER__->getChildByTag(__TAG__))

#define CCS_NODE(__LAYER__, __TYPE__,  __TAG__) \
	dynamic_cast<__TYPE__ *>(__LAYER__->getNodeByTag(__TAG__))


static const std::string getTimer(int x)
{
	std::string timer = "";

	int h = x / 3600;//小时
	int m = (x - h * 3600) / 60;//分钟
	int s = x - h * 3600 - m * 60;//秒


	timer = cocos2d::String::createWithFormat("%d:%02d:%02d", h, m, s)->getCString();
	return timer;
}

static int get_date_now()
{
	struct tm *tm;
	time_t timep;
	time(&timep);
	tm = localtime(&timep);
	char time[64] = { 0 };
	sprintf(time, "%d-%02d-%02d %02d:%02d:%02d", (int)tm->tm_year + 1900,
		(int)tm->tm_mon + 1, (int)tm->tm_mday, (int)tm->tm_hour,
		(int)tm->tm_min, (int)tm->tm_sec);
	return timep;
}

static const cocos2d::Color3B GetEquipQualityColor(int quality)
{
	switch (quality)
	{
	case 1:
		return cocos2d::Color3B(20, 142, 11);
		break;
	case 2:
		return cocos2d::Color3B(0, 104, 220);
		break;
	case 3:
		return cocos2d::Color3B(132, 0, 255);
		break;
	case 4:
		return cocos2d::Color3B(255, 210, 0);
		break;
	case 5:
		return cocos2d::Color3B(255, 96, 0);
		break;
	default:
		return cocos2d::Color3B::WHITE;
		break;
	}
}

static const cocos2d::Color3B getAppraisalColor(int appraisal)
{
	switch (appraisal)
	{
	case 1:
		return cocos2d::Color3B(89, 89, 89);
		break;
	case 2:
		return cocos2d::Color3B(0, 133, 0);
		break;
	case 3:
		return cocos2d::Color3B(0, 82, 232);
		break;
	case 4:
		return cocos2d::Color3B(169, 0, 232);
		break;
	case 5:
		return cocos2d::Color3B(155, 146, 0);
		break;
	case 6:
		return cocos2d::Color3B(255, 54, 0);
		break;
	default:
		return cocos2d::Color3B::WHITE;
		break;
	}
}

static const cocos2d::Color3B getMissionState(int state)
{
	switch (state)
	{
	case 0:
		return cocos2d::Color3B(255, 240, 182);
		break;
	case 1:
		return cocos2d::Color3B(220, 220, 220);
		break;
	case 2:
		return cocos2d::Color3B(255, 240, 182);
		break;
	default:
		return cocos2d::Color3B::WHITE;
		break;
	}
}

static const std::string getNumberLabel(int number)
{
	if (number >= 10000)
	{
		int temp = number / 1000;
		if (temp % 10 == 0)
		{
			temp = temp / 10;
			return cocos2d::Value(temp).asString() + "W";
		}
		else
		{
			float temp2 = FLOAT(temp) / 10;
			return cocos2d::Value(temp2).asString() + "W";
		}
	}

	return cocos2d::Value(number).asString();
}

static const std::vector<std::string> split(std::string str, std::string pattern)
{
	std::string::size_type pos;
	std::vector<std::string> result;
	if (str.size() == 0)
	{
		return result;
	}
	str += pattern;
	std::string::size_type size = str.size();

	for (std::string::size_type i = 0; i < size; i++)
	{
		pos = str.find(pattern, i);
		if (pos < size)
		{
			auto s = str.substr(i, pos - i);
			result.push_back(s);
			i = pos + pattern.size() - 1;
		}
	}

	return result;
}

static const std::vector<int> splitToInt(std::string str, std::string pattern)
{
	std::string::size_type pos;
	std::vector<int> result;
	if (str.size() == 0)
	{
		return result;
	}
	str += pattern;
	std::string::size_type size = str.size();

	for (std::string::size_type i = 0; i < size; i++)
	{
		pos = str.find(pattern, i);
		if (pos < size)
		{
			auto s = str.substr(i, pos - i);
			result.push_back(INT(s));
			i = pos + pattern.size() - 1;
		}
	}

	return result;
}

static const std::string toUTF8(std::string str)
{
	//#if (CC_TARGET_PLATFORM == CC_PLATFORM_WIN32)
	//	int nwLen = MultiByteToWideChar(CP_ACP, 0, str.c_str(), -1, NULL, 0);
	//
	//	wchar_t * pwBuf = new wchar_t[nwLen + 1];//一定要加1，不然会出现尾巴 
	//	ZeroMemory(pwBuf, nwLen * 2 + 2);
	//
	//	MultiByteToWideChar(CP_ACP, 0, str.c_str(), str.length(), pwBuf, nwLen);
	//
	//	int nLen = WideCharToMultiByte(CP_UTF8, 0, pwBuf, -1, NULL, NULL, NULL, NULL);
	//
	//	char * pBuf = new char[nLen + 1];
	//	ZeroMemory(pBuf, nLen + 1);
	//
	//	WideCharToMultiByte(CP_UTF8, 0, pwBuf, nwLen, pBuf, nLen, NULL, NULL);
	//
	//	std::string retStr(pBuf);
	//
	//	delete[]pwBuf;
	//	delete[]pBuf;
	//
	//	pwBuf = NULL;
	//	pBuf = NULL;
	//
	//	return retStr;
	//#else
	return str;
	//#endif
}

static const std::string toUTF8(const char* str)
{
	//#if (CC_TARGET_PLATFORM == CC_PLATFORM_WIN32)
	//	int nwLen = MultiByteToWideChar(CP_ACP, 0, str, -1, NULL, 0);
	//
	//	wchar_t * pwBuf = new wchar_t[nwLen + 1];//一定要加1，不然会出现尾巴 
	//	ZeroMemory(pwBuf, nwLen * 2 + 2);
	//
	//	MultiByteToWideChar(CP_ACP, 0, str, strlen(str), pwBuf, nwLen);
	//
	//	int nLen = WideCharToMultiByte(CP_UTF8, 0, pwBuf, -1, NULL, NULL, NULL, NULL);
	//
	//	char * pBuf = new char[nLen + 1];
	//	ZeroMemory(pBuf, nLen + 1);
	//
	//	WideCharToMultiByte(CP_UTF8, 0, pwBuf, nwLen, pBuf, nLen, NULL, NULL);
	//
	//	std::string retStr(pBuf);
	//
	//	delete[]pwBuf;
	//	delete[]pBuf;
	//	
	//	pwBuf = NULL;
	//	pBuf = NULL;
	//
	//	return retStr;
	//#else
	std::string temp = "";
	std::string retStr = temp + str;
	return retStr;
	//#endif
}

static const std::string EVENT_PAY_MONEY_CALLBACK[] = { "EVENT_PAY_MONEY_CANCEL", "EVENT_PAY_MONEY_SUCCESS", "EVENT_PAY_MONEY_FAIL" };

static void payMoneyCallback(int state, std::string billing, std::string tradeId)
{
	cocos2d::Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_PAY_MONEY_CALLBACK[state], &tradeId);
}

#endif /* defined(__TestSOSCpp__SOSConfig__) */

