#include "GlobalSchedule.h"
#include "GameData.h"

#define SCHEDULE Director::sharedDirector()->getInstance()->getScheduler()

GlobalSchedule* GlobalSchedule::m_pSchedule = NULL;

GlobalSchedule::GlobalSchedule(float fInterval, float fDelay) {
	CCLog("GlobalSchedule()");
	SCHEDULE->scheduleSelector(
		schedule_selector(GlobalSchedule::globalUpdate), this, fInterval,
		false);
	m_pSchedule = this;
}

GlobalSchedule::~GlobalSchedule() {
	CCLog("GlobalSchedule().~()");
}

void GlobalSchedule::globalUpdate(float dt) {
	// 这里写全局定时器的逻辑处理代码
	//CCLog("global update");

	int time = GameData::getInstance()->applicationWillEnterForegroundTime - GameData::getInstance()->applicationDidEnterBackgroundTime;

	if (GameData::getInstance()->shiproomTime > 0){
		if (time > 0)
			GameData::getInstance()->shiproomTime = GameData::getInstance()->shiproomTime - time;
		else
			GameData::getInstance()->shiproomTime--;
		CCLog("shiproomTime:%d", GameData::getInstance()->shiproomTime);
	}
	if (GameData::getInstance()->skillTime > 0){
		if (time > 0)
			GameData::getInstance()->skillTime = GameData::getInstance()->skillTime - time;
		else
			GameData::getInstance()->skillTime--;
		CCLog("skillTime:%d", GameData::getInstance()->skillTime);
	}
	if (GameData::getInstance()->jjcTime > 0){
		if (time > 0)
			GameData::getInstance()->jjcTime = GameData::getInstance()->jjcTime - time;
		else
			GameData::getInstance()->jjcTime--;
		CCLog("jjcTime:%d", GameData::getInstance()->jjcTime);
	}


	if (GameData::getInstance()->getUserData()->getInfo().lastFightTime > 0){
		if (time > 0)
			GameData::getInstance()->getUserData()->getInfo().lastFightTime = GameData::getInstance()->getUserData()->getInfo().lastFightTime - time;
		else
			GameData::getInstance()->getUserData()->getInfo().lastFightTime--;
		//CCLog("lastFightTime:%d", GameData::getInstance()->GameData::getInstance()->getUserData()->getInfo().lastFightTime);
	}
	else
	{
		GameData::getInstance()->getUserData()->getInfo().mobility = GameData::getInstance()->getUserData()->getInfo().mobility + 60 >= 120 ? 120 : GameData::getInstance()->getUserData()->getInfo().mobility + 60;
		GameData::getInstance()->getUserData()->getInfo().lastFightTime = 3600;
		Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_MOBILITY_CHANGED, NULL);
	}

	GameData::getInstance()->applicationWillEnterForegroundTime = 0;
	GameData::getInstance()->applicationDidEnterBackgroundTime = 0;
}

void GlobalSchedule::start(float fInterval, float fDelay) {
	CCLog("GlobalSchedule().start()");
	new GlobalSchedule(fInterval, fDelay);
}

void GlobalSchedule::stop() {
	CCLog("GlobalSchedule().stop()");

	//CCAssert(m_pSchedule, "未定义");
	CC_SAFE_DELETE(m_pSchedule);
}

void GlobalSchedule::pause() {
	CCLog("GlobalSchedule().pause()");

	//CCAssert(m_pSchedule, "未定义");
	SCHEDULE->pauseTarget(m_pSchedule);
}

void GlobalSchedule::resume() {
	CCLog("GlobalSchedule().resume()");

	//CCAssert(m_pSchedule, " 未定义");
	SCHEDULE->resumeTarget(m_pSchedule);
}