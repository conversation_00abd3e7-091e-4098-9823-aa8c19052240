#include "SoundService.h"
#include "SimpleAudioEngine.h"

USING_NS_CC;
USING_NS_CC_CD;

FUNC_GET_INSTANCE(SoundService);

void SoundService::playBGM(std::string sound, bool init)
{
	if (init)
	{
		_sound = sound;
	}
	SimpleAudioEngine::getInstance()->playBackgroundMusic(sound == "" ? _sound.c_str() : sound.c_str(), true);
}

void SoundService::playEAX(std::string sound)
{
	SimpleAudioEngine::getInstance()->playEffect(sound.c_str());
	SimpleAudioEngine::getInstance()->stopBackgroundMusic();
}

void SoundService::playSFX(std::string sound)
{
	SimpleAudioEngine::getInstance()->playEffect(sound.c_str());
}

void SoundService::unload(std::string sound)
{
	SimpleAudioEngine::getInstance()->unloadEffect(sound.c_str());
}

void SoundService::openOrclose(bool b)
{
	if (b)
	{
		//SimpleAudioEngine::getInstance()->resumeBackgroundMusic();
		SimpleAudioEngine::getInstance()->resumeAllEffects();
		SimpleAudioEngine::getInstance()->setBackgroundMusicVolume(1);
		SimpleAudioEngine::getInstance()->setEffectsVolume(1);
	}
	else
	{
		//SimpleAudioEngine::getInstance()->stopBackgroundMusic();
		SimpleAudioEngine::getInstance()->stopAllEffects();
		SimpleAudioEngine::getInstance()->setBackgroundMusicVolume(0);
		SimpleAudioEngine::getInstance()->setEffectsVolume(0);
	}
	
}