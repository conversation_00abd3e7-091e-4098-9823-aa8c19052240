#ifndef __SOS_BASE_SCENE__
#define __SOS_BASE_SCENE__

#include "cocos2d.h"

enum TagBaseScene
{
	SCENE = 900001,
	LAYER = 900002,
	ALERT = 900003,

	NPC_VIEW = 500001,
	BUY_SHIP_VIEW = 500002,
	SHIP_APPOINT_VIEW = 500003,
	RECRUIT_VIEW = 500004,
	SHIP_UPGRADE_VIEW = 500005,
	SHOP_CARGOS_BUY_VIEW = 500006,
	SHOP_CARGOS_VIEW = 500007,
	SHOP_CARGOS_MINE_VIEW = 500008,
	SHOP_WEAPON_BUY_VIEW = 500009,
	SHOP_WEAPON_VIEW = 500018,
	PERSON_BAG_VIEW = 500010,
	PERSON_BAG_EQUIP_VIEW = 500011,
	PERSON_SKILL_VIEW = 500015,
	COPIES_VIEW = 500012,
	FIGHT_SUCCESS_VIEW = 500013,
	COPIES_REWARD_VIEW = 500014,
	SIGNIN_VIEW = 500016,
	SIGNIN_ALERT_VIEW = 500017,
};

class BaseScene : public cocos2d::Layer
{
public:
	CREATE_FUNC(BaseScene);

public:
	virtual bool init() override;
	virtual void onEnter() override;

	void onEventGuidance(float dt);
};

#endif