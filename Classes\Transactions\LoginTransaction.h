﻿//
//  LoginTransaction.h
//  TestSOS
//
//  Created by <PERSON><PERSON> on 13-12-20.
//
//

#ifndef __TestSOS__LoginTransaction__
#define __TestSOS__LoginTransaction__

#include "cocos2d.h"
#include "../SOSConfig.h"

static const char* EVENT_GAME_VERIFY_SUCCESS = "event_game_verify_success";
static const char* EVENT_GAME_LOGIN_SUCCESS = "event_game_login_success";
static const char* EVENT_GAME_LOGIN_CREATE = "event_game_login_create";
static const char* EVENT_GAME_VERSION = "event_game_version";
static const char* EVENT_GAME_NO_VERSION = "event_game_no_version";
static const char* EVENT_GAME_LOGINREWARD = "event_game_loginreward";
static const char* EVENT_GAME_PAY_MONEY_SUCCESS = "event_game_pay_money_success";

class LoginTransaction : public cocos2d::Ref
{
public:
	FUNC_INSTANCE(LoginTransaction);

public:
    LoginTransaction();
    virtual ~LoginTransaction();
    
	void verify(std::string token);
	void login(std::string uid);
	void bind(std::string token, std::string uid);
	void cmLogin(std::string cmUID);
	void getVersion(std::string version);
	void getLoginReward();
	void payMoney(std::string billing, std::string tradeId);
	void updateInfo();
};

#endif /* defined(__TestSOS__LoginTransaction__) */
