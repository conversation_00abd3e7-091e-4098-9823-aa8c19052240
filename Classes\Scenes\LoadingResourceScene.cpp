#include "LoadingResourceScene.h"
#include "../GameData.h"
#include "../Scenes/CityScene.h"
#include "../Transactions/GovernmentTransaction.h"
#include "../Transactions/MissionTransaction.h"
#include "../Transactions/ShopTransaction.h"
#include "../Services/NewguidanceService.h"
#include "cocostudio/CocoStudio.h"

USING_NS_CC;
USING_NS_CC_CCS;

bool r_city_cloud;

LoadingResourceScene::LoadingResourceScene()
{

}

LoadingResourceScene::~LoadingResourceScene()
{
	CCLOG("loading resources clean .... ");

	this->getEventDispatcher()->removeCustomEventListeners(EVENT_ENTER_SHOP_SUCCESS);
	this->getEventDispatcher()->removeCustomEventListeners(EVENT_CHECKPOINT_QUERY_SUCCESS);
}

Scene* LoadingResourceScene::createScene()
{
	return LoadingResourceScene::createScene(true);
}

Scene* LoadingResourceScene::createScene(bool cloud)
{
	r_city_cloud = cloud;

	auto scene = Scene::create();
	auto layer = LoadingResourceScene::create();
	scene->addChild(layer);
	return scene;
}

bool LoadingResourceScene::init()
{
	if (!Layer::init())
	{
		return false;
	}

	if (r_city_cloud)
	{
		auto size = Director::getInstance()->getWinSize();
		auto sprite = Sprite::create("ss_loadingcloud_new.png");
		sprite->setPosition(size.width / 2, size.height / 2);
		this->addChild(sprite, 0, 99999);
	}

	return true;
}

void LoadingResourceScene::onEnter()
{
	auto userData = GameData::getInstance()->getUserData();

	this->getEventDispatcher()->addCustomEventListener(EVENT_CHECKPOINT_QUERY_SUCCESS, [=](EventCustom* event)
	{
		CCLOG("[EVENT] query copies ...");
		ShopTransaction::getInstance()->enter(userData->getInfo().city);
	});

	this->getEventDispatcher()->addCustomEventListener(EVENT_ENTER_SHOP_SUCCESS, [=](EventCustom* event){
		auto cargos = static_cast<std::vector<UserData::Cargo> *>(event->getUserData());

		auto &myCargos = userData->getCargos();
		for (auto &mine : myCargos)
		{
			for (auto &cargo : *cargos)
			{
				if (mine.mid == cargo.mid)
				{
					mine.buy = cargo.buy;
					mine.sell = cargo.sell;
					break;
				}
			}
		}

		auto city = GameData::getInstance()->getCity(userData->getInfo().city);
		city->setCargos(*cargos);

		ArmatureDataManager::getInstance()->removeArmatureFilesInfo();

		Director::getInstance()->replaceScene(CityScene::createScene(r_city_cloud));
	});

	auto city = GameData::getInstance()->getCity(userData->getInfo().city);
	std::vector<int> vec;
	for (auto copy : city->getCopies())
	{
		vec.push_back(INT(copy.first));
	}

	if (vec.size() > 0)
	{
		GovernmentTransaction::getInstance()->checkpointQuery(userData->getInfo().uid, vec);
	}
	else
	{
		ShopTransaction::getInstance()->enter(userData->getInfo().city);
	}

	Layer::onEnter();
}

