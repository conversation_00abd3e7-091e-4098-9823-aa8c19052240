#ifndef __SHIPROOMUPGRADE_VIEW_H__
#define __SHIPROOMUPGRADE_VIEW_H__

#include "cocos2d.h"
#include "ui/CocosGUI.h"
#include "cocostudio/CocoStudio.h"
#include "../SOSConfig.h"
#include "../GameData.h"
#include "PopupView.h"

static const std::string EVENT_CABIN_UPGRADED = "event_cabin_upgraded";

class ShipRoomUpgradeView : public PopupView
{
public:
	CREATE_FUNC(ShipRoomUpgradeView);
	static cocos2d::Layer* createScene();

public:
	ShipRoomUpgradeView();
	virtual ~ShipRoomUpgradeView();
	virtual bool init() override;
	virtual void onEnter() override;
	void menuCloseCallback(cocos2d::Ref* pSender);
	void touchButton(cocos2d::Ref* object, cocos2d::ui::TouchEventType type);

	void showEffect();
	void showEffect(cocos2d::ui::CheckBox* cb);
	std::string getEffect(int type, std::string value);
	virtual void update(float dt);

private:
	cocos2d::Node* _build;
	ShipData* _shipData;

	int _cd;
	bool isUp;
};

#endif