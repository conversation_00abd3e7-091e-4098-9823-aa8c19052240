﻿#include "PartnerScene.h"
#include "CityScene.h"
#include "PersonScene.h"
#include <typeinfo>
#include "../Services/NewguidanceService.h"
#include "../Services/LocalizeService.h"
#include "../Views/AlertView.h"
#include "../Services/SoundService.h"

USING_NS_CC;
USING_NS_CC_CCS; 
USING_NS_CC_UI;

enum tagPartner
{
	UI_SCENE_BUTTON = 11000,

	UI_Person_1 = 10004,	
	UI_Person_2 = 10006,	
	UI_BUTTON_CLOSE = 298,	//关闭
	UI_BUTTON_ADD = 99,		//增加钻石
	UI_SCROLLVIEW = 100,	//滚动层
	UI_TEXT_MONEY = 97,
	UI_TEXT_DIAMOND = 98,
	UI_TEXT_PERSON = 96,

	UI_PERSONLIST_NAME = 102,
	UI_PERSONLIST_LEVEL = 103,
	UI_PERSONLIST_GRADE = 104,
	UI_PERSONLIST_PENAL = 105,
	UI_PERSONLIST_DELETE = 106,

};

Point partner_currentPoint;
Point partner_oldPoint;

bool moving;

PartnerScene::PartnerScene()
{
	_listener = EventListenerTouchOneByOne::create();
	_listener ->retain();
	//_listener->setSwallowTouches(true);
	_listener->onTouchBegan = CC_CALLBACK_2(PartnerScene::onTouchBegan, this);
	_listener->onTouchMoved = CC_CALLBACK_2(PartnerScene::onTouchMoved, this);
	_listener->onTouchEnded = CC_CALLBACK_2(PartnerScene::onTouchEnded, this);
}

PartnerScene::~PartnerScene()
{
	this->getEventDispatcher()->removeCustomEventListeners(EVENT_PARTNER_LEAVE_SUCCESS);
	_listener->release();
}

Scene* PartnerScene::createScene()
{
	// 'scene' is an autorelease object
	auto scene = Scene::create();

	// 'layer' is an autorelease object
	auto layer = PartnerScene::create();

	// add layer as a child to scene
	scene->addChild(layer);

	// return the scene
	return scene;
}

bool PartnerScene::init()
{
	if (!BaseScene::init())
	{
		return false;
	}

	node = CCS_CREATE_SCENE("Scene_PersonList");
	node->setTag(TagBaseScene::LAYER);
	addChild(node);

	auto userData = GameData::getInstance()->getUserData();

	CCS_GET_COMPONENT_FROM_SCENE(node, Layer, layer, UI_Person_1);
	CCS_GET_CHILD(layer, Button, addBtn, UI_BUTTON_ADD);
	CCS_GET_CHILD(layer, Text, cointext, UI_TEXT_MONEY);
	CCS_GET_CHILD(layer, Text, diamondtext, UI_TEXT_DIAMOND);

	addBtn->addTouchEventListener(this,toucheventselector(PartnerScene::touchButton));  
	cointext->setText(STRING(userData->getInfo().money));
	diamondtext->setText(STRING(userData->getInfo().diamond));
	
	CCS_GET_COMPONENT_FROM_SCENE(node, Layer, ui2, UI_SCENE_BUTTON);
	CCS_GET_CHILD(ui2, Button, closeBtn, UI_BUTTON_CLOSE);
	closeBtn->addTouchEventListener(this,toucheventselector(PartnerScene::touchButton));  

	initScrollview();

	Texture2D::setDefaultAlphaPixelFormat(Texture2D::PixelFormat::RGB888);
	auto imgBg = Sprite::create("ss_city_1280bg.png");
	auto size = Director::getInstance()->getWinSize();
	imgBg->setScale(size.width / 960, size.height / 640);
	imgBg->setAnchorPoint(Point(0, 0));
	this->addChild(imgBg, -1);
	Texture2D::setDefaultAlphaPixelFormat(Texture2D::PixelFormat::DEFAULT);


// 	if (GameData::getInstance()->isCreate)
// 		this->addChild(NewguidanceService::getInstance()->createLayer(), this->getChildrenCount() + 3);
	return true;
}

void PartnerScene::onEnter()
{
// 	if (GameData::getInstance()->getUserData()->getInfo().state == UserData::StateType::GUIDER)
// 	{
// 		NewguidanceService::getInstance()->createLayer(this);
// 	}
	BaseScene::onEnter();
}

void PartnerScene::menuCloseCallback(Ref* pSender)
{
	Director::getInstance()->end();

#if (CC_TARGET_PLATFORM == CC_PLATFORM_IOS)
	exit(0);
#endif
}

void PartnerScene::initScrollview()
{
	auto userData = GameData::getInstance()->getUserData();
	auto child = node->getChildByTag(UI_Person_1);  
	auto reader = (ComRender*)child->getComponent("GUIComponent");  
	auto layer = (Layer*)reader->getNode();  
	auto scrollview = dynamic_cast<ui::ScrollView*>(layer->getChildByTag(UI_SCROLLVIEW));
	scrollview->removeAllChildren();
	int width = 0;
	PlayerVector& players = userData->getPlayers();
	auto personTxt = dynamic_cast<Text*>(layer->getChildByTag(UI_TEXT_PERSON));
	int num = 0;
	for(auto player :players)
	{
		PlayerData::Info& playerInfo = player->getInfo();
		if(playerInfo.appoint!=1)
		{
			num++;
			CCLOG("player id: %s, mid: %d", playerInfo.id.c_str(), playerInfo.mid);
			auto person = CCS_CREATE_LAYER("UI_PersonList_2");
			CCS_GET_CHILD(person, Text, name, UI_PERSONLIST_NAME);
			CCS_GET_CHILD(person, Text, level, UI_PERSONLIST_LEVEL);
			CCS_GET_CHILD(person, ImageView, grade, UI_PERSONLIST_GRADE);
			CCS_GET_CHILD(person, ImageView, penal, UI_PERSONLIST_PENAL);
			CCS_GET_CHILD(person, Button, del, UI_PERSONLIST_DELETE);

			grade->loadTexture(GET_PLAYER_STAR(playerInfo.appraisal), TextureResType::PLIST);
			if (playerInfo.mid < 5)
			{
				del->setVisible(false);
				del->setTouchEnabled(false);
			}
			name->setText(playerInfo.name);
			name->setColor(getAppraisalColor(playerInfo.appraisal));
			level->setText("Lv."+UTF8(STRING(playerInfo.level)));
			penal->loadTexture(GET_PLAYER_IDLE(playerInfo.type, playerInfo.appraisal), TextureResType::PLIST);
			
// 			Armature* arm = Armature::create(GET_PLAYER_NAME(playerInfo.type, playerInfo.appraisal < 4 ? 1 : playerInfo.appraisal));
// 			arm->getAnimation()->play(PlayerAction::IDLE);
// 			arm->getAnimation()->setSpeedScale(1); 
// 			//arm->setPosition(Point(penal->getContentSize().width/2,penal->getContentSize().height/2));
// 			penal->addNode(arm);
			person->setUserObject(player);
			
			_eventDispatcher->addEventListenerWithSceneGraphPriority(_listener->clone(), person);
			del->setName(playerInfo.id.c_str());
			del->addTouchEventListener(this,toucheventselector(PartnerScene::touchButton));  
			scrollview->addChild(person);
			width = person->getContentSize().width;
		}
	}
	personTxt->setText(STRING(num) + "/" + STRING(userData->getMyShip()->getPlayersLimit()));
	scrollview->setInnerContainerSize(Size(width*num,scrollview->getContentSize().height));
	scrollview->addEventListenerScrollView(this, scrollvieweventselector(PartnerScene::touchScrollview));
}

void PartnerScene::touchButton(Ref* obj, TouchEventType eventType)
{
	auto button = dynamic_cast<Button*>(obj);
	int tag = button->getTag();
	std::string name = button->getName();
	switch (eventType)
	{
	case TouchEventType::TOUCH_EVENT_BEGAN:
		break;
	case TouchEventType::TOUCH_EVENT_ENDED:
		if (tag == UI_BUTTON_CLOSE)
		{
			Director::getInstance()->replaceScene(CityScene::createScene());
		}
		else if (tag == UI_PERSONLIST_DELETE)
		{
			auto userData = GameData::getInstance()->getUserData();		

			//for (auto item : userData->getQuickens())
			//{
			//	if (item->getInfo().type == QuickenData::QuickType::PLAYER)
			//	{
			//		if (get_date_now() - GameData::getInstance()->skillTime > item->getInfo().totalTime * 60)
			//		{
			//			MessageBox("技能升级冷却中..，不能移除伙伴", "Tips");
			//			return;
			//		}
			//	}
			//}

			PlayerVector& players = userData->getPlayers();
			for (auto player : players)
			{
				PlayerData::Info& playerInfo = player->getInfo();
				if (name == playerInfo.id)
				{

					if (playerInfo.appoint == PlayerData::AppointType::TRAINING)
					{
						this->addChild(AlertView::create(LocalizeService::getInstance()->getString("4015"), AlertType::IDLE), 999999);
					}
					else
					{
						PartnerTransaction::getInstance()->leavePlayer(userData->getInfo().uid, name, 0);
						playerInfo.appoint = 1;
						playerInfo.playType = PlayerData::PlayType::LEAVE;

						auto& positions = userData->getTeam()->getPositions();
						for (int i = 0; i < positions.size(); i++)
						{
							if (positions[i] == name)
							{
								positions[i] = TeamPosition::EMPTY;
								break;
							}
						}
					}
					break;
				}
			}
			
			initScrollview();
		}
		else if (tag == UI_BUTTON_ADD)
		{
			 this->addChild(AlertView::create(LocalizeService::getInstance()->getString("4001"), AlertType::IDLE), 999999);
		}
		break;
	}
}

void PartnerScene::touchScrollview(Ref* object, ScrollviewEventType type)
{
	switch (type)
	{
	case ScrollviewEventType::SCROLLVIEW_EVENT_SCROLLING:
		CCLOG("scroll view scrolling ...");
		moving = true;
		break;
	default:
		break;
	}
}

bool PartnerScene::onTouchBegan(Touch* touch, Event* event)  
{  
	partner_oldPoint = touch->getLocation();
	moving = false;
	CCLOG("ccTouchBegan");  
	return true;  
}  

void PartnerScene::onTouchMoved(Touch* touch, Event* event)
{  
	//partner_currentPoint = touch->getLocation();
	//moving = true;
	//CCLOG("ccTouchMoved");  
}  

void PartnerScene::onTouchEnded(Touch* touch, Event* event)
{
	auto target = static_cast<Widget *>(event->getCurrentTarget());
	partner_currentPoint = touch->getLocation();

	CCLOG("old %f, %f", partner_oldPoint.x, partner_oldPoint.y);
	CCLOG("new %f, %f", partner_currentPoint.x, partner_currentPoint.y);

	if (!moving || partner_currentPoint.getDistance(partner_oldPoint) <= 10)
	{
		Point locationInNode = target->convertToNodeSpace(touch->getLocation());  
		Size s = target->getContentSize();  
		Rect rect = Rect(0, 0, s.width, s.height);  
		if (rect.containsPoint(locationInNode))
		{
			SoundService::getInstance()->playSFX(SOUND_EFFECT_PAGE);
			Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_NEWGUIDANCE_TOUCHED, NULL);
			auto pl =dynamic_cast<PlayerData*>(target->getUserObject());
			Director::getInstance()->replaceScene(PersonScene::createScene(pl));
		}
	}
	CCLOG("ccTouchEnded");  
}  