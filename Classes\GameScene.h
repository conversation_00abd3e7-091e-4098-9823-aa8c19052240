#ifndef __GAME_SCENE_H__
#define __GAME_SCENE_H__

#include "cocos2d.h"

class GameScene : public cocos2d::Scene
{
public:
	CREATE_FUNC(GameScene);

public:
	virtual bool init() override;
};

class GameLayer : public cocos2d::Layer
{
public:
	CREATE_FUNC(GameLayer);

public:
	GameLayer();
	virtual ~GameLayer();

	virtual bool init() override;
	virtual void onEnter() override;

	void initConfigThread();
	void initConfigs(float dt);
};

#endif //__GAME_SCENE_H__