﻿#include "SettleScene.h"
#include "GovernmentScene.h"
#include "../Services/NewguidanceService.h"
#include "../Services/SoundService.h"
#include "../Services/EffectService.h"
#include "../Scenes/ArenaScene.h"
#include "../Views/WeaponView.h"
#include "../Views/ItemTipsView.h"
#include "../Views/Newguidance.h"

USING_NS_CC;
USING_NS_CC_CCS;
USING_NS_CC_UI;

enum tagSettle
{
	UI_SETTLE_1 = 10003,
	UI_NODE = 10008,
	UI_NOED_1 = 10004,
	UI_NOED_2 = 10005,
	UI_NOED_3 = 10006,
	UI_NOED_4 = 10007,
	UI_ATRMATURE_STAR = 10009,
	UI_ATRMATURE_SHINE = 10010,

	UI_NODE_IMAGEVIEW_REPLACE = 629,
	UI_NODE_TEXTBMFONT_NUMBER = 630,
	UI_NODE_IMAGEVIEW_QUALITY = 37000,

	UI_IMAGEVIEW_STARL = 595,
	UI_IMAGEVIEW_STARM = 596,
	UI_IMAGEVIEW_STARR = 597,
	UI_IMAGEVIEW_VICTORY = 598,

	UI_TEXT_NAME1 = 599,
	UI_TEXT_EXP1 = 600,
	UI_TEXT_NAME2 = 601,
	UI_TEXT_EXP2 = 602,
	UI_TEXT_NAME3 = 603,
	UI_TEXT_EXP3 = 604,
	UI_TEXT_NAME4 = 605,
	UI_TEXT_EXP4 = 606,
	UI_TEXT_NAME5 = 607,
	UI_TEXT_EXP5 = 608,

	UI_BUTTON_BACK = 625,
	UI_BUTTON_GO = 626,
	UI_BUTTON_SHARE = 627,

	UI_IMAGE_LIGHT = 20147,

	UI_IMAGEVIEW_REWARD = 1531,
	UI_TEXT_RANKID = 20010,
	UI_TEXT_RANKTEXT1 = 20011,
	UI_TEXT_RANKTEXT2 = 20012,
	UI_TEXT_RANKTEXT3 = 20013,
};

FightData* r_fight;

SettleScene::SettleScene()
{

}

SettleScene::~SettleScene()
{
	SoundService::getInstance()->unload("Sounds/ea_fight_fail.mp3");
	SoundService::getInstance()->unload("Sounds/ea_fight_victory.mp3");
	ArmatureDataManager::getInstance()->removeArmatureFileInfo("Animation_Star.ExportJson");
	ActionManagerEx::getInstance()->releaseActions();
}

Layer* SettleScene::createScene(FightData* fight)
{
	r_fight = fight;
	auto layer = SettleScene::create();
	return layer;
}

bool SettleScene::init()
{
	if (!PopupView::init())
	{
		return false;
	}

	_autoClose = false;

	if (r_fight->getInfo().scene == 0)
		jjcInit();
	else
		copiesInit();

	return true;
}

void SettleScene::onEnter()
{
	if (r_fight->getInfo().upgrade)
	{
		EffectService::getInstance()->play("Animation_Task", "Animation3");
		SoundService::getInstance()->playSFX(SOUND_EFFECT_LEVELUP);
	}

	if (GameData::getInstance()->getUserData()->getInfo().state == UserData::StateType::GUIDER)
	{
		Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_GUIDANCE_FINISHED);
	}

	PopupView::onEnter();
}

void SettleScene::touchButton(Ref* obj, cocos2d::ui::TouchEventType eventType)
{
	auto userData = GameData::getInstance()->getUserData();
	auto button = dynamic_cast<Widget*>(obj);
	int tag = button->getTag();
	switch (eventType)
	{
	case TouchEventType::TOUCH_EVENT_BEGAN:
		break;
	case TouchEventType::TOUCH_EVENT_ENDED:
		if (tag == UI_BUTTON_BACK)
		{
		}
		else if (tag == UI_BUTTON_GO)
		{
			Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_SETTLE_CHANGED);
			if (r_fight->getInfo().scene > 0)	//关卡
				Director::getInstance()->replaceScene(GovernmentScene::createScene(r_fight->getSettles()[GameData::RewardType::COPIES_ID]));
			else	//竞技场
				Director::getInstance()->replaceScene(ArenaScene::createScene());
		}
		else if (tag == UI_BUTTON_SHARE)
		{

		}
		else if (tag == UI_NODE_IMAGEVIEW_REPLACE)
		{
			auto itemData = dynamic_cast<ItemData*>(button->getUserObject());
			if (itemData->getInfo().type == ItemData::ItemType::TASK)
				this->addChild(WeaponView::create(itemData, WeaponView::Type::BUY), 999999, 99999);
			else
				this->addChild(ItemTipsView::create(itemData, NULL), 999999, 99999);
		}
		break;
	}
}


void SettleScene::copiesInit()
{
	auto userData = GameData::getInstance()->getUserData();

	node = CCS_CREATE_SCENE("Scene_Fight");
	node->setTag(TagBaseScene::LAYER);
	this->addChild(node);

	CCS_GET_COMPONENT_FROM_SCENE(node, Layer, layer, UI_SETTLE_1);
	CCS_GET_CHILD(layer, Button, backBtn, UI_BUTTON_BACK);
	CCS_GET_CHILD(layer, Button, goBtn, UI_BUTTON_GO);
	CCS_GET_CHILD(layer, Button, shareBtn, UI_BUTTON_SHARE);
	goBtn->setPressedActionEnabled(true);
	backBtn->addTouchEventListener(this, toucheventselector(SettleScene::touchButton));
	goBtn->setVisible(false);
	goBtn->setTouchEnabled(false);
	goBtn->setPressedActionEnabled(true);
	goBtn->addTouchEventListener(this, toucheventselector(SettleScene::touchButton));
	shareBtn->addTouchEventListener(this, toucheventselector(SettleScene::touchButton));

	auto settles = r_fight->getSettles();

	auto teams = userData->getTeam();
	auto postions = teams->getPositions();
	auto players = userData->getPlayers();
	int x = 0;

	for (int i = 0; i < postions.size(); i++)
	{
		if (postions.at(i) != "0")
		{
			for (auto player : players)
			{
				if (player->getInfo().id == postions.at(i))
				{
					CCS_GET_CHILD(layer, Text, name, UI_TEXT_NAME1 + x * 2);
					CCS_GET_CHILD(layer, Text, exp, UI_TEXT_EXP1 + x * 2);

					name->setText(player->getInfo().name);
					exp->setText(UTF8("EXP+" + STRING(settles[-2])));
					x++;
					break;
				}
			}
		}
	}


	CCS_GET_CHILD(layer, ImageView, isWin, UI_IMAGEVIEW_VICTORY);

	CCS_GET_CHILD(node, Node, layout, UI_NODE);

	CCS_GET_CHILD(node, Node, starNode, UI_ATRMATURE_STAR);
	CCS_GET_CHILD(node, Node, shineNode, UI_ATRMATURE_SHINE);
	CCS_GET_ARMATURE_FROM_SCENE(node, Armature, starArmature, UI_ATRMATURE_STAR);
	CCS_GET_ARMATURE_FROM_SCENE(node, Armature, shineArmature, UI_ATRMATURE_SHINE);

	if (settles[GameData::RewardType::DoneEvaluate] == 3)
	{
		//star3->loadTexture("ss_war_end_star_1_right.png", TextureResType::PLIST);
		starArmature->getAnimation()->play("left_mid_right");
		starArmature->getAnimation()->setMovementEventCallFunc(CC_CALLBACK_0(SettleScene::onStartEvent, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3));
	}
	else if (settles[GameData::RewardType::DoneEvaluate] == 2)
	{
		//star2->loadTexture("ss_war_end_star_1_mid.png", TextureResType::PLIST);
		starArmature->getAnimation()->play("left_mid");
		starArmature->getAnimation()->setMovementEventCallFunc(CC_CALLBACK_0(SettleScene::onStartEvent, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3));
	}
	else if (settles[GameData::RewardType::DoneEvaluate] == 1)
	{
		//star1->loadTexture("ss_war_end_star_1_left.png", TextureResType::PLIST);
		starArmature->getAnimation()->play("left");
		starArmature->getAnimation()->setMovementEventCallFunc(CC_CALLBACK_0(SettleScene::onStartEvent, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3));
	}

	if (settles[GameData::RewardType::DoneEvaluate] > 0)
	{
		isWin->loadTexture("ss_war_end_v.png", TextureResType::PLIST);

		layout->setVisible(true);
		int i = 0;
		for (auto item : settles)
		{
			if (item.first > -2)
			{
				// 				auto c = layout->getChildByTag(UI_NOED_1 + i);
				// 				auto r = (ComRender*)c->getComponent("GUIComponent");
				// 				auto l = (Layer*)r->getNode();
				CCS_GET_COMPONENT_FROM_SCENE(layout, Layer, l, UI_NOED_1 + i);
				CCS_GET_CHILD(l, ImageView, img, UI_NODE_IMAGEVIEW_REPLACE);
				CCS_GET_CHILD(l, TextBMFont, num, UI_NODE_TEXTBMFONT_NUMBER);
				CCS_GET_CHILD(l, ImageView, quality, UI_NODE_IMAGEVIEW_QUALITY);

				

				if (item.first > 0)
				{
					ItemData* itemData = ItemData::create();
					auto itemInfo = GameData::getInstance()->getItem(item.first);
					img->loadTexture(GET_PROP_ICON(itemInfo.icon), TextureResType::PLIST);
					img->setVisible(true);
					num->setText(STRING(item.second).c_str());
					if (itemInfo.type == ItemData::ItemType::EQUIP&& itemInfo.quality > 0)
					{
						quality->loadTexture(GET_PROP_ICON_MASK(itemInfo.icon), TextureResType::PLIST);
						quality->setColor(GetEquipQualityColor(itemInfo.quality));
					}
					else
					{
						quality->setVisible(false);
					}

					img->setTouchEnabled(true);
					img->addTouchEventListener(this, toucheventselector(SettleScene::touchButton));				
					itemData->setInfo(itemInfo);
					img->setUserObject(itemData);
					i++;
				}
				else if (item.first == -1)
				{
					img->loadTexture("ss_ty_diamond.png", TextureResType::PLIST);
					img->setVisible(true);
					num->setText(STRING(item.second).c_str());
					i++;
				}
				else if (item.first == 0)
				{
					img->loadTexture("ss_ty_gold.png", TextureResType::PLIST);
					img->setVisible(true);
					num->setText(STRING(item.second).c_str());
					i++;
				}
			}
		}

		SoundService::getInstance()->playEAX("Sounds/ea_fight_victory.mp3");

		starNode->setVisible(true);
		shineNode->setVisible(true);
		shineArmature->getAnimation()->play("victorylight");
	}
	else
	{
		goBtn->setVisible(true);
		goBtn->setTouchEnabled(true);
		layout->setVisible(false);
		SoundService::getInstance()->playEAX("Sounds/ea_fight_fail.mp3");
	}
}

void SettleScene::jjcInit()
{
	auto userData = GameData::getInstance()->getUserData();

	node = CCS_CREATE_SCENE("Scene_Fight_2");
	addChild(node);

	CCS_GET_COMPONENT_FROM_SCENE(node, Layer, layer, UI_SETTLE_1);
	CCS_GET_CHILD(layer, Button, goBtn, UI_BUTTON_GO);
	goBtn->setPressedActionEnabled(true);
	goBtn->addTouchEventListener(this, toucheventselector(SettleScene::touchButton));

	CCS_GET_CHILD(node, Node, starNode, UI_ATRMATURE_STAR);
	CCS_GET_CHILD(node, Node, shineNode, UI_ATRMATURE_SHINE);
	CCS_GET_ARMATURE_FROM_SCENE(node, Armature, starArmature, UI_ATRMATURE_STAR);
	CCS_GET_ARMATURE_FROM_SCENE(node, Armature, shineArmature, UI_ATRMATURE_SHINE);

	CCS_GET_CHILD(layer, Text, rankidtxt1, UI_TEXT_RANKID);
	rankidtxt1->setText(STRING(GameData::getInstance()->getUserData()->getInfo().rankId));

	starArmature->getAnimation()->play("left_mid_right");
	SoundService::getInstance()->playEAX("Sounds/ea_fight_victory.mp3");

	starNode->setVisible(true);
	shineNode->setVisible(true);
	shineArmature->getAnimation()->play("victorylight");
}
void SettleScene::onStartEvent(Armature *armature, MovementEventType movementType, const std::string& movementID)
{
	if (movementType == COMPLETE)
	{
		CCS_GET_COMPONENT_FROM_SCENE(node, Layer, layer, UI_SETTLE_1);
		CCS_GET_CHILD(layer, Button, goBtn, UI_BUTTON_GO);
		goBtn->setTouchEnabled(true);
		goBtn->setVisible(true);
	}
}
