#ifndef __SOS_WEAPON_VIEW__
#define __SOS_WEAPON_VIEW__

#include "cocos2d.h"
#include "PopupView.h"
#include "ui/CocosGUI.h"
#include "../GameData.h"

static const std::string EVENT_UNLOAD_ITEM = "event_unload_item";


class WeaponView : public PopupView
{
public:
	enum class Type
	{
		IDLE,
		BUY,
		SELL,
		UNLOAD,
		USING,
	};

public:
	CREATE_FUNC(WeaponView);
	static cocos2d::Layer* create(ItemData* item, WeaponView::Type type);

public:
	virtual ~WeaponView();

	virtual bool init() override;
	virtual void onEnter() override;

	void initTips3();
	void initTips4();

	void touchButton(cocos2d::Ref* object, cocos2d::ui::TouchEventType type);
};

#endif