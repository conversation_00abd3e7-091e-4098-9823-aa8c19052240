﻿#include "DockScene.h"
#include "CityScene.h"
#include "../GameData.h"
#include "../Views/BuyDockView.h"
#include "../Datas/UserData.h"
#include "../Transactions/DockTransaction.h"
#include "../Transactions/ShipTransaction.h"
#include "../Services/LocalizeService.h"
#include "../Services/NewguidanceService.h"
#include "../Views/AlertView.h"
#include "../Transactions/LoginTransaction.h"
#include "../Platforms/CMHelper.h"

USING_NS_CC;
USING_NS_CC_CCS;
USING_NS_CC_UI;
USING_STD;

enum tagDock
{
	SCENE_PANEL_SHIP = 10007,

	UI_DOCK_1 = 10004,
	UI_DOCK_2 = 11000,
	//UI_CHECKBOX_BUYSHIP = 914,     
	//UI_CHECKBOX_MYSHIP = 915,		
	UI_BUTTON_CLOSE = 298,		
	UI_BUTTON_BUY = 920,		
	UI_BUTTON_USE = 37002,
	UI_SCROLLVIEW = 916,		
	UI_IMAGEVIEW_SHIPNAME = 917,
	UI_TEXT_NAME = 918,
	UI_PANEL = 919,
	UI_TEXT_MONEY = 910,
	UI_TEXT_DIAMOND = 913,
	UI_PANEL_ARMATIRE = 1400,

	OTHER9_BUTTON_BG = 657,
	OTHER9_IMAGEVIEW_COIN = 658,
	OTHER9_TEXTBMFONT_PRICE = 659,
	OTHER9_IMAGEVIEW_COINBG = 660,
	OTHER9_IMAGEVIEW_SHIP = 661,
	OTHER9_IMAGEVIEW_NOTOPEN = 662,
	OTHER9_TEXT_LEVEL = 663,
	OTHER9_IMAGEVIEW_USING = 20147,

	OTHER10_TEXT_NAME = 665,
	OTHER10_TEXT_NUMBER = 666,

	DOCK_SHIP = 100090,
	MY_SHIP = 100095,
};

std::vector<CheckBox*> r_dockShips;

DockScene::DockScene()
: _status(true)
{
	r_dockShips.clear();
}

DockScene::~DockScene()
{
	this->getEventDispatcher()->removeCustomEventListeners(EVENT_MONEY_CHANGED);
	this->getEventDispatcher()->removeCustomEventListeners(EVENT_BUYSHOPVIEW_CHANGED);
	
	r_dockShips.clear();

	Director::getInstance()->getTextureCache()->removeTextureForKey("ss_ty_bg1.png");
}

Scene* DockScene::createScene()
{
	auto scene = Scene::create();
	auto layer = DockScene::create();
	scene->addChild(layer);
	return scene;
}

bool DockScene::init()
{
	if (!BaseScene::init())
	{
		return false;
	}

	node = CCS_CREATE_SCENE("Scene_Dock");
	node->setTag(TagBaseScene::LAYER);
	addChild(node);
	auto userData = GameData::getInstance()->getUserData();

	CCS_GET_COMPONENT_FROM_SCENE(node, Layer, layer, UI_DOCK_1);
	CCS_GET_CHILD(layer, Button, buyBtn, UI_BUTTON_BUY);
	CCS_GET_CHILD(layer, Button, useBtn, UI_BUTTON_USE);
	CCS_GET_CHILD(layer, Text, cointext, UI_TEXT_MONEY);
	CCS_GET_CHILD(layer, Text, diamondtext, UI_TEXT_DIAMOND);
	CCS_GET_CHILD(layer, Text, nameTxt, UI_TEXT_NAME);
	CCS_GET_CHILD(layer, ImageView, shipnameIV, UI_IMAGEVIEW_SHIPNAME);

	buyBtn->setPressedActionEnabled(true);
	buyBtn->setVisible(false);
	buyBtn->setTouchEnabled(false);
	buyBtn->addTouchEventListener(this, toucheventselector(DockScene::touchButton));
	useBtn->setPressedActionEnabled(true);
	useBtn->setVisible(false);
	useBtn->setTouchEnabled(false);
	useBtn->addTouchEventListener(this, toucheventselector(DockScene::touchButton));

	cointext->setText(STRING(userData->getInfo().money));
	diamondtext->setText(STRING(userData->getInfo().diamond));
	nameTxt->setVisible(false);
	shipnameIV->setVisible(false);

	CCS_GET_COMPONENT_FROM_SCENE(node, Layer, layer1, UI_DOCK_2);
	CCS_GET_CHILD(layer1, Button, closeBtn, UI_BUTTON_CLOSE);
	closeBtn->addTouchEventListener(this, toucheventselector(DockScene::touchButton));

	initShips();

	Texture2D::setDefaultAlphaPixelFormat(Texture2D::PixelFormat::RGB888);
	auto imgBg = Sprite::create("ss_ty_bg1.png");
	auto size = Director::getInstance()->getWinSize();
	imgBg->setScale(size.width / 960, size.height / 640);
	imgBg->setAnchorPoint(Point(0, 0));
	this->addChild(imgBg, -1);
	Texture2D::setDefaultAlphaPixelFormat(Texture2D::PixelFormat::DEFAULT);

	return true;
}

void DockScene::initShips()
{
	r_dockShips.clear();

	auto user = GameData::getInstance()->getUserData();
	auto role = GameData::getInstance()->getPlayer(user->getInfo().role);

	CCS_GET_COMPONENT_FROM_SCENE(node, Layer, layer, UI_DOCK_1);
	CCS_GET_CHILD(layer, ScrollView, scrollView, UI_SCROLLVIEW);
	CCS_GET_CHILD(node, Node, shipNode, SCENE_PANEL_SHIP);
	shipNode->removeAllChildren();
	scrollView->removeAllChildren();

	auto width = 0;
	auto count = 0;

	auto city = GameData::getInstance()->getCity(user->getInfo().city);
	auto sids = city->getDock().ships;

	auto &ships = GameData::getInstance()->getShips();
	for (auto ship : ships)
	{
		auto other9 = CCS_CREATE_LAYER("UI_Other_9");
		width = other9->getContentSize().width;

		CCS_GET_CHILD(other9, ImageView, coinIV, OTHER9_IMAGEVIEW_COIN);
		CCS_GET_CHILD(other9, TextBMFont, priceTF, OTHER9_TEXTBMFONT_PRICE);
		CCS_GET_CHILD(other9, ImageView, shipIV, OTHER9_IMAGEVIEW_SHIP);
		CCS_GET_CHILD(other9, ImageView, notopenIV, OTHER9_IMAGEVIEW_NOTOPEN);
		CCS_GET_CHILD(other9, ImageView, coinIVbg, OTHER9_IMAGEVIEW_COINBG);
		CCS_GET_CHILD(other9, Text, levelTxt, OTHER9_TEXT_LEVEL);
		CCS_GET_CHILD(other9, CheckBox, bgBtn, OTHER9_BUTTON_BG);
		CCS_GET_CHILD(other9, ImageView, usingImage, OTHER9_IMAGEVIEW_USING);

		auto mine = user->getShips().at(ship.second->getInfo().mid);
		if (mine != nullptr)
		{
			coinIV->setVisible(false);
			priceTF->setVisible(false);
			notopenIV->setVisible(false);
			coinIVbg->setVisible(false);
			levelTxt->setVisible(false);
			notopenIV->setVisible(false);
			shipIV->loadTexture(GET_SHIP_SHOP(mine->getInfo().mid), TextureResType::PLIST);

			bgBtn->setUserObject(mine);
			bgBtn->setTag(MY_SHIP);
			bgBtn->addTouchEventListener(this, toucheventselector(DockScene::touchButton));

			usingImage->setVisible(mine->getInfo().use);

			r_dockShips.push_back(bgBtn);
			scrollView->addChild(other9);
			if (count == 0)
			{
				bgBtn->setSelectedState(true);
				this->createShipInfo(bgBtn);
			}
			count++;
			continue;
		}

		auto inCity = false;
		for (auto sid : sids)
		{
			if (sid != ship.second->getInfo().mid) continue;

			priceTF->setText(getNumberLabel(ship.second->getRequire().price).c_str());
			if (user->getInfo().money < ship.second->getRequire().price)
			{
				priceTF->setColor(Color3B(255, 0, 0));
			}

			shipIV->loadTexture(GET_SHIP_SHOP(ship.second->getInfo().mid), TextureResType::PLIST);

			levelTxt->setText("Lv" + STRING(ship.second->getRequire().level));

			bgBtn->setUserObject(ship.second);
			bgBtn->setTag(DOCK_SHIP);
			bgBtn->addTouchEventListener(this, toucheventselector(DockScene::touchButton));

			if (role->getInfo().level >= ship.second->getRequire().level)
			{
				levelTxt->setVisible(false);
				notopenIV->setVisible(false);
				priceTF->setVisible(true);
				coinIVbg->setVisible(true);
				coinIV->setVisible(true);
			}
			else
			{
				notopenIV->setVisible(true);
				notopenIV->setVisible(true);
				priceTF->setVisible(false);
				coinIVbg->setVisible(false);
				coinIV->setVisible(false);
			}
			if (count == 0)
			{
				bgBtn->setSelectedState(true);
				this->createShipInfo(bgBtn);
			} 
			count++;
			r_dockShips.push_back(bgBtn);
			scrollView->addChild(other9);

			break;
		}
	}

	scrollView->setInnerContainerSize(Size(width * count, scrollView->getContentSize().height));
}

void DockScene::createShipInfo(Widget* widget)
{
	auto ship = static_cast<ShipData*>(widget->getUserObject());

	CCS_GET_COMPONENT_FROM_SCENE(node, Layer, layer, UI_DOCK_1);
	CCS_GET_CHILD(layer, Layout, panel, UI_PANEL);
	CCS_GET_CHILD(layer, Text, nameTxt, UI_TEXT_NAME);
	CCS_GET_CHILD(layer, ImageView, shipnameIV, UI_IMAGEVIEW_SHIPNAME);
	CCS_GET_CHILD(layer, Button, buyBtn, UI_BUTTON_BUY);
	CCS_GET_CHILD(layer, Button, useBtn, UI_BUTTON_USE);

	if (widget->getTag() == DOCK_SHIP)
	{
		buyBtn->setVisible(true);
		buyBtn->setTouchEnabled(true);
		useBtn->setVisible(false);
		useBtn->setTouchEnabled(false);
	}
	else
	{
		buyBtn->setVisible(false);
		buyBtn->setTouchEnabled(false);
		useBtn->setVisible(!ship->getInfo().use);
		useBtn->setTouchEnabled(!ship->getInfo().use);
	}

	nameTxt->setVisible(true);
	nameTxt->setText(ship->getInfo().name);
	shipnameIV->setVisible(true);

	panel->removeAllChildren();
	panel->addChild(this->createShipAttri(0, widget->getTag(), ship->getCP()));
	panel->addChild(this->createShipAttri(1, widget->getTag(), ship->getOP()));
	panel->addChild(this->createShipAttri(2, widget->getTag(), ship->getTrainingLimit()));
	panel->addChild(this->createShipAttri(3, widget->getTag(), ship->getSpace()));
	panel->addChild(this->createShipAttri(4, widget->getTag(), ship->getPlayersLimit()));

	CCS_GET_CHILD(node, Node, shipNode, SCENE_PANEL_SHIP);
	shipNode->removeAllChildren();

	ArmatureDataManager::getInstance()->addArmatureFileInfo(GET_SHIP_FILE(ship->getInfo().mid));
	auto arm = Armature::create(GET_SHIP_NAME(ship->getInfo().mid));
	arm->getAnimation()->play("Face_g");
	arm->getAnimation()->setSpeedScale(1);
	arm->setScale(1.2f);
	shipNode->addChild(arm);
}

Widget* DockScene::createShipAttri(int content, int tag, int value)
{
	auto other = CCS_CREATE_LAYER("UI_Other_10");
	CCS_GET_CHILD(other, Text, contentLabel, OTHER10_TEXT_NAME);
	CCS_GET_CHILD(other, Text, valueLabel, OTHER10_TEXT_NUMBER);
	contentLabel->setText(LocalizeService::getInstance()->getString(STRING(content + tag)));
	valueLabel->setText(STRING(value));
	return other;
}

void DockScene::onEnter()
{
	this->getEventDispatcher()->addCustomEventListener(EVENT_MONEY_CHANGED, [this](EventCustom* event){
		auto money = static_cast<int *>(event->getUserData());
		CCS_GET_COMPONENT_FROM_SCENE(node, Layer, layer, UI_DOCK_1);
		CCS_GET_CHILD(layer, Text, coinText, UI_TEXT_MONEY);
		coinText->setText(STRING(*money));
		//GameData::getInstance()->isTaskComplete(GameData::MissionType::MONEY, 0, 0);
	});

	this->getEventDispatcher()->addCustomEventListener(EVENT_BUYSHOPVIEW_CHANGED, [this](EventCustom* event) {
		this->initShips();
	});

// 	if (GameData::getInstance()->getUserData()->getInfo().state == UserData::StateType::GUIDER)
// 	{
// 		NewguidanceService::getInstance()->createLayer(this);
// 	}

	BaseScene::onEnter();
}

void DockScene::touchButton(Ref* obj, cocos2d::ui::TouchEventType eventType)
{
	auto userData = GameData::getInstance()->getUserData();
	auto widget = dynamic_cast<Widget *>(obj);
	int tag = widget->getTag();

	switch (eventType)
	{
	case TouchEventType::TOUCH_EVENT_ENDED:
		CCS_GET_COMPONENT_FROM_SCENE(node, Layer, layer, UI_DOCK_1);
		CCS_GET_CHILD(layer, Layout, panel, UI_PANEL);
		if (tag == UI_BUTTON_CLOSE)
		{
			Director::getInstance()->replaceScene(CityScene::createScene());
		}
		else if (tag == UI_BUTTON_BUY)
		{
			for (auto item : r_dockShips)
			{
				if (item->getSelectedState())
				{
					auto role = userData->getPlayer(userData->getInfo().role);
					auto ship = static_cast<ShipData*>(item->getUserObject());
					if (role->getInfo().level >= ship->getRequire().level)
						this->addChild(BuyDockView::createScene(ship->getInfo().mid, ship->getRequire().price), this->getChildrenCount() + 1, TagBaseScene::BUY_SHIP_VIEW);
					else
						this->addChild(AlertView::create(LocalizeService::getInstance()->getString("4003"), AlertType::IDLE), 999999);
					break;
				}
			}
		}
		else if (tag == UI_BUTTON_USE)
		{
			for (auto item : r_dockShips)
			{
				if (item->getSelectedState())
				{
					auto shipData = dynamic_cast<ShipData*>(item->getUserObject());
					int num = 0;
					for (auto player : userData->getPlayers())
					{
						if (player->getInfo().appoint != PlayerData::AppointType::LEAVING)
						{
							num++;
						}
					}
					if (shipData->getPlayersLimit() < num)
					{
						this->addChild(AlertView::create(LocalizeService::getInstance()->getString("4013"), AlertType::IDLE), 999999);
						break;
					}

					//if (get_date_now() < GameData::getInstance()->shiproomTime)
					if (GameData::getInstance()->shiproomTime>30*60)
					{
						this->addChild(AlertView::create(LocalizeService::getInstance()->getString("608"), AlertType::IDLE), 999999);
						break;
					}

					//使用船只
					if (userData->getCargos().size() > 0)
					{
						this->addChild(AlertView::create(LocalizeService::getInstance()->getString("4006"), AlertType::IDLE), 999999);
						break;
					}

					ShipTransaction::getInstance()->use(userData->getInfo().uid, shipData->getInfo().id);
					auto myShips = userData->getShips();
					for (auto ship : myShips)
					{
						for (auto cabin : ship.second->getCabins())
						{
							cabin->getInfo().appoint = "";
						}

						if (ship.first == shipData->getInfo().mid)
						{
							ship.second->getInfo().use = true;
							Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_BUYSHOPVIEW_CHANGED, NULL);
						}
						else
						{
							ship.second->getInfo().use = false;
						}
					}

					for (auto player : userData->getPlayers())
					{
						if (player->getInfo().appoint == 2 || player->getInfo().appoint == 3 || player->getInfo().appoint == 4)
						{
							player->getInfo().appoint = 0;
						}
					}

					initShips();

					LoginTransaction::getInstance()->cmLogin(CMHelper::GetCMUserID());
					LoginTransaction::getInstance()->bind(GameData::getInstance()->token, GameData::getInstance()->getUserData()->getInfo().uid);
					UserDefault::getInstance()->setStringForKey("Token", GameData::getInstance()->token);
					UserDefault::getInstance()->flush();

					Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_NEWGUIDANCE_START);
					break;
				}
			}
		}
		else
		{
			for (auto item : r_dockShips)
			{
				if (item == dynamic_cast<CheckBox*>(widget))
				{
					//item->setSelectedState(true);
					item->setTouchEnabled(false);
				}
				else
				{
					item->setSelectedState(false);
					item->setTouchEnabled(true);
				}
			}
			this->createShipInfo(widget);
		}
		break;
	}
}




