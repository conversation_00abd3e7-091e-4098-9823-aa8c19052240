#ifndef __TRAINING_VIEW_H__
#define __TRAINING_VIEW_H__

#include "cocos2d.h"
#include "ui/CocosGUI.h"
#include "cocostudio/CocoStudio.h"
#include "../SOSConfig.h"
#include "../GameData.h"
#include "PopupView.h"

class TrainingView : public PopupView
{
public:
	CREATE_FUNC(TrainingView);
	static cocos2d::Layer* createScene();

public:
	TrainingView();
	virtual ~TrainingView();
	virtual bool init() override;
	virtual void onEnter() override;
	void menuCloseCallback(cocos2d::Ref* pSender);
	void touchButton(cocos2d::Ref* object, cocos2d::ui::TouchEventType type);
private:
	int _trainType;
	cocos2d::Node* _training;
	cocos2d::ui::Widget* _layer1;
	cocos2d::ui::Widget* _layer2;

	int _cd;
};

#endif