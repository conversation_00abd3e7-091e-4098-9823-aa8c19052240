#ifndef __SOS_POPUP_VIEW__
#define __SOS_POPUP_VIEW__

#include "cocos2d.h"

class PopupView : public cocos2d::Layer
{
public:
	CREATE_FUNC(PopupView);

public:
	PopupView() : _autoClose(true){};
	virtual ~PopupView() {};

	virtual bool init() override;
	virtual void onEnter() override;

	void setCenter();
	void setCenter(cocos2d::Node* node);
	void onEventGuidance(float dt);

	inline bool getAutoClose() { return _autoClose; }
	inline void setAutoClose(bool autoClose) { _autoClose = autoClose; }

protected:
	bool _autoClose;
};

#endif