#ifndef __SOS_EFFECT_SERVICE__
#define __SOS_EFFECT_SERVICE__

#include "cocos2d.h"
#include "../SOSConfig.h"
#include "cocostudio/CocoStudio.h"

class EffectService : public cocos2d::Node
{
public:
	FUNC_INSTANCE(EffectService);

public:
	void play(std::string name, std::string animation);
	void onAnimationEvent(cocostudio::Armature *armature, cocostudio::MovementEventType movementType, const std::string& movementID);
	void cleanup(float dt);
};

#endif