#ifndef __COPIESREWARD_VIEW_H__
#define __COPIESREWARD_VIEW_H__

#include "cocos2d.h"
#include "ui/CocosGUI.h"
#include "cocostudio/CocoStudio.h"
#include "../Views/PopupView.h"
#include "../SOSConfig.h"
#include "../Datas/CopyData.h"

static const char* EVENT_COPIES_CHANGE = "event_copies_change";

class CopiesRewardView : public PopupView
{

public:
	CREATE_FUNC(CopiesRewardView);
	static cocos2d::Layer* createScene(int copiesID, int star, std::vector<CopiesRewardData::Reward> vecReward);
public:
	CopiesRewardView();
	virtual ~CopiesRewardView();
	virtual bool init() override;
	virtual void onEnter() override;
	void touchButton(cocos2d::Ref* object, cocos2d::ui::TouchEventType type);

private:
	cocos2d::ui::Widget* layer;

};

#endif