#include "LoadingView.h"
#include "cocostudio/CocoStudio.h"

USING_NS_CC;
USING_NS_CC_CCS;

bool LoadingView::init()
{
	if (!PopupView::init())
	{
		return false;
	}

	this->setAutoClose(false);

	auto size = Director::getInstance()->getWinSize();

	ArmatureDataManager::getInstance()->addArmatureFileInfo("Animation_Flower.ExportJson");
	auto armature = Armature::create("Animation_Flower");
	armature->getAnimation()->play("flower");
	armature->setPosition(size.width / 2, size.height / 2);
	this->addChild(armature);

	return true;
}