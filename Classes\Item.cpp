#include "Item.h"
#include "SOSConfig.h"

USING_NS_CC;

Item* Item::create(ItemData::Info info)
{
	auto item = new Item();
	if (item && item->init(info))
	{
		item->autorelease();
		return item;
	}

	CC_SAFE_DELETE(item);
	return NULL;
}

bool Item::init(ItemData::Info info)
{
	if (!Sprite::init())
	{
		return false;
	}

	auto icon = Sprite::createWithSpriteFrameName(GET_PROP_ICON(info.icon));
	icon->setAnchorPoint(Vec2::ZERO);
	this->addChild(icon);

	if (info.type == ItemData::ItemType::EQUIP && info.quality > 0)
	{
		auto mask = Sprite::createWithSpriteFrameName(GET_PROP_ICON_MASK(info.icon));
		mask->setColor(GetEquipQualityColor(info.quality));
		mask->setAnchorPoint(Vec2::ZERO);
		this->addChild(mask);
	}

	return true;
}