#ifndef __TestSOS__NoviceData__
#define __TestSOS__NoviceData__

#include "cocos2d.h"
#include "../SOSConfig.h"

class NoviceData : public cocos2d::Ref
{
public:
	FUNC_CREATE(NoviceData);

public:
	enum NoviceType
	{
		DEFAULT,
		UI,
		DIALOG,
	};

	struct Info
	{
		int id;
		int action;
		int type;
		int ui;
		std::string dialog;
		int save;
		std::string effect;
		int value;
		int scene;
	};

	struct Mask
	{
		cocos2d::Point point;
		int anchor;
		int type;
		int width;
		int height;
		std::vector<int> tags;
	};

	struct Arrow
	{
		std::string id;
		cocos2d::Point point;
		std::string content;
	};

	struct Animation
	{
		std::string id;
		cocos2d::Point point;
		float scaleX;
		float scaleY;
	};

public:
	inline Info& getInfo() { return _info; }
	inline Mask& getMask() { return _mask; }
	inline Arrow& getArrow() { return _arrow; }
	inline Animation& getAnimation() { return _animation; }

private:
	Info _info;
	Mask _mask;
	Arrow _arrow;
	Animation _animation;
};


#endif