﻿#include "ShipRoomUpgradeView.h"
#include "../Transactions/ShipTransaction.h"
#include "../Services/LocalizeService.h"
#include "../Services/NewguidanceService.h"
#include "../Transactions/PartnerTransaction.h"
#include "AlertView.h"
#include "../Services/SoundService.h"
#include "../Scenes/BaseScene.h"

USING_NS_CC;
USING_NS_CC_CCS;
USING_NS_CC_UI;

enum tagShipRoom
{
	UI6_BUTTON_CLOSE = 1073,
	UI6_TEXTBMFONT_NAME = 1074,
	UI6_TEXTBMFONT_CURRENTLEVEL = 1078,
	UI6_TEXTBMFONT_NEXTLEVEL = 1085,
	UI6_TEXTBMFONT_CURRENTEFFECT = 1079,
	UI6_TEXTBMFONT_NEXTEFFECT = 1086,
	UI6_TEXTBMFONT_MONEY = 1081,
	UI6_TEXTBMFONT_CDTIME = 1082,
	UI6_BUTTON_UP = 1087,
	UI6_BUTTON_SPEEDUP = 33000,
	UI6_SCROLLVIEW = 1088,
	UI6_TEXTBMFONT_EFFECT = 1075,
	UI6_TEXTBMFONT_LEVEL = 965,
	UI6_IMAGEVIEW_ARROW1 = 1083,
	UI6_IMAGEVIEW_ARROW2 = 1084,
};

std::vector<CheckBox*> shiproom_checkBoxList;
int r_cabinDiamond;

ShipRoomUpgradeView::ShipRoomUpgradeView()
{

}

ShipRoomUpgradeView::~ShipRoomUpgradeView()
{
	r_cabinDiamond = 0;
	this->getEventDispatcher()->removeCustomEventListeners(EVENT_BUTTON_YES);
	shiproom_checkBoxList.clear();
	_shipData = nullptr;
	this->unschedule(schedule_selector(ShipRoomUpgradeView::update));
}

Layer* ShipRoomUpgradeView::createScene()
{
	auto layer = ShipRoomUpgradeView::create();
	return layer;
}

bool ShipRoomUpgradeView::init()
{
	if (!PopupView::init())
	{
		return false;
	}

	_autoClose = false;
	_build = CCS_CREATE_LAYER("UI_ship_6");
	_build->setTag(TagBaseScene::SHIP_UPGRADE_VIEW);
	this->addChild(_build);

	CCS_GET_CHILD(_build, Button, closeBtn6, UI6_BUTTON_CLOSE);
	CCS_GET_CHILD(_build, Button, upBtn, UI6_BUTTON_UP);
	CCS_GET_CHILD(_build, Button, speedupBtn, UI6_BUTTON_SPEEDUP);
	CCS_GET_CHILD(_build, cocos2d::ui::ScrollView, scrollView, UI6_SCROLLVIEW);

	this->scheduleUpdate();

	closeBtn6->addTouchEventListener(this, toucheventselector(ShipRoomUpgradeView::touchButton));
	upBtn->setPressedActionEnabled(true);
	upBtn->addTouchEventListener(this, toucheventselector(ShipRoomUpgradeView::touchButton));
	speedupBtn->addTouchEventListener(this, toucheventselector(ShipRoomUpgradeView::touchButton));

	auto userData = GameData::getInstance()->getUserData();
	for (auto item : userData->getQuickens())
	{
		if (item->getInfo().type == QuickenData::QuickType::CABIN)
		{
			_cd = item->getInfo().totalTime * 60;
			break;
		}
	}

	auto ships = userData->getShips();
	int width = 0;
	for (auto item : ships)
	{
		if (item.second->getInfo().use)
		{
			_shipData = item.second;
			auto cabins = item.second->getCabins();
			for (auto cb : cabins)
			{
				if (cb->getInfo().mid != 0 && cb->getInfo().type != 6)
				{
					auto uiLayer = CCS_CREATE_LAYER(SHIPROOM_UIFILE[cb->getInfo().type].c_str());
					CCS_GET_CHILD(uiLayer, TextBMFont, name, SHIPROOM_NAME_TAG[cb->getInfo().type]);
					CCS_GET_CHILD(uiLayer, TextBMFont, level, SHIPROOM_LEVEL_TAG[cb->getInfo().type]);
					name->setText(LocalizeService::getInstance()->getString(SHIP_CABIN_NAMES[cb->getInfo().type]).c_str());
					level->setText(UTF8("Lv" + STRING(cb->getInfo().level)).c_str());
					CCS_GET_CHILD(uiLayer, CheckBox, checkbox, SHIPROOM_CHECKBOX_TAG[cb->getInfo().type]);
					checkbox->setUserObject(cb);
					checkbox->addTouchEventListener(this, toucheventselector(ShipRoomUpgradeView::touchButton));
					if (cb->getInfo().type == 1)
					{
						checkbox->setSelectedState(true);
						checkbox->setTouchEnabled(false);
					}
					shiproom_checkBoxList.push_back(checkbox);
					scrollView->addChild(uiLayer);
					width = uiLayer->getContentSize().width;
				}
			}
			break;
		}
	}
	scrollView->setInnerContainerSize(Size(width*shiproom_checkBoxList.size(), scrollView->getContentSize().height));

	showEffect();
	return true;
}

void ShipRoomUpgradeView::showEffect(CheckBox* item)
{
	auto userData = GameData::getInstance()->getUserData();

	CCS_GET_CHILD(_build, Button, upBtn, UI6_BUTTON_UP);
	CCS_GET_CHILD(_build, Button, speedupBtn, UI6_BUTTON_SPEEDUP);

	//item->setSelectedState(true);
	auto cabinData = dynamic_cast<CabinData*>(item->getUserObject());

	CCS_GET_CHILD(_build, TextBMFont, name, UI6_TEXTBMFONT_NAME);
	name->setText(LocalizeService::getInstance()->getString(SHIP_CABIN_NAMES[cabinData->getInfo().type]).c_str());
	CCS_GET_CHILD(_build, TextBMFont, currentLevel, UI6_TEXTBMFONT_CURRENTLEVEL);
	currentLevel->setText(UTF8("Lv." + STRING(cabinData->getInfo().level)).c_str());
	CCS_GET_CHILD(_build, TextBMFont, currentEffect, UI6_TEXTBMFONT_CURRENTEFFECT);
	currentEffect->setText(getEffect(cabinData->getInfo().type, STRING(cabinData->getInfo().subjoin1)).c_str());
	CCS_GET_CHILD(_build, TextBMFont, money, UI6_TEXTBMFONT_MONEY);
	money->setText(STRING(cabinData->getRequire().money).c_str());
	if (cabinData->getRequire().money > userData->getInfo().money)
		money->setColor(Color3B::RED);
	else
		money->setColor(Color3B::WHITE);

	CCS_GET_CHILD(_build, TextBMFont, cdTime, UI6_TEXTBMFONT_CDTIME);
	//auto nowtiem = get_date_now();
	//if (GameData::getInstance()->shiproomTime - nowtiem <= 0)
	if (GameData::getInstance()->shiproomTime  <= 0)
	{
		cdTime->setText("0:00:00");
		speedupBtn->setOpacity(0);
		speedupBtn->setTouchEnabled(false);
	}
	else
	{
		//cdTime->setText(getTimer(GameData::getInstance()->shiproomTime - nowtiem));
		cdTime->setText(getTimer(GameData::getInstance()->shiproomTime ));
		speedupBtn->setOpacity(255);
		speedupBtn->setPressedActionEnabled(true);
		speedupBtn->setTouchEnabled(true);
	}

	//if (GameData::getInstance()->shiproomTime - nowtiem > _cd)
	if (GameData::getInstance()->shiproomTime  > _cd)
		cdTime->setColor(Color3B::RED);
	else
		cdTime->setColor(Color3B::WHITE);
	//isUp = GameData::getInstance()->shiproomTime - nowtiem > _cd ? false : true;
	isUp = GameData::getInstance()->shiproomTime  > _cd ? false : true;

	CCS_GET_CHILD(_build, TextBMFont, nextLevel, UI6_TEXTBMFONT_NEXTLEVEL);
	CCS_GET_CHILD(_build, TextBMFont, nextEffect, UI6_TEXTBMFONT_NEXTEFFECT);
	CCS_GET_CHILD(_build, ImageView, arrow1, UI6_IMAGEVIEW_ARROW1);
	CCS_GET_CHILD(_build, ImageView, arrow2, UI6_IMAGEVIEW_ARROW2);
	if (cabinData->getRequire().money == 0)
	{
		nextEffect->setVisible(false);
		nextLevel->setVisible(false);
		arrow1->setVisible(false);
		arrow2->setVisible(false);
		currentLevel->setText(UTF8("Lv.Max").c_str());
	}
	else
	{
		int id = cabinData->getInfo().mid;
		auto nextCabinData = GameData::getInstance()->getCabin(id + 1);
		nextLevel->setVisible(true);
		nextLevel->setText(UTF8("Lv." + STRING(nextCabinData->getInfo().level)));
		nextEffect->setVisible(true);
		nextEffect->setText(getEffect(cabinData->getInfo().type, STRING(nextCabinData->getInfo().subjoin1)).c_str());

		arrow1->setVisible(true);
		arrow2->setVisible(true);
	}

	CCS_GET_CHILD(_build, TextBMFont, cabinEffect, UI6_TEXTBMFONT_EFFECT);
	cabinEffect->setText(LocalizeService::getInstance()->getString(CABINEFFECT_TYPE[cabinData->getInfo().type]).c_str());

	CCS_GET_CHILD(_build, TextBMFont, levelTxt, UI6_TEXTBMFONT_LEVEL);
	if (cabinData->getInfo().type == CabinData::CabinType::CAPTAIN_HOUSE)
	{
		levelTxt->setText((LocalizeService::getInstance()->getString(PLAYER_SHIP_LEVEL[0]) + STRING(cabinData->getRequire().level)).c_str());

		for (auto player : userData->getPlayers())
		{
			if (player->getInfo().mid < 5)
			{
				if (player->getInfo().level < cabinData->getRequire().level)
				{
					levelTxt->setColor(Color3B::RED);
				}
				else
				{
					levelTxt->setColor(Color3B::WHITE);
				}

				//if (player->getInfo().level < cabinData->getRequire().level || cabinData->getRequire().money == 0)
				//{
				//	upBtn->setTouchEnabled(false);
				//}
				//else
				//{
				//	upBtn->setTouchEnabled(true);
				//}
				break;
			}
		}
	}
	else
	{
		auto captainCabinData = GameData::getInstance()->getCabin(cabinData->getRequire().captain);
		int level = captainCabinData->getInfo().level;
		levelTxt->setText((LocalizeService::getInstance()->getString(PLAYER_SHIP_LEVEL[1]) + STRING(level)).c_str());
		for (auto ship : userData->getShips())
		{
			if (ship.second->getInfo().use)
			{
				for (auto cabin : ship.second->getCabins())
				{
					if (cabin->getInfo().type == CabinData::CabinType::CAPTAIN_HOUSE)
					{
						if (cabin->getInfo().level < level)
						{
							levelTxt->setColor(Color3B::RED);
						}
						else
						{
							levelTxt->setColor(Color3B::WHITE);
						}

						//if (cabin->getInfo().level < level || cabinData->getRequire().money == 0)
						//{
						//	upBtn->setTouchEnabled(false);
						//}
						//else
						//{
						//	upBtn->setTouchEnabled(true);
						//}
						break;
					}
				}
				break;
			}
		}
	}
	if (cabinData->getInfo().type == CabinData::CabinType::CAPTAIN_HOUSE)
	{
		if (cabinData->getRequire().level == 0)
		{
			levelTxt->setColor(Color3B::WHITE);
			//levelTxt->setText(LocalizeService::getInstance()->getString("659"));
		}
	}
	else
	{
		if (cabinData->getRequire().captain == 0)
		{
			levelTxt->setColor(Color3B::WHITE);
			//levelTxt->setText(LocalizeService::getInstance()->getString("659"));
		}
	}
}

void ShipRoomUpgradeView::showEffect()
{
	for (auto item : shiproom_checkBoxList)
	{
		if (item->getSelectedState())
		{
			this->showEffect(item);
			break;
		}
	}
}

void ShipRoomUpgradeView::menuCloseCallback(Ref* pSender)
{
	Director::getInstance()->end();

#if (CC_TARGET_PLATFORM == CC_PLATFORM_IOS)
	exit(0);
#endif
}

void ShipRoomUpgradeView::onEnter()
{
	// 	if (GameData::getInstance()->isCreate)
	// 	{
	// 		this->addChild(NewguidanceService::getInstance()->createLayer(), this->getChildrenCount() + 3);
	// 	}
	//this->schedule(schedule_selector(ShipRoomUpgradeView::update), 1.0f);
	this->getEventDispatcher()->addCustomEventListener(EVENT_BUTTON_YES, [=](EventCustom* event){
		if (r_cabinDiamond > 0)
		{
			auto userData = GameData::getInstance()->getUserData();
			if (userData->getInfo().diamond >= r_cabinDiamond)
			{
				SoundService::getInstance()->playSFX(SOUND_EFFECT_SHIPUP);
				PartnerTransaction::getInstance()->refreshCDTime(GameData::CDTimeType::CD_CABIN);
				userData->getInfo().diamond -= r_cabinDiamond;
				GameData::getInstance()->shiproomTime = 0;
				showEffect();
				Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_NEWGUIDANCE_START);
			}
			else
			{
				this->addChild(AlertView::create(LocalizeService::getInstance()->getString("645"), AlertType::IDLE), 10000, 99999);
			}
			r_cabinDiamond = 0;
		}
	});
	
	PopupView::onEnter();
}

void ShipRoomUpgradeView::touchButton(Ref* obj, cocos2d::ui::TouchEventType eventType)
{
	auto userData = GameData::getInstance()->getUserData();
	auto widget = dynamic_cast<Widget*>(obj);
	int tag = widget->getTag();
	switch (eventType)
	{
	case TouchEventType::TOUCH_EVENT_BEGAN:
		break;
	case TouchEventType::TOUCH_EVENT_ENDED:
		if (tag == UI6_BUTTON_CLOSE)
		{
			Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_NEWGUIDANCE_START, NULL);
			this->removeFromParent();
		}
		else if (tag == UI6_BUTTON_SPEEDUP)
		{
			//auto t = (GameData::getInstance()->shiproomTime - get_date_now()) / 60 == 0 ? 1 : (GameData::getInstance()->shiproomTime - get_date_now()) / 60 + 1;
			auto t = (GameData::getInstance()->shiproomTime) / 60 == 0 ? 1 : (GameData::getInstance()->shiproomTime) / 60 + 1;
			for (auto a : userData->getQuickens())
			{
				if (a->getInfo().type == GameData::CDTimeType::CD_CABIN)
				{
					r_cabinDiamond = ceil(t*a->getInfo().reqValue);
					//userData->getInfo().diamond -= d;
					break;
				}
			}
			std::string str = "644";
			AlertView::createAndAdded(STRING(r_cabinDiamond), AlertType::DIALOG, &str);
			//this->addChild(AlertView::create(), 10000, 99999);
			//this->addChild(AlertView::create(LocalizeService::getInstance()->getString("644") + STRING(r_cabinDiamond), AlertType::DIALOG, &r_cabinDiamond), 10000, 99999);
		}
		else if (tag == UI6_BUTTON_UP)
		{
			for (auto item : shiproom_checkBoxList)
			{
				// 				if (GameData::getInstance()->isCreate)
				// 				{
				// 					this->addChild(NewguidanceService::getInstance()->createLayer(), this->getChildrenCount() + 3);
				// 				}
				//auto item = dynamic_cast<cocos2d::ui::CheckBox*>(obj);
				if (item->getSelectedState())
				{
					auto cabinData = dynamic_cast<CabinData*>(item->getUserObject());

					if (cabinData->getInfo().type == CabinData::CabinType::CAPTAIN_HOUSE)
					{
						if (cabinData->getRequire().level == 0)
						{
							this->addChild(AlertView::create(LocalizeService::getInstance()->getString("100031"), AlertType::IDLE), 999999);
							r_cabinDiamond = 0;
							return;
						}
					}
					else
					{
						if (cabinData->getRequire().captain == 0)
						{
							this->addChild(AlertView::create(LocalizeService::getInstance()->getString("100031"), AlertType::IDLE), 999999);
							r_cabinDiamond = 0;
							return;
						}
					}

					if (!isUp)
					{
						//MessageBox(LocalizeService::getInstance()->getString("604").c_str(), "Tips");
						this->addChild(AlertView::create(LocalizeService::getInstance()->getString("608"), AlertType::IDLE), 999999);
						r_cabinDiamond = 0;
						return;
					}


					auto ships = userData->getShips();
					for (auto ship : ships)
					{
						if (ship.second->getInfo().use)
						{

							if (cabinData->getInfo().type == CabinData::CabinType::CAPTAIN_HOUSE)
							{
								if (userData->getPlayer(userData->getInfo().role)->getInfo().level < cabinData->getRequire().level)
								{
									this->addChild(AlertView::create(LocalizeService::getInstance()->getString("603"), AlertType::IDLE), 999999);
									r_cabinDiamond = 0;
									return;
								}
							}
							else
							{

								for (auto c : ship.second->getCabins())
								{
									if (c->getInfo().type == CabinData::CabinType::CAPTAIN_HOUSE)
									{

										if (c->getInfo().level < GameData::getInstance()->getCabin(cabinData->getRequire().captain)->getInfo().level)
										{
											this->addChild(AlertView::create(LocalizeService::getInstance()->getString("603"), AlertType::IDLE), 999999);
											r_cabinDiamond = 0;
											return;
										}
										break;
									}
								}
							}


							CCS_GET_CHILD(_build, TextBMFont, money, UI6_TEXTBMFONT_MONEY);

							if (cabinData->getRequire().money > userData->getInfo().money)
							{
								//MessageBox(LocalizeService::getInstance()->getString("4010").c_str(), "Tips");
								this->addChild(AlertView::create(LocalizeService::getInstance()->getString("4010"), AlertType::IDLE), 999999);
								r_cabinDiamond = 0;
								return;
							}
							else if (cabinData->getRequire().money == 0)
							{
								this->addChild(AlertView::create(LocalizeService::getInstance()->getString("100031"), AlertType::IDLE), 999999);
								r_cabinDiamond = 0;
								return;
							}
							else
							{
								userData->getInfo().money -= cabinData->getRequire().money;

								//if (GameData::getInstance()->shiproomTime - get_date_now() <= 0)
								//	GameData::getInstance()->shiproomTime = get_date_now();
								GameData::getInstance()->shiproomTime += cabinData->getRequire().cd * 60;
								ShipTransaction::getInstance()->houseUpgrade(userData->getInfo().uid, ship.second->getInfo().id, cabinData->getInfo().type, cabinData->getInfo().mid);

								auto cab = GameData::getInstance()->getCabin(cabinData->getInfo().mid + 1);
								cab->getInfo().appoint = cabinData->getInfo().appoint;

								ship.second->getCabins().replace(ship.second->getCabins().getIndex(cabinData), cab);
								item->setUserObject(cab);

								CCS_GET_CHILD(item->getParent(), TextBMFont, level, SHIPROOM_LEVEL_TAG[cab->getInfo().type]);
								int cablevel = cab->getInfo().level;
								level->setText(UTF8("Lv" + STRING(cablevel)).c_str());
								showEffect();

								this->getEventDispatcher()->dispatchCustomEvent(EVENT_CABIN_UPGRADED, NULL);
								GameData::getInstance()->isTaskComplete(GameData::MissionType::CABIN, 0, 0);

								Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_NEWGUIDANCE_START);
							}
							break;
						}
					}
					break;
				}
			}
		}
		else
		{
			CheckBox* cb = nullptr;

			for (auto item : shiproom_checkBoxList)
			{
				if (item == dynamic_cast<CheckBox*>(obj))
				{
					//item->setSelectedState(true);
					cb = item;
					item->setTouchEnabled(false);
				}
				else
				{
					item->setSelectedState(false);
					item->setTouchEnabled(true);
				}
			}

			showEffect(cb);
		}
		break;
	}
}

std::string ShipRoomUpgradeView::getEffect(int type, std::string value)
{
	std::string str;
	switch (type)
	{
	case 1:
	case 2:
		str = STRING(FLOAT(value) * 100) + "%";
		break;
	case 4:
		str = STRING(value);
		break;
	case 5:
	case 7:
		str = STRING(value);
		break;
	default:
		str = STRING(value + "T");
		break;
	}
	return str;
}

void ShipRoomUpgradeView::update(float dt)
{
	CCS_GET_CHILD(_build, TextBMFont, cdTime, UI6_TEXTBMFONT_CDTIME);
	CCS_GET_CHILD(_build, Button, speedupBtn, UI6_BUTTON_SPEEDUP);

	//auto nowtiem = get_date_now();
	//if (GameData::getInstance()->shiproomTime - nowtiem <= 0)
	if (GameData::getInstance()->shiproomTime  <= 0)
	{
		cdTime->setText("0:00:00");
		speedupBtn->setOpacity(0);
		speedupBtn->setTouchEnabled(false);
		//this->unscheduleUpdate();
	}
	else
	{
		//cdTime->setText(getTimer(GameData::getInstance()->shiproomTime - nowtiem));
		cdTime->setText(getTimer(GameData::getInstance()->shiproomTime));
		speedupBtn->setOpacity(255);
		speedupBtn->setPressedActionEnabled(true);
		speedupBtn->setTouchEnabled(true);
		//this->scheduleUpdate();
	}

	//if (GameData::getInstance()->shiproomTime - nowtiem > _cd)
	if (GameData::getInstance()->shiproomTime  > _cd)
		cdTime->setColor(Color3B::RED);
	else
		cdTime->setColor(Color3B::WHITE);

	//isUp = GameData::getInstance()->shiproomTime - nowtiem > _cd ? false : true;
	isUp = GameData::getInstance()->shiproomTime  > _cd ? false : true;
}