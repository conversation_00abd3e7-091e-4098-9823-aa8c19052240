#ifndef __SOS_CITY_DATA__
#define __SOS_CITY_DATA__

#include "cocos2d.h"
#include "../SOSConfig.h"
#include "UserData.h"

typedef cocos2d::Map<std::string, CopyData *> CopyMap;


class CityData : public cocos2d::Ref
{
public:
	FUNC_CREATE(CityData);

public:
	struct Info
	{
		int mid;
		std::string name;
		int bg;
		int type;
		int seas;
		cocos2d::Point crood;
		std::vector<std::string> funs;
	};

	struct Dock
	{
		int city;
	 	std::vector<int> ships;
	};

	struct Smithy
	{
		struct SmithyInfo
		{
			int mid;
		};
		std::vector<SmithyInfo> SmithyVector;
	};

	inline Info& getInfo() { return _info; }
	inline UserData::ShopMap& getCargos() { return _cargos; }
	inline void setCargos(UserData::ShopMap cargos) { _cargos = cargos; }
	
	inline Dock& getDock(){ return _dock; }
	inline CopyMap& getCopies() { return _copies; }

	inline Smithy& getSmithy(){ return _smithy; }

private:
	Info _info;
	UserData::ShopMap _cargos;
	Dock _dock;
	CopyMap _copies;
	Smithy _smithy;
};

#endif //__SOS_CITY_DATA__