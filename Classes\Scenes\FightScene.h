#ifndef __SOS_FIGHT_SCENE__
#define __SOS_FIGHT_SCENE__

#include "cocos2d.h"
#include "ui/CocosGUI.h"
#include "cocostudio/CocoStudio.h"
#include "../Datas/FightData.h"
#include "../Datas/TeamData.h"
#include "../Datas/SkillData.h"
#include "BaseScene.h"

class FightScene : public BaseScene
{
public:
	CREATE_FUNC(FightScene);

	static cocos2d::Scene* createScene();
	static cocos2d::Scene* createScene(FightData* fightData);

	static FightScene* create(FightData* fightData);

public:
	FightScene();
	virtual ~FightScene();

	virtual bool init() override;
	virtual void onEnter() override;

	bool init(FightData* fightData);
	void onTouchCheck(cocos2d::Ref *object, cocos2d::ui::CheckBoxEventType eventType);
	void onStartEvent(cocostudio::Armature *armature, cocostudio::MovementEventType movementType, const std::string& movementID);
	void onEffectEvent(cocostudio::Armature *armature, cocostudio::MovementEventType movementType, const std::string& movementID);
	void onAnimationEvent(cocostudio::Armature *armature, cocostudio::MovementEventType movementType, const std::string& movementID);
	void onFrameEvent(cocostudio::Bone *bone, const std::string& evt, int originFrameIndex, int currentFrameIndex);
	void onDamageCallback(cocos2d::Node* node, bool cleanup);
	void onShooterCallback(cocos2d::Node* node, bool cleanup);
	void cleanup(float dt);
	void showResult(float dt);

private:
	void initTeam(TeamData::FaceType face);
	void start(float dt);
	void next();
	void runAct();
	void runHitting(cocos2d::Node* targetNode, int type, int life, int energy, bool dead, bool hit = true);
	void runAOE(SkillData* skill);
	void runEffect(SkillData* skill, cocos2d::Node* targetNode);

private:
	FightData* _fightData;
	cocos2d::Node* _scene;
	std::map<std::string, cocos2d::Node *> _playerNodes;

private:
	int _currentCount;
	int _currentRound;
	int _currentAction;
	int _currentZOrder;
	cocos2d::Node* _currentNode;
	cocos2d::Point _currentPoint;

	//bool _isWreck = false;
};

#endif //__SOS_FIGHT_SCENE__