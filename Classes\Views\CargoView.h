#ifndef __SOS_CARGO_VIEW__
#define __SOS_CARGO_VIEW__

#include "cocos2d.h"
#include "PopupView.h"
#include "ui/CocosGUI.h"
#include "../GameData.h"

static const std::string EVENT_SHOP_SELL_SUCCESS = "event_shop_sell_success";

class CargoView : public PopupView
{
public:
	enum class Type
	{
		BUY,
		SELL,
	};

public:
	CREATE_FUNC(CargoView);

	static cocos2d::Layer* create(UserData::Cargo cargo, CargoView::Type type);

public:
	virtual bool init() override;
	virtual void onEnter() override;

	void touchButton(cocos2d::Ref* object, cocos2d::ui::TouchEventType type);

private:
	void updateInfo(int count);

private:
	cocos2d::ui::Widget* _layer;
	int _count;
};

#endif // !__SOS_CARGO_VIEW__
