//
//  FormationTransaction.h
//  TestSOS
//
//  Created by <PERSON><PERSON> on 13-12-21.
//
//

#ifndef __TestSOS__FormationTransaction__
#define __TestSOS__FormationTransaction__

#include "cocos2d.h"
#include "../SOSConfig.h"
#include "../GameData.h"

class PostionArray;

static const char* EVENT_FORMATION_GET_SUCCESS = "event_formation_get_success";
static const char* EVENT_FORMATION_SAVE_SUCCESS = "event_formation_save_success";

class FormationTransaction : public cocos2d::Ref
{
public:
	FUNC_INSTANCE(FormationTransaction);

public:
	FormationTransaction();
	virtual ~FormationTransaction();

	void Modify(std::string uID, std::string onlyID, int postion, int type);

	void Get(std::string uID);

	void Save(std::string uID,PositionVector postionArray);

};

#endif /* defined(__TestSOS__FormationTransaction__) */
