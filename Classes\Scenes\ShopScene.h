#ifndef __SHOP_SCENE_H__
#define __SHOP_SCENE_H__

#include "cocos2d.h"
#include "ui/CocosGUI.h"
#include "cocostudio/CocoStudio.h"
#include "../GameData.h"
#include "BaseScene.h"

class ShopScene : public BaseScene
{
public:
	CREATE_FUNC(ShopScene);

	static cocos2d::Scene* createScene();
	static cocos2d::Scene* createScene(bool b);

public:
	ShopScene(): _scene(nullptr) {};
	virtual ~ShopScene();

	virtual bool init() override;
	virtual void onEnter() override;

	void removeCargoView(float dt);
	void touchButton(cocos2d::Ref* object, cocos2d::ui::TouchEventType type);
	void touchPageView(cocos2d::Ref* object, cocos2d::ui::PageViewEventType type);

private:
	void initCargos();
	void initMyCargos();
	void initWeapon();
	void initBag();

public:
	cocos2d::Node* _scene;
	cocos2d::ui::Widget* _cargosLayer;
	cocos2d::ui::Widget* _mycargosLayer;
	cocos2d::ui::Widget* _weaponLayer;
	cocos2d::ui::Widget* _bagLayer;
};

#endif // __SHOP_SCENE_H__