#ifndef __SOS_SHADER_SPRITE__
#define __SOS_SHADER_SPRITE__

#include "cocos2d.h"

USING_NS_CC;

class EffectSprite;

class Effect : public cocos2d::Ref
{
public:
	cocos2d::GLProgramState* getGLProgramState() const { return _glprogramstate; }
	virtual void setTarget(EffectSprite *sprite){}

protected:
	bool initGLProgramState(const std::string &fragmentFilename);
	Effect();
	virtual ~Effect();
	cocos2d::GLProgramState *_glprogramstate;
#if (CC_TARGET_PLATFORM == CC_PLATFORM_ANDROID)
	std::string _fragSource;
	EventListenerCustom* _backgroundListener;
#endif
};

static int tuple_sort(const std::tuple<ssize_t, Effect*, QuadCommand> &tuple1, const std::tuple<ssize_t, Effect*, QuadCommand> &tuple2)
{
	return std::get<0>(tuple1) < std::get<0>(tuple2);
}

class EffectSprite : public Sprite
{
public:
	static EffectSprite *create(const std::string& filename) {
		auto ret = new (std::nothrow) EffectSprite;
		if (ret && ret->initWithFile(filename)) {
			ret->autorelease();
			return ret;
		}
		CC_SAFE_RELEASE(ret);
		return nullptr;
	}

	static EffectSprite *createWithSpriteFrameName(const std::string& spriteFrameName) {
		auto ret = new (std::nothrow) EffectSprite;
		if (ret && ret->initWithSpriteFrameName(spriteFrameName)) {
			ret->autorelease();
			return ret;
		}
		CC_SAFE_RELEASE(ret);
		return nullptr;
	}

	void setEffect(Effect* effect);

	void draw(Renderer *renderer, const Mat4 &transform, bool transformUpdated) override
	{
		// Don't do calculate the culling if the transform was not updated
		_insideBounds = transformUpdated ? renderer->checkVisibility(transform, _contentSize) : _insideBounds;

		if (_insideBounds)
		{
			// negative effects: order < 0
			int idx = 0;
			for (auto &effect : _effects) {

				if (std::get<0>(effect) >= 0)
					break;
				QuadCommand &q = std::get<2>(effect);
				q.init(_globalZOrder, _texture->getName(), std::get<1>(effect)->getGLProgramState(), _blendFunc, &_quad, 1, transform);
				renderer->addCommand(&q);
				idx++;

			}

			// normal effect: order == 0
			_quadCommand.init(_globalZOrder, _texture->getName(), getGLProgramState(), _blendFunc, &_quad, 1, transform);
			renderer->addCommand(&_quadCommand);

			// postive effects: oder >= 0
			for (auto it = std::begin(_effects) + idx; it != std::end(_effects); ++it) {
				QuadCommand &q = std::get<2>(*it);
				q.init(_globalZOrder, _texture->getName(), std::get<1>(*it)->getGLProgramState(), _blendFunc, &_quad, 1, transform);
				renderer->addCommand(&q);
				idx++;
			}
		}
	}
protected:
	EffectSprite() : _defaultEffect(nullptr)
	{
		_effects.reserve(2);
	}
	~EffectSprite() {
		for (auto &tuple : _effects) {
			std::get<1>(tuple)->release();
		}
		CC_SAFE_RELEASE(_defaultEffect);
	}

	std::vector<std::tuple<ssize_t, Effect*, QuadCommand>> _effects;
	Effect* _defaultEffect;
};

// Outline
class EffectOutline : public Effect
{
public:
	CREATE_FUNC(EffectOutline);

	bool init()
	{
		initGLProgramState("Shaders/example_outline.fsh");

		Vec3 color(1.0, 0.2, 0.3);
		GLfloat radius = 0.01;
		GLfloat threshold = 1.75;

		_glprogramstate->setUniformVec3("u_outlineColor", color);
		_glprogramstate->setUniformFloat("u_radius", radius);
		_glprogramstate->setUniformFloat("u_threshold", threshold);
		return true;
	}
};

#endif //__SOS_SHADER_SPRITE__