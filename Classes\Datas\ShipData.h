﻿//
//  ShipData.h
//  TestSOS
//
//  Created by <PERSON><PERSON> on 14-1-22.
//
//

#ifndef __TestSOS__ShipData__
#define __TestSOS__ShipData__

#include "cocos2d.h"
#include "../SOSConfig.h"

static const std::string EVENT_SHIPWAREHOUSE_CHANGED = "event_shipwarehouse_changed";

class CabinData : public cocos2d::Ref
{
public:
    FUNC_CREATE(CabinData)
    
public:
    enum CabinType : int
    {
        CAPTAIN_HOUSE = 1,
        OPERATE_HOUSE,
        SUPPLY_HOUSE,
        NAVIGATION_HOUSE,
        REST_HOUSE,
        MEDICAL_HOUSE,
        TRAIN_HOUSE,
        WARE_HOUSE,
    }; /** 1:船长室 2:经营室 3:补给室 4:航海室 5:休息室 6:医疗室 7:训练室 8:仓库 **/

    struct Info
    {
        int mid;
        int type;       //CabinType
		int level;
        std::string appoint;
		int appointCount;
		float subjoin1;
		float subjoin2;
		float subjoin3;
    };

	struct Require
	{
		int mid;
		int captain;
		int money;
		int level;
		int cd;
	};
    
public:
    inline Info& getInfo() { return _info; }
    inline void setInfo(Info info) { _info = info; }

	inline Require& getRequire() { return _require; }
	inline void setRequire(Require require) { _require = require; }
    
private:
    Info _info;
	Require _require;
};

typedef cocos2d::Vector<CabinData *> CabinVector;

class ShipData : public cocos2d::Ref
{
public:
    FUNC_CREATE(ShipData)
    
public:
    struct Info
    {
        std::string id;
        int mid;
        std::string name;
        int level;
        int icon;
        bool use;
        int artillery;
		int type;
		int recharge;
    };
    
    struct Require
    {
        int price;
        int level;
        int renown;
    };

    inline Info& getInfo() { return _info; }
    inline void setInfo(Info info) { _info = info; }
    
    inline Require& getRequire() { return _require; }
    inline void setRequire(Require require) { _require = require; }

    inline CabinVector& getCabins() { return _cabins; }

	int getCP();
	int getOP();
	int getSpace();
	int getPlayersLimit();
	int getTrainingLimit();
	float getBuyDiscount();
	float getSellDiscount();

	void refreshView();
    
private:
    Info _info;
    Require _require;
    CabinVector _cabins;
};

#endif /* defined(__TestSOS__ShipData__) */
