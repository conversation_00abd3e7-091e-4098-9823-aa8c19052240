//
//  HTTPService.cpp
//  TestSOS
//
//  Created by <PERSON><PERSON> on 13-12-17.
//
//

#include "HTTPService.h"
#include "json/stringbuffer.h"
#include "json/writer.h"
#include "../GameData.h"
#include "../Views/AlertView.h"

USING_NS_CC;
USING_STD;

using namespace network;
using namespace rapidjson;

HTTPData::HTTPData()
{
    _root.SetObject();
}

HTTPData::~HTTPData()
{
    
}

HTTPData* HTTPData::readData(const char* name)
{
	rapidjson::Value& value = _root[name];

	auto data = HTTPData::create();
	data->setRoot(value);

	return data;
}

vector<HTTPData *> HTTPData::readDatas(const char *name)
{
    vector<HTTPData *> datas;
    
    auto size = _root[name].Size();
    for (int i = 0; i < size; i++)
    {
        rapidjson::Value& value = _root[name][i];
        
        auto data = HTTPData::create();
        data->setRoot(value);
        
        datas.push_back(data);
    }
    
    return datas;
}

void HTTPData::write(const char* name, vector<HTTPData *> value)
{
    rapidjson::Value array(kArrayType);
    
    for (auto data : value)
    {
        array.PushBack(data->getRoot(), _allocator);
    }
    
    _root.AddMember(name, array, _allocator);
}

void HTTPData::update(const char* name, const char* value)
{
	_root[name].SetString(value);
}

FUNC_GET_INSTANCE(HTTPService)

HTTPService::HTTPService()
{
    string uid = GameData::getInstance()->getUserData()->getInfo().uid;
    _headers.push_back("UDID: " + uid);

	Director::getInstance()->getEventDispatcher()->addCustomEventListener(EVENT_HTTP_CLIENT_ERROR, [=](EventCustom* event){
		auto tag = static_cast<const char* *>(event->getUserData());
		auto scene = Director::getInstance()->getRunningScene();
		if (scene != nullptr)
		{
			auto error = String::createWithFormat("[Client]: %s", *tag)->getCString();
			scene->addChild(AlertView::create(error, AlertType::IDLE), scene->getChildrenCount() + 1);
		}
	});

	Director::getInstance()->getEventDispatcher()->addCustomEventListener(EVENT_HTTP_SERVER_ERROR, [=](EventCustom* event){
		auto tag = static_cast<const char* *>(event->getUserData());
		auto scene = Director::getInstance()->getRunningScene();
		if (scene != nullptr)
		{
			auto error = String::createWithFormat("[Server]: %s", *tag)->getCString();
			scene->addChild(AlertView::create(error, AlertType::IDLE), scene->getChildrenCount() + 1);
		}
	});
}

HTTPService::~HTTPService()
{
    HttpClient::getInstance()->destroyInstance();

	Director::getInstance()->getEventDispatcher()->removeCustomEventListeners(EVENT_HTTP_CLIENT_ERROR);
	Director::getInstance()->getEventDispatcher()->removeCustomEventListeners(EVENT_HTTP_SERVER_ERROR);
}

void HTTPService::request(const char* url, HTTPData* data)
{
    this->request(url, data, nullptr);
}

void HTTPService::request(const char* url, HTTPData* data, const char* tag)
{
    StringBuffer buffer;
    Writer<StringBuffer> writer(buffer);
    
    data->getRoot().Accept(writer);
    
    auto json = buffer.GetString();
    
    //CCLOG("[HTTP] request: %s, %s", url, json);
    
    auto request = new HttpRequest();
    request->setUrl(url);
    request->setTag(tag);
    request->setHeaders(_headers);
    request->setRequestType(HttpRequest::Type::POST);
    request->setResponseCallback(this, httpresponse_selector(HTTPService::onResponseCallback));
    request->setRequestData(json, strlen(json));
    
	this->send(request);
    //HttpClient::getInstance()->send(request);
    //request->release();
}

void HTTPService::request(const char* url, const char* tag)
{
	CCLOG("[HTTP] request: %s", url);

	auto request = new HttpRequest();
	request->setUrl(url);
	request->setTag(tag);
	request->setHeaders(_headers);
	request->setRequestType(HttpRequest::Type::GET);
	request->setResponseCallback(this, httpresponse_selector(HTTPService::onResponseCallback));

	HttpClient::getInstance()->send(request);

	request->release();
}

void HTTPService::onResponseCallback(HttpClient *sender, HttpResponse *response)
{
    if (!response)
    {
        return;
    }
    
    auto statusCode = response->getResponseCode();
    auto tag = response->getHttpRequest()->getTag();
    CCLOG("[HTTP] status: %ld, tag: %s", statusCode, tag);
    
    if (!response->isSucceed())
    {
		CCLOG("[HTTP] response failed: %s", response->getErrorBuffer());
		Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_CONNECT_ERROR, NULL);
		_requests.clear();
        return;
    }
    
    auto body = response->getResponseData();
    std::string json(body->begin(), body->end());
    //CCLOG("[HTTP] data: %s", json.c_str());
    
	Document document;
	document.Parse<0>(json.c_str());

	auto data = HTTPData::create();
	data->setRoot(document);

	if (!data->getRoot().IsObject())
	{
		//MessageBox("server error ...", "HTTP");
		Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_SERVER_ERROR, NULL);
		Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_HTTP_SERVER_ERROR, &tag);
		_requests.clear();
		return;
	}

	auto flags = data->readInt("Flags");

	if (flags != 0)
	{
		CCLOG("[HTTP] flags == %d", flags);
		//MessageBox("send error ...", "HTTP");
		std::string error = String::createWithFormat("[%d] %s", flags, tag)->getCString();
		Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_CLIENT_ERROR, NULL);
		Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_HTTP_CLIENT_ERROR, &error);
		_requests.clear();
		return;
	}

	if (tag != nullptr)
	{
		CCLOG("[HTTP] flags == %d, tag: %s", flags, tag);
		this->send();
		Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(tag, data);
	}
}

void HTTPService::send()
{
	if (_requests.size() > 0)
	{
		_requests.erase(0);
	}
	if (_requests.size() > 0)
	{
		CCLOG("[HTTP] request: %s, %s", _requests.at(0)->getUrl(), _requests.at(0)->getRequestData());
		HttpClient::getInstance()->send(_requests.at(0));
	}
}

void HTTPService::send(HttpRequest* request)
{
	_requests.pushBack(request);
	if (_requests.size() == 1)
	{
		CCLOG("[HTTP] request: %s, %s", _requests.at(0)->getUrl(), _requests.at(0)->getRequestData());
		HttpClient::getInstance()->send(_requests.at(0));
	}
}
