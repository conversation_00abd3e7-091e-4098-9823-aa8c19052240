#include "GameScene.h"
#include "SOSConfig.h"
#include "Transactions/LoginTransaction.h"
#include "Scenes/CreatePersonScene.h"
#include "LoadingScene.h"
#include "Services/LocalizeService.h"
#include "Services/SoundService.h"
#include "SimpleAudioEngine.h"
#include "Scenes/EntryScene.h"
#include "json/rapidjson.h"
#include "json/document.h"
#include "Platforms/CMHelper.h"
#include "Services/ResourceService.h"

USING_NS_CC;
USING_NS_CC_CD;

std::thread *r_thread;

bool GameScene::init()
{
	auto layer = GameLayer::create();
	this->addChild(layer);

	return true;
}

GameLayer::GameLayer()
{

}

GameLayer::~GameLayer()
{
	//this->getEventDispatcher()->removeCustomEventListeners(EVENT_GAME_LOGIN_CREATE);
	//this->getEventDispatcher()->removeCustomEventListeners(EVENT_GAME_LOGIN_SUCCESS);

// 	r_thread->join();
// 	delete r_thread;
// 	r_thread = nullptr;

	Director::getInstance()->getTextureCache()->removeTextureForKey("logo.jpg");
}

bool GameLayer::init()
{
	if (!Layer::init())
	{
		return false;
	}

	auto size = Director::getInstance()->getWinSize();

	auto sprite = Sprite::create("logo.png");
	sprite->setAnchorPoint(Point::ZERO);
	sprite->setPosition(Point((size.width - sprite->getContentSize().width) / 2, (size.height - sprite->getContentSize().height) / 2));
	this->addChild(sprite);

	GameData::getInstance()->isOpenMusic = CMHelper::GetMusicEnabled();
	SoundService::getInstance()->openOrclose(GameData::getInstance()->isOpenMusic);
	SimpleAudioEngine::getInstance()->preloadEffect("Sounds/fx_button.mp3");

	return true;
}

void GameLayer::onEnter()
{
	//SoundService::getInstance()->playBGM(SOUND_CITY, true);

	SoundService::getInstance()->playSFX("Sounds/fx_logo.mp3");

	//GameData::getInstance()->initScale();

	this->scheduleOnce(schedule_selector(GameLayer::initConfigs), 1.0f / 60);

//  	std::string filename = "Configs/S_PlayerPoolInfoSet.json";
//  	rapidjson::Document doc;
// 	//??ȡ?????ݣ???ʼ??doc  
//  	std::string data = FileUtils::getInstance()->getStringFromFile(filename);
//  	doc.<rapidjson::kParseDefaultFlags>(data.c_str());
// 	for (unsigned int i = 1; i < doc.Size(); i++)
// 	{
// 		//????ȡ??Ԫ??????ı??????????  
// 		rapidjson::Value &config = doc[i];
// 
// 		PlayerData::Info info;
// 		info.mid = config["RoleID"].GetInt();
// 		info.level = config["Level"].GetInt();

// 		info.type = config["Type"].GetInt();
// 		info.name = config["Name"].GetString();
// 		info.job = config["Job"].GetInt();
// 		info.appraisal = config["Appraisal"].GetInt();
// 		info.rareType = config["PlayType"].GetInt();
// 		info.bias = config["BiasAttack"].GetInt();
// 		info.defaultSkill = config["DefaultSkillID"].GetInt();
// 		info.maxEnergy = config["MaxEnergy"].GetInt();
// 
// 		GameData::getInstance()->setPlayerInfo(info);
// 	}
	
	//r_thread = new std::thread(&GameLayer::initConfigThread, this);

	//this->initConfigs(0);

	Layer::onEnter();
}

void GameLayer::initConfigThread()
{
	auto time = get_date_now();
	auto playersCSV = ResourceService::getInstance()->getCSVConfig("Configs/S_PlayerPoolInfoSet.csv");
	CCLOG("PlayerInfo time: %d", get_date_now() - time);
	for (int i = 0; i < playersCSV.size(); i++)
	{
		auto config = playersCSV[i];

		PlayerData::Info info;
		info.mid = config["RoleID"].asInt();
		info.level = config["Level"].asInt();
		info.type = config["Type"].asInt();
		info.name = LocalizeService::getInstance()->getString(config["Name"].asString());
		info.job = config["Job"].asInt();
		info.appraisal = config["Appraisal"].asInt();
		info.rareType = config["PlayType"].asInt();
		info.bias = config["BiasAttack"].asInt();
		info.defaultSkill = config["DefaultSkillID"].asInt();
		info.maxEnergy = config["MaxEnergy"].asInt();
		GameData::getInstance()->setPlayerInfo(info);

		PlayerData::Sound sound;
		sound.magic = config["MagicSound"].asString();
		sound.hitting = config["HittingSound"].asString();
		GameData::getInstance()->setPlayerSound(info.mid, sound);

		PlayerData::Attribute attri;
		attri.HP = config["MaxLife"].asInt();
		attri.HP_G = config["MaxLifeGrow"].asInt();
		attri.CP = config["CaptainValue"].asInt();
		attri.NP = config["NavigationValue"].asInt();
		attri.OP = config["OperateValue"].asInt();
		attri.STR = config["StrengthValue"].asInt();
		attri.STR_G = config["StrengthGrowValue"].asInt();
		attri.INC = config["BrainsValue"].asInt();
		attri.INC_G = config["BrainsGrowValue"].asInt();
		attri.CRT = config["CritValue"].asFloat();
		attri.ATT = config["PenetrationAttackValue"].asFloat();
		attri.LUK = config["LuckyValue"].asFloat();
		attri.BLK = config["ParryValue"].asFloat();
		attri.EVA = config["DodgeValue"].asFloat();
		GameData::getInstance()->setPlayerAttribute(info.mid, attri);
	}

	time = get_date_now();
	auto skillsCSV = ResourceService::getInstance()->getCSVConfig("Configs/S_MagicInfoSet.csv");
	CCLOG("SkillInfo time: %d", get_date_now() - time);
	for (auto i = 0; i < skillsCSV.size(); i++)
	{
		auto config = skillsCSV[i];

		auto skill = SkillData::create();

		auto &info = skill->getInfo();
		info.mid = config["MagicID"].asInt();
		info.icon = config["Icon"].asInt();
		info.level = config["CurrentLevel"].asInt();
		info.next = config["OnLevel"].asInt();
		info.name = config["Name"].asString();
		info.extent = config["Extent"].asInt();
		info.cdTime = config["LevelTime"].asInt() * 60;
		info.target = config["Target"].asInt();

		auto &effect = skill->getEffect();
		effect.type = config["EffectID"].asInt();
		effect.value = config["EffectValue"].asFloat();

		auto &need = skill->getNeed();
		need.level = config["NextLevel"].asInt();
		need.money = config["NextCoin"].asInt();

		auto &trigger = skill->getTrigger();
		trigger.type = config["TriggerType"].asInt();
		trigger.extent = config["TriggerAreaType"].asInt();
		trigger.effect = config["TriggerEffectId"].asString();
		trigger.count = config["TriggerCount"].asInt();
		trigger.sound = config["TriggerSound"].asString();

		auto &shooter = skill->getShooter();
		shooter.mid = config["ShooterId"].asString();
		shooter.extent = config["ShooterAreaType"].asInt();
		shooter.penetration = config["IsPenetration"].asBool();
		shooter.sound = config["ShooterSound"].asString();

		auto &hitting = skill->getHitting();
		hitting.effect = config["HittingEffectId"].asString();
		hitting.extent = config["HittingAreaType"].asInt();

		GameData::getInstance()->setSkill(skill);
	}

	for (auto config : GAME_CONFIGS)
	{
		CCLOG("config : %s", config.c_str());
		auto time = get_date_now();
		Configuration::getInstance()->loadConfigFile(config);
		CCLOG("config time: %d", get_date_now() - time);
	}

	//this->initConfigs(0);
}

void GameLayer::initConfigs(float dt)
{
// 	for (auto config : GAME_CONFIGS)
// 	{
// 		CCLOG("config : %s", config.c_str());
// 		Configuration::getInstance()->loadConfigFile(config);
// 	}

// 	r_thread->join();
// 	delete r_thread;
// 	r_thread = nullptr;

	LocalizeService::getInstance()->loadStrings();

	this->initConfigThread();

	CCLOG("config file: %s", UserDefault::getInstance()->getXMLFilePath().c_str());

	auto version = UserDefault::getInstance()->getStringForKey("Version");
	if (version != "1.0.0")
	{
		UserDefault::getInstance()->setStringForKey("Version", "1.0.0");
 		UserDefault::getInstance()->setStringForKey("Server", "115.28.221.144");
 		UserDefault::getInstance()->setStringForKey("EntryServer", "115.28.221.144:3000");
		UserDefault::getInstance()->setFloatForKey("ActionDelay", 0.2f);
		UserDefault::getInstance()->setFloatForKey("ActionDeadDelay", 0.5f);
		UserDefault::getInstance()->setFloatForKey("LabelDamageStartX", 124);
		UserDefault::getInstance()->setFloatForKey("LabelDamageStartY", 137);
		UserDefault::getInstance()->setFloatForKey("LabelDamageLastX", 124);
		UserDefault::getInstance()->setFloatForKey("LabelDamageLastY", 195);
		UserDefault::getInstance()->setFloatForKey("LabelTypeStartX", 0);
		UserDefault::getInstance()->setFloatForKey("LabelTypeStartY", 148);
		UserDefault::getInstance()->setFloatForKey("LabelTypeLastX", 0);
		UserDefault::getInstance()->setFloatForKey("LabelTypeLastY", 206);
		UserDefault::getInstance()->setFloatForKey("LabelDelay", 1.0f);
		UserDefault::getInstance()->flush();
	}

	auto token = UserDefault::getInstance()->getStringForKey("Token");

// 	if (token == "")
// 	{
// 		UserDefault::getInstance()->setStringForKey("Token", "");
// 		UserDefault::getInstance()->setIntegerForKey("City", 14);
// 		UserDefault::getInstance()->setBoolForKey("InCity", true);
// 		UserDefault::getInstance()->setIntegerForKey("GuideSave", 1);
// 		UserDefault::getInstance()->setIntegerForKey("TargetCity", 2);
// 		UserDefault::getInstance()->setBoolForKey("Guider", true);
// 		UserDefault::getInstance()->flush();
// 	}

#if (SOS_PLATFORM == SOS_PLATFORM_ANDROID)
	std::string empty = "";
	token = CMHelper::GetCMUserID() + empty;
	CCLOG("cm token: %s", token.c_str());
	if (token != "")
		UserDefault::getInstance()->setStringForKey("Token", token);
// #elif (SOS_PLATFORM == SOS_PLATFORM_XAMI)
// 	auto token = UserDefault::getInstance()->getStringForKey("Token");
#endif

	GameData::getInstance()->server = "http://" + UserDefault::getInstance()->getStringForKey("Server") + "/";
	GameData::getInstance()->entryServer = "http://" + UserDefault::getInstance()->getStringForKey("EntryServer") + "/";
	GameData::getInstance()->token = UserDefault::getInstance()->getStringForKey("Token");
	GameData::getInstance()->actionDelay = UserDefault::getInstance()->getFloatForKey("ActionDelay");
	GameData::getInstance()->actionDeadDelay = UserDefault::getInstance()->getFloatForKey("ActionDeadDelay");
	GameData::getInstance()->labelDamageStartX = UserDefault::getInstance()->getFloatForKey("LabelDamageStartX");
	GameData::getInstance()->labelDamageStartY = UserDefault::getInstance()->getFloatForKey("LabelDamageStartY");
	GameData::getInstance()->labelDamageLastX = UserDefault::getInstance()->getFloatForKey("LabelDamageLastX");
	GameData::getInstance()->labelDamageLastY = UserDefault::getInstance()->getFloatForKey("LabelDamageLastY");
	GameData::getInstance()->labelTypeStartX = UserDefault::getInstance()->getFloatForKey("LabelTypeStartX");
	GameData::getInstance()->labelTypeStartY = UserDefault::getInstance()->getFloatForKey("LabelTypeStartY");
	GameData::getInstance()->labelTypeLastX = UserDefault::getInstance()->getFloatForKey("LabelTypeLastX");
	GameData::getInstance()->labelTypeLastY = UserDefault::getInstance()->getFloatForKey("LabelTypeLastY");
	GameData::getInstance()->labelDelay = UserDefault::getInstance()->getFloatForKey("LabelDelay");

// 	auto city = UserDefault::getInstance()->getIntegerForKey("City");
// 	auto guider = UserDefault::getInstance()->getBoolForKey("Guider");
// 	GameData::getInstance()->getUserData()->getInfo().city = city;
// 	GameData::getInstance()->getUserData()->getInfo().targetCity = UserDefault::getInstance()->getIntegerForKey("TargetCity");
// 	GameData::getInstance()->getUserData()->getInfo().inCity = UserDefault::getInstance()->getBoolForKey("InCity");
// 	GameData::getInstance()->getUserData()->getInfo().state = (guider ? UserData::StateType::GUIDER : 0);
// 
// 	auto guideSave = UserDefault::getInstance()->getIntegerForKey("GuideSave");
// 	GameData::getInstance()->getUserData()->getGuidance().id = guideSave;
// 	GameData::getInstance()->getUserData()->getGuidance().action = 1;

// 	auto playersConfig = Configuration::getInstance()->getValue(PLAYER_CONFIG).asValueMap();
// 	for (auto playerConfig : playersConfig)
// 	{
// 		auto config = playerConfig.second.asValueMap();
// 
//  		PlayerData::Info info;
// 		info.mid = config["RoleID"].asInt();
// 		info.level = config["Level"].asInt();
// 		info.type = config["Type"].asInt();
// 		info.name = config["Name"].asString();
// 		info.job = config["Job"].asInt();
// 		info.appraisal = config["Appraisal"].asInt();
// 		info.rareType = config["PlayType"].asInt();
// 		info.bias = config["BiasAttack"].asInt();
// 		info.defaultSkill = config["DefaultSkillID"].asInt();
// 		info.maxEnergy = config["MaxEnergy"].asInt();
// 		GameData::getInstance()->setPlayerInfo(info);

// 		PlayerData::Sound sound;
// 		sound.magic = config["MagicSound"].asString();
// 		sound.hitting = config["HittingSound"].asString();
// 		GameData::getInstance()->setPlayerSound(info.mid, sound);
// 
// 		PlayerData::Attribute attri;
// 		attri.HP = config["MaxLife"].asInt();
// 		attri.HP_G = config["MaxLifeGrow"].asInt();
// 		attri.CP = config["CaptainValue"].asInt();
// 		attri.NP = config["NavigationValue"].asInt();
// 		attri.OP = config["OperateValue"].asInt();
// 		attri.STR = config["StrengthValue"].asInt();
// 		attri.STR_G = config["StrengthGrowValue"].asInt();
// 		attri.INC = config["BrainsValue"].asInt();
// 		attri.INC_G = config["BrainsGrowValue"].asInt();
// 		attri.CRT = config["CritValue"].asFloat();
// 		attri.ATT = config["PenetrationAttackValue"].asFloat();
// 		attri.LUK = config["LuckyValue"].asFloat();
// 		attri.BLK = config["ParryValue"].asFloat();
// 		attri.EVA = config["DodgeValue"].asFloat();
// 		GameData::getInstance()->setPlayerAttribute(info.mid, attri);
/*	}*/

	auto itemsConfig = Configuration::getInstance()->getValue(PROPS_CONFIG).asValueMap();
	for (auto itemConfig : itemsConfig)
	{
		auto config = itemConfig.second.asValueMap();

		ItemData::Info item;
		item.mid = config["PropsID"].asInt();
		item.name = config["Name"].asString();
		item.icon = config["IconID"].asInt();
		item.type = config["Type"].asInt();
		item.quality = config["Quality"].asInt();
		item.require = config["PlayerLevelRequire"].asInt();
		item.artillery = config["ArtilleryID"].asInt();
		item.stack = config["StackNum"].asInt();
		item.equip = config["EquipIndex"].asInt();
		item.sell = config["SellPrice"].asInt();
		item.descrip = config["Description"].asString();
		item.buy = config["BuyPrice"].asInt();
		item.jobs = splitToInt(config["Job"].asString(), "|");

		auto effects = split(config["Effects"].asString(), "|");
		for (auto effect : effects)
		{
			auto ef = split(effect, ",");
			if (ef[0] == "0") continue;

			ItemData::Info::Effect fx;
			fx.type = INT(ef[0]);
			fx.value = FLOAT(ef[1]);
			item.effects.push_back(fx);
		}

		GameData::getInstance()->setItem(item);
	}

	auto suc = Configuration::getInstance()->getValue(SHIPROOM_UPGRADE_CONFIG).asValueMap();
	for (auto cc : suc)
	{
		auto config = cc.second.asValueMap();

		auto cabin = CabinData::create();

		auto &info = cabin->getInfo();
		info.mid = config["RoomID"].asInt();
		info.type = config["RoomType"].asInt();
		info.level = config["Level"].asInt();
		info.appointCount = config["AppointCount"].asInt();
		info.subjoin1 = FLOAT(config["Subjoin1"].asString());
		info.subjoin2 = FLOAT(config["Subjoin2"].asString());
		info.subjoin3 = FLOAT(config["Subjoin3"].asString());

		auto &require = cabin->getRequire();
		require.captain = config["ReqCaptainHouseID"].asInt();
		require.money = config["ReqCoin"].asInt();
		require.level = config["ReqUserLevel"].asInt();
		require.cd = config["CDTime"].asInt();

		GameData::getInstance()->setCabin(cabin);
	}

	auto scs = Configuration::getInstance()->getValue(SHIP_CONFIG).asValueMap();
	for (auto sc : scs)
	{
		auto config = sc.second.asValueMap();

		auto ship = ShipData::create();

		auto &info = ship->getInfo();
		info.mid = INT(sc.first);
		info.name = config["Name"].asString();
		info.level = config["Level"].asInt();
		info.icon = config["Icon"].asInt();
		info.artillery = config["ArtilleryID"].asInt();
		info.type = config["Type"].asInt();
		info.recharge = config["Recharge"].asInt();

		auto &require = ship->getRequire();
		require.level = config["RequireLevel"].asInt();
		require.renown = config["RequireRenown"].asInt();
		require.price = config["SellPrice"].asInt();

		auto cids = splitToInt(config["Cabins"].asString(), "|");
		for (auto cid : cids)
		{
			if (cid <= 0) continue;
			ship->getCabins().pushBack(GameData::getInstance()->getCabin(cid));
		}

		GameData::getInstance()->setShip(ship);
	}

	auto cargosConfig = Configuration::getInstance()->getValue(CARGOS_CONFIG).asValueMap();
	for (auto cargoConfig : cargosConfig)
	{
		auto config = cargoConfig.second.asValueMap();

		UserData::Cargo cargo;
		cargo.mid = config["TradablesID"].asInt();
		cargo.name = config["NameKey"].asString();
		cargo.icon = config["Icon"].asInt();
		cargo.space = config["RequireSpace"].asInt();
		cargo.explanation = config["ExplanationKey"].asString();

		GameData::getInstance()->setCargo(cargo);
	}

	auto upgradeLevelsConfig = Configuration::getInstance()->getValue(PLAYER_UPGRADE_CONFIG).asValueMap();
	for (auto upgradeLevelConfig : upgradeLevelsConfig)
	{
		auto upgradeLevel = upgradeLevelConfig.second.asValueMap();

		GameData::Level level;
		level.maxExp = upgradeLevel["ReqExp"].asInt();
		level.allExp = upgradeLevel["Max"].asInt();
		level.cues = splitToInt(upgradeLevel["Cue"].asString(), "|");
		level.level = upgradeLevel["Level"].asInt();
		level.position = upgradeLevel["Count"].asInt();

		GameData::getInstance()->setLevel(level);
	}

// 	auto skillsConfig = Configuration::getInstance()->getValue(SKILL_CONFIG).asValueMap();
// 	for (auto skillConfig : skillsConfig)
// 	{
// 		auto mid = INT(skillConfig.first);
// 		auto config = skillConfig.second.asValueMap();
// 
// 		auto skill = SkillData::create();
// 
// 		auto &info = skill->getInfo();
// 		info.mid = mid;
// 		info.icon = config["Icon"].asInt();
// 		info.level = config["CurrentLevel"].asInt();
// 		info.next = config["OnLevel"].asInt();
// 		info.name = config["Name"].asString();
// 		info.extent = config["Extent"].asInt();
// 		info.cdTime = config["LevelTime"].asInt() * 60;
// 		info.target = config["Target"].asInt();
// 
// 		auto &effect = skill->getEffect();
// 		effect.type = config["EffectID"].asInt();
// 		effect.value = config["EffectValue"].asFloat();
// 
// 		auto &need = skill->getNeed();
// 		need.level = config["NextLevel"].asInt();
// 		need.money = config["NextCoin"].asInt();
// 
// 		auto &trigger = skill->getTrigger();
// 		trigger.type = config["TriggerType"].asInt();
// 		trigger.extent = config["TriggerAreaType"].asInt();
// 		trigger.effect = config["TriggerEffectId"].asString();
// 		trigger.count = config["TriggerCount"].asInt();
// 		trigger.sound = config["TriggerSound"].asString();
// 
// 		auto &shooter = skill->getShooter();
// 		shooter.mid = config["ShooterId"].asString();
// 		shooter.extent = config["ShooterAreaType"].asInt();
// 		shooter.penetration = config["IsPenetration"].asBool();
// 		shooter.sound = config["ShooterSound"].asString();
// 
// 		auto &hitting = skill->getHitting();
// 		hitting.effect = config["HittingEffectId"].asString();
// 		hitting.extent = config["HittingAreaType"].asInt();
// 
// 		GameData::getInstance()->setSkill(skill);
// 	}

	auto cultivatesDiamondConfig = Configuration::getInstance()->getValue(PLAYER_CULTIVATE_DIAMOND_CONFIG).asValueMap();
	auto &cultivates = GameData::getInstance()->getCultivates();
	for (int i = 1; i <= CULTIVATE_COUNT; i++)
	{
		GameData::Cultivate cultivate = { i, 0, 0, 0, 0.0 };

		auto cultivateDiamondConfig = cultivatesDiamondConfig[STRING(i)].asValueMap();
		cultivate.coef1 = cultivateDiamondConfig["FirstCoefficient"].asInt();
		cultivate.coef2 = cultivateDiamondConfig["SecondCoefficient"].asInt();
		cultivate.diamond = cultivateDiamondConfig["ReqDiamond"].asInt();
		cultivate.rate = cultivateDiamondConfig["Rate"].asDouble();

		cultivates[i] = cultivate;
	}

	auto teamSkillRefreshCostConfig = Configuration::getInstance()->getValue(TEAM_SKILL_REFRESH_COST_CONFIG).asValueMap();
	auto &tsnm = GameData::getInstance()->getTeamSkillNeeds();
	for (int i = 1; i <= APPRAISAL_COUNT; i++)
	{
		auto tsrcc = teamSkillRefreshCostConfig[STRING(i)].asValueMap();

		for (int j = 1; j <= TEAM_SKILL_NEED_COUNT; j++)
		{
			auto tsrc = tsrcc[STRING(j)].asInt();

			GameData::TeamSkillNeed tsn;
			tsn.appraisal = i;
			tsn.type = j;
			tsn.value = tsrc;

			tsnm[i][j] = tsn;
		}
	}

	auto copiesConfig = Configuration::getInstance()->getValue(COPIES_CONFIG).asValueMap();
	auto checkpointInfoConfigs = Configuration::getInstance()->getValue(CHECKPOINT_CONFIG).asValueMap();
	auto citysConfig = Configuration::getInstance()->getValue(CITY_CONFIG).asValueMap();
	for (auto cityConfig : citysConfig)
	{
		auto config = cityConfig.second.asValueMap();

		auto city = CityData::create();
		city->getInfo().mid = config["CityID"].asInt();
		city->getInfo().name = config["CityName"].asString();
		city->getInfo().bg = config["BgID"].asInt();
		city->getInfo().type = config["CityType"].asInt();
		city->getInfo().seas = config["PertainSeas"].asInt();
		city->getInfo().funs = split(config["Function"].asString(), "|");
		city->getDock().ships = splitToInt(config["DockShips"].asString(), "|");

		auto copies = split(config["CityCopies"].asString(), "|");
		for (auto copyID : copies)
		{
			if (copyID == "0")
				continue;

			auto copyConfig = copiesConfig[copyID].asValueMap();
			auto copyData = CopyData::create();

			CopyData::Info& copyInfo = copyData->getInfo();
			copyInfo.bgID = copyConfig["BackgroundID"].asInt();
			copyInfo.mid = copyConfig["CopiesID"].asInt();
			copyInfo.name = copyConfig["Name"].asString();
			copyInfo.suitLevel = copyConfig["SuitLevel"].asString();
			copyInfo.level = copyConfig["ReqLevel"].asInt();
			copyInfo.task = copyConfig["ReqTaskID"].asInt();

			auto checkpoints = split(copyConfig["CheckpointStr"].asString(), "|");
			for (int i = 0; i < checkpoints.size(); i++)
			{
				auto chapterData = ChapterData::create();

				ChapterData::Info& chapterInfo = chapterData->getInfo();
				chapterInfo.mid = INT(checkpoints[i]);
				chapterInfo.evaluate = ChapterData::EvaluateType::NO_STAR;

				auto checkpointInfoConfig = checkpointInfoConfigs[checkpoints[i]].asValueMap();
				chapterInfo.name = checkpointInfoConfig["Name"].asString();
				chapterInfo.reqMobility = checkpointInfoConfig["ReqMobility"].asInt();
				chapterInfo.description = checkpointInfoConfig["Description"].asString();
				chapterInfo.start = checkpointInfoConfig["Start"].asString();
				chapterInfo.closing = checkpointInfoConfig["Closing"].asString();
				chapterInfo.head = checkpointInfoConfig["Head"].asInt();
				chapterInfo.suggest = checkpointInfoConfig["CopiesLevel"].asString();

				auto rewardConfigs = split(checkpointInfoConfig["Rewards"].asString(), "|");
				for (auto rewardConfig : rewardConfigs)
				{
					auto rewardObj = splitToInt(rewardConfig, ",");
					if (rewardObj[2] <= 0) continue;

					ChapterData::Reward reward;
					reward.type = rewardObj[0];
					reward.value = rewardObj[2];
					chapterData->getRewards().push_back(reward);
				}

				copyData->getChapters().insert(checkpoints[i], chapterData);
			}

			city->getCopies().insert(STRING(copyInfo.mid), copyData);
		}

		auto pointStr = config["Coordinate"].asString();
		if (pointStr != "0")
		{
			auto point = split(pointStr, ",");
			city->getInfo().crood = Point(INT(point[0]), INT(point[1]));
		}

		GameData::getInstance()->setCity(city);
	}

	auto citys = GameData::getInstance()->getCitys();
	auto tradablesHouseInfoConfigs = Configuration::getInstance()->getValue(TRADABLESHOUSE_CONFIG).asValueMap();
	for (auto city : citys)
	{
		auto carConfig = tradablesHouseInfoConfigs[STRING(city.first)].asValueVector();
		for (auto thic : carConfig)
		{
			auto car = thic.asValueMap();
			if (car["SellPrice"].asInt() > 0)
			{
				UserData::Cargo a;
				a.mid = car["SellTradablesID"].asInt();
				a.buy = car["SellPrice"].asInt();
				city.second->getCargos().push_back(a);
			}
		}
	}

	auto smithyConfig = Configuration::getInstance()->getValue(SMITHYINFO_CONFIG).asValueMap();
	for (auto config : smithyConfig)
	{
		auto cityData = GameData::getInstance()->getCity(INT(config.first));
		auto smithys = config.second.asValueVector();
		for (auto item : smithys)
		{
			auto& smithy = cityData->getSmithy();
			CityData::Smithy::SmithyInfo smithyInfo;
			smithyInfo.mid = item.asValueMap()["PropsID"].asInt();
			smithy.SmithyVector.push_back(smithyInfo);
		}
	}

	auto& quicks = GameData::getInstance()->getUserData()->getQuickens();
	auto quickConfigs = Configuration::getInstance()->getValue(QUICKEN_CONFIG).asValueMap();
	for (auto q : quickConfigs)
	{
		auto config = q.second.asValueMap();
		auto quick = new QuickenData();
		auto& info = quick->getInfo();
		info.quickTime = config["QuickTime"].asInt();
		info.type = config["Type"].asInt();
		info.totalTime = config["TotalTime"].asInt();
		info.reqValue = config["ReqValue"].asFloat();
		quicks.pushBack(quick);
	}



	//?????
	auto& novices = GameData::getInstance()->getUserData()->getNovices();
	auto newguidanceConfigs = Configuration::getInstance()->getValue(NEWGUIDANCE_CONFIG).asValueMap();
	for (auto noviceConfigs : newguidanceConfigs)
	{
		auto noviceCfgs = noviceConfigs.second.asValueMap();

		for (auto actionConfigs : noviceCfgs)
		{
			auto config = actionConfigs.second.asValueMap();

			auto novice = new NoviceData();

			auto& info = novice->getInfo();
			info.id = config["GuideID"].asInt();
			info.action = config["NextGuideID"].asInt();
			info.type = config["Type"].asInt();
			info.ui = config["UITag"].asInt();
			info.dialog = config["NPCstatus"].asString();
			info.save = config["Save"].asInt();
			info.effect = config["WordPoint"].asString();
			info.value = config["Trigger"].asInt();
			info.scene = config["TriggerType"].asInt();

			auto& mask = novice->getMask();
			auto shapeStr = config["ArrowEvert"].asString();
			if (shapeStr != "0")
			{
				auto shape = split(shapeStr, ",");
				mask.anchor = INT(shape[0]);
				mask.type = INT(shape[1]);
				mask.width = INT(shape[2]);
				if (mask.type == 1)
				{
					mask.height = INT(shape[3]);
				}
			}
			auto pointStr = config["UIPoint"].asString();
			if (pointStr != "0")
			{
				auto tags = splitToInt(pointStr, "|");
				mask.tags = tags;
			}

			auto& arrow = novice->getArrow();
			arrow.id = config["ArrowID"].asString();
			arrow.content = config["Content"].asString();
			pointStr = config["ArrowPoint"].asString();
			if (pointStr != "0")
			{
				auto point = split(pointStr, ",");
				arrow.point = Point(INT(point[0]), INT(point[1]));
			}

			auto& animation = novice->getAnimation();
			animation.id = config["AnimationID"].asString();
			pointStr = config["AnimationPoint"].asString();
			if (pointStr != "0")
			{
				auto point = split(pointStr, ",");
				animation.point = Point(INT(point[0]), INT(point[1]));
			}
			auto scaleStr = config["AnimationZoom"].asString();
			if (scaleStr != "0")
			{
				auto scale = split(scaleStr, "|");
				animation.scaleX = FLOAT(scale[0]);
				animation.scaleY = FLOAT(scale[1]);
			}
			else
			{
				animation.scaleX = 1;
				animation.scaleY = 1;
			}

			novices[novice->getInfo().id][novice->getInfo().action] = novice;
		}
	}

	auto recordRewardConfigs = Configuration::getInstance()->getValue(RECORDREWARD_CONFIG).asValueMap();
	for (auto rrcs : recordRewardConfigs)
	{
		auto noviceCfgs = rrcs.second.asValueMap();

		GameData::RecordReward record;
		record.rewardID = noviceCfgs["RewardID"].asInt();
		record.reward = noviceCfgs["Reward"].asString();
		GameData::getInstance()->setRecordReward(record);

	}

	//Mall
	long fZize = 0;
	unsigned char * buff = NULL;
	//??ip???ȡͼƬ???
// 	buff = CCFileUtils::sharedFileUtils()->getFileDataFromZip("Configs/Configs.zip", "Mall.json", &fZize);
// 	auto mallJson = String::createWithData(buff, fZize);
	std::string filename = "Configs/Mall.json";
	rapidjson::Document doc;
	//???ļ?????  
	if (FileUtils::getInstance()->isFileExist(filename))
	{
		//??ȡ?????ݣ???ʼ??doc  
		std::string fdata = FileUtils::getInstance()->getStringFromFile(filename);
		doc.Parse<rapidjson::kParseDefaultFlags>(fdata.c_str());
		//doc.Parse<rapidjson::kParseDefaultFlags>(mallJson->getCString());
		//??϶?ȡ?ɹ??????????????? 
		if (doc.HasParseError() || !doc.IsArray())
		{
			log("get json data err!");
		}
		else
		{
			for (unsigned int i = 0; i < doc.Size(); i++)
			{
				//????ȡ??Ԫ??????ı??????????  
				rapidjson::Value &v = doc[i];

				auto mall = new MallData();
				auto& info = mall->getInfo();

				//?????ȡ???
				auto id = v["GoodsID"].GetInt();
				info.billingIndex = String::createWithFormat("%03d", id)->getCString();
				info.currencyValue = v["CurrencyValue"].GetDouble();
				info.propsType = v["PropsType"].GetInt();
				info.propsValue = v["PropsValue"].GetInt();
				info.sorting = v["Sorting"].GetInt();

				GameData::getInstance()->setMall(mall);
			}
		}
	}

	//LoginTransaction::getInstance()->login(GameData::getInstance()->getUserData()->getInfo().uid);
	GameData::getInstance()->guideID = UserDefault::getInstance()->getIntegerForKey("GuideID");
	Director::getInstance()->replaceScene(EntryScene::createScene());
}
