#ifndef __HEAD_VIEW_H__
#define __HEAD_VIEW_H__

#include "cocos2d.h"
#include "ui/CocosGUI.h"
#include "cocostudio/CocoStudio.h"
#include "../Views/PopupView.h"
#include "../SOSConfig.h"
#include "../GameData.h"

class HeadView :public PopupView
{
public:
	CREATE_FUNC(HeadView);
public:
	HeadView();
	virtual ~HeadView();
	virtual bool init() override;
	virtual void onEnter() override;
	void touchButton(cocos2d::Ref* object, cocos2d::ui::TouchEventType type);
private:
	cocos2d::ui::Widget* ui;
};

#endif 