#include "Newguidance.h"
#include "../Services/LocalizeService.h"
#include "../Views/NPCView.h"
#include "../Scenes/BaseScene.h"

USING_NS_CC;
USING_NS_CC_CCS;
USING_NS_CC_UI;

enum TagNewguidance
{
// 	UI_IMAGEVIEW_NPC = 20009,
// 	UI_IMAGEVIEW_SPEAKBG = 20010,
// 	UI_TEXT_CONTENT = 20011,
// 
// 	UI_TEXT_WORD = 20013,
// 	UI_IMAGEVIEW_BG = 20012,
	UI_TEXT_CONTENT = 27000,
};

Newguidance::Newguidance()
{
	
}

Newguidance::~Newguidance()
{
	//_listener = nullptr;
	//ActionManagerEx::getInstance()->releaseActions();

	ActionManagerEx::getInstance()->releaseActionByName("UI_Arrow_1.json");
	ActionManagerEx::getInstance()->releaseActionByName("UI_Arrow_2.json");
	ActionManagerEx::getInstance()->releaseActionByName("UI_Arrow_3.json");
	ActionManagerEx::getInstance()->releaseActionByName("UI_Arrow_4.json");
}

Layer* Newguidance::createScene()
{
	auto layer = Newguidance::create();
	return layer;
}

bool Newguidance::init()
{
	if (!Layer::init())
	{
		return false;
	}

	auto userData = GameData::getInstance()->getUserData();
	auto novices = userData->getNovices();

	_noviceData = novices.at(userData->getGuidance().id).at(userData->getGuidance().action);

	createView();

	if (_noviceData->getArrow().id != "0")
	{
		auto arrow = _noviceData->getArrow();
		auto uiName = arrow.id;
		auto ui = CCS_CREATE_LAYER(("UI_Newguidance/" + uiName).c_str());
		auto point = _noviceData->getMask().point;
		point.add(arrow.point);
		ui->setPosition(point);
		//ui->setPosition(arrow.point);
		this->addChild(ui);
		ActionManagerEx::getInstance()->playActionByName((uiName + ".json").c_str(), "Animation0");
		if (arrow.content != "0")
		{
			CCS_GET_CHILD(ui, Text, contentText, UI_TEXT_CONTENT);
			contentText->setText(LocalizeService::getInstance()->getString(arrow.content));
		}
	}

	if (_noviceData->getAnimation().id != "0")
	{
		auto animation = _noviceData->getAnimation();
		ArmatureDataManager::getInstance()->addArmatureFileInfo("Animation_Light.ExportJson");
		auto armature = Armature::create("Animation_Light");
		auto point = _noviceData->getMask().point;
		point.add(animation.point);
		armature->getAnimation()->play(animation.id);
		armature->setScaleX(animation.scaleX);
		armature->setScaleY(animation.scaleY);
		armature->setPosition(point);
		//armature->setPosition(animation.point);
		this->addChild(armature);
	}

	if (_noviceData->getInfo().effect != "0")
	{
		auto size = Director::getInstance()->getWinSize();
		ArmatureDataManager::getInstance()->addArmatureFileInfo("Animation_word.ExportJson");
		auto armature = Armature::create("Animation_word");
		armature->getAnimation()->play("word");
		//armature->getAnimation()->setMovementEventCallFunc(CC_CALLBACK_0(Newguidance::onMovementEvent, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3));
		armature->setPosition(Point(size.width * 0.5, size.height * 0.5));
		this->addChild(armature);
	}

	auto listener = EventListenerTouchOneByOne::create();
	listener->setSwallowTouches(true);
	listener->onTouchBegan = CC_CALLBACK_2(Newguidance::onTouchBegan, this);
	listener->onTouchMoved = CC_CALLBACK_2(Newguidance::onTouchMoved, this);
	listener->onTouchEnded = CC_CALLBACK_2(Newguidance::onTouchEnded, this);
	this->getEventDispatcher()->addEventListenerWithSceneGraphPriority(listener, this);

	return true;
}

void Newguidance::menuCloseCallback(Ref* pSender)
{
	Director::getInstance()->end();

#if (CC_TARGET_PLATFORM == CC_PLATFORM_IOS)
	exit(0);
#endif
}


bool Newguidance::onTouchBegan(Touch* touch, Event* event)
{
	CCLOG("ccTouchBegan");
	CCLOG("x:%f,y:%f", touch->getLocation().x, touch->getLocation().y);
	auto p = touch->getLocation();
	auto mask = _noviceData->getMask();

	if (mask.type == 0)
	{
		if (p.getDistance(mask.point) <= mask.width)
		{
			return false;
		}
	}
	else if (mask.type == 1)
	{
		Rect rect;
		if (mask.anchor == 0)
		{
			rect = Rect(mask.point.x - mask.width * 0.5, mask.point.y - mask.height * 0.5, mask.width, mask.height);
		}
		else if (mask.anchor == 1)
		{
			rect = Rect(mask.point.x, mask.point.y, mask.width, mask.height);
		}

		if (rect.containsPoint(p))
		{
			return false;
		}
	}
	return true;
}

void Newguidance::onTouchMoved(Touch* touch, Event* event){
	CCLOG("ccTouchMoved");
}

void Newguidance::onTouchEnded(Touch* touch, Event* event)
{
	CCLOG("x:%f,y:%f", touch->getLocation().x, touch->getLocation().y);
}

void Newguidance::onEnter()
{
	Layer::onEnter();
}

void Newguidance::createView()
{
	auto &mask = _noviceData->getMask();

	if (_noviceData->getInfo().ui != 0)
	{
		auto layer = Director::getInstance()->getRunningScene()->getChildByTag(TagBaseScene::SCENE);
		for (int i = 0; i < mask.tags.size(); i++)
		{
			layer = layer->getChildByTag(mask.tags[i]);
		}
		auto point = this->convertToNodeSpace(layer->getParent()->convertToWorldSpace(layer->getPosition()));
		CCLOG("%f, %f", point.x, point.y);
		mask.point = point;
	}

	if (mask.type == 0)
	{
		auto size = Director::getInstance()->getWinSize();

		ClippingNode *clippingNode = ClippingNode::create();
		clippingNode->setInverted(true);
		clippingNode->setContentSize(size);
		this->addChild(clippingNode);

		Color4F color = Color4F::RED;
		float radius = mask.width;
		const int nCount = 200;
		const float angel = 2.0f*(float)M_PI / nCount;
		Point circle[nCount];
		for (int i = 0; i < nCount; i++)
		{
			float radian = i*angel;
			circle[i].x = radius * cosf(radian);
			circle[i].y = radius * sinf(radian);
		}
		DrawNode *pStencil = DrawNode::create();
		pStencil->drawPolygon(circle, nCount, color, 0, color);
		pStencil->setPosition(mask.point);
		clippingNode->setStencil(pStencil);
		
		LayerColor* pLayer = LayerColor::create(Color4B(0, 0, 0, 150));
		pLayer->setContentSize(size);
		clippingNode->addChild(pLayer);
	}
	else if (mask.type == 1)
	{
		auto size = Director::getInstance()->getWinSize();

		ClippingNode *clippingNode = ClippingNode::create();
		clippingNode->setInverted(true);
		clippingNode->setContentSize(size);
		this->addChild(clippingNode);

		DrawNode *pStencil = DrawNode::create();

		if (mask.anchor == 0)
		{
			Point rect[4] = { ccp(-mask.width * 0.5, mask.height * 0.5), ccp(mask.width * 0.5, mask.height * 0.5), ccp(mask.width * 0.5, -mask.height * 0.5), ccp(-mask.width * 0.5, -mask.height * 0.5) };
			pStencil->drawPolygon(rect, 4, Color4F(1, 0, 0, 1), 0, Color4F(1, 0, 0, 1));
		}
		else if (mask.anchor == 1)
		{
			Point rect[4] = { ccp(0, mask.height), ccp(mask.width, mask.height), ccp(mask.width, 0), ccp(0, 0) };
			pStencil->drawPolygon(rect, 4, Color4F(1, 0, 0, 1), 0, Color4F(1, 0, 0, 1));
		}
		
		pStencil->setPosition(mask.point);
		clippingNode->setStencil(pStencil);

		LayerColor* pLayer = LayerColor::create(Color4B(0, 0, 0, 150));
		pLayer->setContentSize(size);
		clippingNode->addChild(pLayer);
	}
}

void Newguidance::getRunScene()
{
	auto scene = Director::getInstance()->getRunningScene();
	for (auto node : scene->getChildren())
	{
		if (node->getTag() == _noviceData->getInfo().ui)
		{
			for (auto child : node->getChildren())
			{
				if (child->getTag() == _noviceData->getInfo().ui)
				{
					break;
				}
			}
		}
	}
}

void Newguidance::removeFromParentWithDT(float dt)
{
	this->removeFromParent();
}
