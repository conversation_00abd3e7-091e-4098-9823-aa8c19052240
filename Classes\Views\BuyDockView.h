#ifndef __BUYDOCK_VIEW_H__
#define __BUYDOCK_VIEW_H__

#include "cocos2d.h"
#include "ui/CocosGUI.h"
#include "cocostudio/CocoStudio.h"
#include "../SOSConfig.h"
#include "../GameData.h"
#include "PopupView.h"

static const std::string EVENT_BUYSHOPVIEW_CHANGED = "event_buyshopview_changed";

class BuyDockView : public PopupView
{
public:
	CREATE_FUNC(BuyDockView);

	static cocos2d::Layer* createScene();
	static cocos2d::Layer* createScene(int shipID,int Price);

public:
	BuyDockView();
	virtual ~BuyDockView();

	virtual bool init() override;
	virtual void onEnter() override;
	void touchButton(cocos2d::Ref* object, cocos2d::ui::TouchEventType type);

private:
	cocos2d::Node* _myLayout;
};

#endif // __BUYDOCK_VIEW_H__