#ifndef __JNI_CM_HELPER_H__
#define __JNI_CM_HELPER_H__

#include "cocos2d.h"
using namespace cocos2d;

#if (CC_TARGET_PLATFORM == CC_PLATFORM_ANDROID)
#include <jni.h>
#include "platform/android/jni/JniHelper.h"
#include <android/log.h>
#endif

class CMHelper
{
public:
	static std::string GetCMUserID() 
	{
		std::string userID;

#if (CC_TARGET_PLATFORM == CC_PLATFORM_ANDROID)
		JniMethodInfo minfo;
		bool isHave = JniHelper::getStaticMethodInfo(minfo,  "com/xami/sos/CMHelper","getUserID","()Ljava/lang/String;");
		jobject jobj;
		if (!isHave) {
			CCLog("[JNI]: no java method --- CMHelper:getUserID");
		}else{
			CCLog("[JNI]: call CMHelper:getUserID");
			jobj = minfo.env->CallStaticObjectMethod(minfo.classID, minfo.methodID);
			jstring jstr = (jstring)jobj;
			JNIEnv* env = JniHelper::getEnv();
			auto str = env->GetStringUTFChars(jstr, 0);
			CCLOG("[JNI]: %s", str);
			userID = str;
			env->ReleaseStringUTFChars(jstr, str);
			CCLOG("[JNI]: %s", userID.c_str());
		}
		CCLog("[JNI]: get user id complete ...");
#endif

		return userID;
	}

	static bool GetMusicEnabled() 
	{
		bool isMusic = true;

#if (CC_TARGET_PLATFORM == CC_PLATFORM_ANDROID)
		JniMethodInfo minfo;
		bool isHave = JniHelper::getStaticMethodInfo(minfo,  "com/xami/sos/CMHelper","getMusicEnabled","()Z");
		jboolean ret = true;
		if (!isHave) {
			CCLog("[JNI]: no java method --- CMHelper:getMusicEnabled");
		}else{
			CCLog("[JNI]: call CMHelper:getMusicEnabled");
			ret = minfo.env->CallStaticBooleanMethod(minfo.classID, minfo.methodID);
			isMusic = ret;
			CCLOG("[JNI]: music %d", isMusic);
		}
		CCLog("[JNI]: get music enabled complete ...");
#endif

		return isMusic;
	}

	static void ExitGame()
	{
#if (CC_TARGET_PLATFORM == CC_PLATFORM_ANDROID)
		JniMethodInfo minfo;
		bool isHave = JniHelper::getStaticMethodInfo(minfo, "com/xami/sos/CMHelper", "exitGame", "()V");

		if (!isHave) {
			CCLog("[JNI]: no java method --- CMHelper:exitGame");
		}
		else{
			CCLog("[JNI]: call CMHelper:exitGame");
			minfo.env->CallStaticVoidMethod(minfo.classID, minfo.methodID);
		}
		CCLog("[JNI]: exit game complete ...");
#endif
	}

	static void PayMoney(const char* billingIndex, const char* userId)
	{
#if (CC_TARGET_PLATFORM == CC_PLATFORM_ANDROID)
		JniMethodInfo minfo;
		bool isHave = JniHelper::getStaticMethodInfo(minfo, "com/xami/sos/CMHelper", "payMoney", "(Ljava/lang/String;Ljava/lang/String;)V");

		if (!isHave) {
			CCLog("[JNI]: no java method --- CMHelper:payMoney");
		}
		else{
			CCLog("[JNI]: call CMHelper:payMoney");
			jstring jBillingIndex = minfo.env->NewStringUTF(billingIndex);
			jstring jUserID = minfo.env->NewStringUTF(userId);
			minfo.env->CallStaticVoidMethod(minfo.classID, minfo.methodID, jBillingIndex, jUserID);
		}
		CCLog("[JNI]: pay money complete ...");
#endif
	}
};

#endif  //__JNI_CM_HELPER_H__
