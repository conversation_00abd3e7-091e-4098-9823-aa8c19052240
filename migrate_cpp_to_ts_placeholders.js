const fs = require('fs');
const path = require('path');

const baseTsPath = path.join('assets', 'Scripts');
const workspaceRoot = __dirname; // Script is in project root

const filesToCreate = [
    // Root
    { cppName: 'AppDelegate', type: 'class' },
    { cppName: 'GameData', type: 'class' },
    { cppName: 'GameScene', type: 'class' },
    { cppName: 'GlobalSchedule', type: 'class' },
    { cppName: 'Item', type: 'class' },
    { cppName: 'JniHelper', type: 'class' },
    { cppName: 'LoadingScene', type: 'class' },
    { cppName: 'MapScene', type: 'class' },
    { cppName: 'SOSConfig', type: 'class' },
    { cppName: 'ShaderSprite', type: 'class' },
    { cppName: 'Ship', type: 'class' },

    // Datas
    { dir: 'Datas', cppName: 'CityData', type: 'class' },
    { dir: 'Datas', cppName: 'CopyData', type: 'class' },
    { dir: 'Datas', cppName: 'FightData', type: 'class' },
    { dir: 'Datas', cppName: 'ItemData', type: 'class' },
    { dir: 'Datas', cppName: 'MallData', type: 'class' },
    { dir: 'Datas', cppName: 'NoviceData', type: 'class' },
    { dir: 'Datas', cppName: 'PlayerData', type: 'class' },
    { dir: 'Datas', cppName: 'QuickenData', type: 'class' },
    { dir: 'Datas', cppName: 'RankData', type: 'class' },
    { dir: 'Datas', cppName: 'ShipData', type: 'class' },
    { dir: 'Datas', cppName: 'SkillData', type: 'class' },
    { dir: 'Datas', cppName: 'TaskData', type: 'class' },
    { dir: 'Datas', cppName: 'TeamData', type: 'class' },
    { dir: 'Datas', cppName: 'UserData', type: 'class' },

    // Platforms
    { dir: 'Platforms', cppName: 'CMHelper', type: 'class' },

    // Scenes
    { dir: 'Scenes', cppName: 'ArenaScene', type: 'class' },
    { dir: 'Scenes', cppName: 'BaseScene', type: 'class' },
    { dir: 'Scenes', cppName: 'CityScene', type: 'class' },
    { dir: 'Scenes', cppName: 'CreatePersonScene', type: 'class' },
    { dir: 'Scenes', cppName: 'DockScene', type: 'class' },
    { dir: 'Scenes', cppName: 'EntryScene', type: 'class' },
    { dir: 'Scenes', cppName: 'FightScene', type: 'class' },
    { dir: 'Scenes', cppName: 'FormationScene', type: 'class' },
    { dir: 'Scenes', cppName: 'GovernmentScene', type: 'class' },
    { dir: 'Scenes', cppName: 'HonorScene', type: 'class' },
    { dir: 'Scenes', cppName: 'LoadingResourceScene', type: 'class' },
    { dir: 'Scenes', cppName: 'MallScene', type: 'class' },
    { dir: 'Scenes', cppName: 'MinimapScene', type: 'class' },
    { dir: 'Scenes', cppName: 'PartnerScene', type: 'class' },
    { dir: 'Scenes', cppName: 'PersonScene', type: 'class' },
    { dir: 'Scenes', cppName: 'PubScene', type: 'class' },
    { dir: 'Scenes', cppName: 'SettleScene', type: 'class' },
    { dir: 'Scenes', cppName: 'ShipScene', type: 'class' },
    { dir: 'Scenes', cppName: 'ShopScene', type: 'class' },
    { dir: 'Scenes', cppName: 'StartScene', type: 'class' },
    { dir: 'Scenes', cppName: 'WeaponShopScene', type: 'class' },

    // Services
    { dir: 'Services', cppName: 'EffectService', type: 'class' },
    { dir: 'Services', cppName: 'HTTPService', type: 'class' },
    { dir: 'Services', cppName: 'LocalizeService', type: 'class' },
    { dir: 'Services', cppName: 'NewguidanceService', type: 'class' },
    { dir: 'Services', cppName: 'ResourceService', type: 'class' },
    { dir: 'Services', cppName: 'SoundService', type: 'class' },

    // Transactions
    { dir: 'Transactions', cppName: 'ArenaTransaction', type: 'class' },
    { dir: 'Transactions', cppName: 'CreateTransaction', type: 'class' },
    { dir: 'Transactions', cppName: 'DockTransaction', type: 'class' },
    { dir: 'Transactions', cppName: 'FightTransaction', type: 'class' },
    { dir: 'Transactions', cppName: 'FormationTransaction', type: 'class' },
    { dir: 'Transactions', cppName: 'GovernmentTransaction', type: 'class' },
    { dir: 'Transactions', cppName: 'LoginTransaction', type: 'class' },
    { dir: 'Transactions', cppName: 'MissionTransaction', type: 'class' },
    { dir: 'Transactions', cppName: 'PartnerTransaction', type: 'class' },
    { dir: 'Transactions', cppName: 'PersonTransaction', type: 'class' },
    { dir: 'Transactions', cppName: 'RecruitTransaction', type: 'class' },
    { dir: 'Transactions', cppName: 'ShipTransaction', type: 'class' },
    { dir: 'Transactions', cppName: 'ShopTransaction', type: 'class' },

    // Views
    { dir: 'Views', cppName: 'AlertView', type: 'class' },
    { dir: 'Views', cppName: 'AppointmentView', type: 'class' },
    { dir: 'Views', cppName: 'BuyDockView', type: 'class' },
    { dir: 'Views', cppName: 'CargoView', type: 'class' },
    { dir: 'Views', cppName: 'CopiesRewardView', type: 'class' },
    { dir: 'Views', cppName: 'DialogView', type: 'class' },
    { dir: 'Views', cppName: 'EntryView', type: 'class' },
    { dir: 'Views', cppName: 'FightFailView', type: 'class' },
    { dir: 'Views', cppName: 'HeadView', type: 'class' },
    { dir: 'Views', cppName: 'ItemTipsView', type: 'class' },
    { dir: 'Views', cppName: 'LoadingView', type: 'class' },
    { dir: 'Views', cppName: 'MapTipsView', type: 'class' },
    { dir: 'Views', cppName: 'MissionView', type: 'class' },
    { dir: 'Views', cppName: 'NPCView', type: 'class' },
    { dir: 'Views', cppName: 'Newguidance', type: 'class' },
    { dir: 'Views', cppName: 'NoticeView', type: 'class' },
    { dir: 'Views', cppName: 'PopupView', type: 'class' },
    { dir: 'Views', cppName: 'RecruitView', type: 'class' },
    { dir: 'Views', cppName: 'RegisterView', type: 'class' },
    { dir: 'Views', cppName: 'ShipRoomUpgradeView', type: 'class' },
    { dir: 'Views', cppName: 'TrainingView', type: 'class' },
    { dir: 'Views', cppName: 'WarehouseView', type: 'class' },
    { dir: 'Views', cppName: 'WeaponView', type: 'class' },
];

function ensureDirectoryExistence(filePath) {
    const dirname = path.dirname(filePath);
    if (fs.existsSync(dirname)) {
        return true;
    }
    ensureDirectoryExistence(dirname);
    fs.mkdirSync(dirname);
}

filesToCreate.forEach(fileInfo => {
    const targetDir = fileInfo.dir ? path.join(workspaceRoot, baseTsPath, fileInfo.dir) : path.join(workspaceRoot, baseTsPath);
    const tsFilePath = path.join(targetDir, `${fileInfo.cppName}.ts`);
    
    ensureDirectoryExistence(tsFilePath);

    let content = `// Placeholder for ${fileInfo.cppName}\nexport class ${fileInfo.cppName} {\n    // TODO: Implement logic from C++ sources for ${fileInfo.cppName}\n}\n`;
    
    fs.writeFileSync(tsFilePath, content);
    console.log(`Created ${tsFilePath}`);
});

console.log('\nPlaceholder TypeScript files created in assets/Scripts.');
console.log('This is a first step in migrating your C++ code.');
console.log('Next steps:');
console.log('1. Review the generated placeholder files in assets/Scripts.');
console.log('2. Manually translate the C++ logic from the original Classes folder into these TypeScript files.');
console.log('3. Adapt to Cocos Creator APIs, e.g., by making scene/view classes extend cc.Component and using @ccclass decorators.');
console.log('4. Update imports between your new TypeScript files.');
console.log('5. Once you are satisfied with the manual migration into these placeholders, the old "Classes" folder can be removed.');