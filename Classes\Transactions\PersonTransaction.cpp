#include "PersonTransaction.h"
#include "../Services/HTTPService.h"


USING_NS_CC;
USING_STD;

static const char* TRANS_GAME_CULTIVATE_EXECUTE = "Game_Cultivate_Execute.ashx";
static const char* TRANS_GAME_CULTIVATE_SAVE = "Game_Cultivate_Save.ashx";
static const char* TRANS_GAME_PASSIVE_SKILLS_REFRESH = "Game_PassiveSkills_Refresh.ashx";
static const char* TRANS_GAME_EQUIP_UPDATE = "Game_Equip_Update.ashx";
static const char* TRANS_GAME_CHANGEFACE = "Game_ChangeFaceImg.ashx";

FUNC_GET_INSTANCE(PersonTransaction);

PersonTransaction::PersonTransaction()
{
	Director::getInstance()->getEventDispatcher()->addCustomEventListener(TRANS_GAME_CULTIVATE_EXECUTE, [=](EventCustom* event) {
		auto data = static_cast<HTTPData *>(event->getUserData());
		std::vector<int> i;
		i.push_back(data->readInt("StrengthValue"));
		i.push_back(data->readInt("BrainsValue"));
		Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_CULTIVATE_EXECUTE_SUCCESS, &i);
	});
	Director::getInstance()->getEventDispatcher()->addCustomEventListener(TRANS_GAME_CULTIVATE_SAVE, [=](EventCustom* event) {
		Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_CULTIVATE_SAVE_SUCCESS, NULL);
	});

	Director::getInstance()->getEventDispatcher()->addCustomEventListener(TRANS_GAME_PASSIVE_SKILLS_REFRESH, [=](EventCustom* event) {
		auto data = static_cast<HTTPData*>(event->getUserData());
		int passiveSkillsID = data->readInt("PassiveSkillsID");
		Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_PASSIVESKILL_REFRESH,&passiveSkillsID);
	});
}

PersonTransaction::~PersonTransaction()
{

	Director::getInstance()->getEventDispatcher()->removeCustomEventListeners(TRANS_GAME_CULTIVATE_EXECUTE);
	Director::getInstance()->getEventDispatcher()->removeCustomEventListeners(TRANS_GAME_CULTIVATE_SAVE);
}

void PersonTransaction::executeCultivate(string uid, string onlyID, int cultivateType)
{
	auto data = HTTPData::create();
	data->write("UID", uid.c_str());
	data->write("OnlyID", onlyID.c_str());
	data->write("CultivateType", cultivateType);

	auto url = WEB_HOST + TRANS_GAME_CULTIVATE_EXECUTE;
	HTTPService::getInstance()->request(url.c_str(), data, TRANS_GAME_CULTIVATE_EXECUTE);
}

void PersonTransaction::saveCultivate(string uid, string onlyID, int submitType)
{
	auto data = HTTPData::create();
	data->write("UID", uid.c_str());
	data->write("OnlyID", onlyID.c_str());
	data->write("SubmitType", submitType);

	auto url = WEB_HOST + TRANS_GAME_CULTIVATE_SAVE;
	HTTPService::getInstance()->request(url.c_str(), data,TRANS_GAME_CULTIVATE_SAVE);
}

void PersonTransaction::refreshPassiveSkill(string uid, string onlyID, int type)
{
	auto data = HTTPData::create();
	data->write("UID", uid.c_str());
	data->write("OnlyID", onlyID.c_str());
	data->write("Type", type);

	auto url = WEB_HOST + TRANS_GAME_PASSIVE_SKILLS_REFRESH;
	HTTPService::getInstance()->request(url.c_str(), data, TRANS_GAME_PASSIVE_SKILLS_REFRESH);
}

void PersonTransaction::updateEquips(string uid, PlayerData* player)
{
	auto data = HTTPData::create();
	data->write("UID", uid.c_str());
	data->write("OnlyID", (player->getInfo().id).c_str());

	vector<HTTPData *> equipList;
	for (int i = 0; i < 6; i++)
	{
		auto equipData = HTTPData::create();
		equipData->write("EquipID", "");
		equipList.push_back(equipData);
	}
	for (auto equip : player->getEquips())
	{
		auto index = equip.second->getInfo().equip;
		auto equipData = equipList[index - 1];
		equipData->update("EquipID", (equip.second->getInfo().id).c_str());
	}
	data->write("EquipList", equipList);

	auto url = WEB_HOST + TRANS_GAME_EQUIP_UPDATE;
	HTTPService::getInstance()->request(url.c_str(), data, TRANS_GAME_EQUIP_UPDATE);
}

void PersonTransaction::updateFace(string uid, string faceImg)
{
	auto data = HTTPData::create();
	data->write("UID", uid.c_str());
	data->write("FaceImg", faceImg.c_str());

	auto url = WEB_HOST + TRANS_GAME_CHANGEFACE;
	HTTPService::getInstance()->request(url.c_str(), data, TRANS_GAME_CHANGEFACE);
}
