#include "LocalizeService.h"
#include "../SOSConfig.h"

USING_NS_CC;

FUNC_GET_INSTANCE(LocalizeService);

void LocalizeService::loadStrings()
{
	auto currentLanguageType = Application::getInstance()->getCurrentLanguage();
	std::string filename = "";

	switch (currentLanguageType)
	{
	case LanguageType::ENGLISH:
		filename = "S_GameStringSet_CN";
		break;
	case LanguageType::CHINESE:
		filename = "S_GameStringSet_CN";
		break;
	default:
		CCASSERT(false, "Invalid language type.");
		break;
	}

	if (filename != "")
	{
		Configuration::getInstance()->loadConfigFile("Configs/" + filename + ".plist");
		_strings = Configuration::getInstance()->getValue(filename).asValueMap();
	}
}