#ifndef __SOS_RESOURCE_SERVICE__
#define __SOS_RESOURCE_SERVICE__

#include "cocos2d.h"
#include "../SOSConfig.h"

class ResourceService : public cocos2d::Ref
{
public:
	FUNC_INSTANCE(ResourceService);

public:
	//std::vector<cocos2d::ValueMap> getCSVConfigFromZip(const char* zipFileName, const char* csvFileName);
	std::vector<cocos2d::ValueMap> getCSVConfig(const char* csvFileName);
};

#endif //__SOS_RESOURCE_SERVICE__