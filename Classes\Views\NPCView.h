#ifndef __NPC_VIEW_H__
#define __NPC_VIEW_H__

#include "cocos2d.h"
#include "ui/CocosGUI.h"
#include "cocostudio/CocoStudio.h"
#include "../SOSConfig.h"
#include "../GameData.h"
#include "PopupView.h"

static const std::string EVENT_NPCVIEW_CHANGED = "event_npcview_changed";

enum NPCType : int
{
	GOVERNMENT = 1,
	SHOP,
	PUB,
	WEAPON,
	DOCK,
	WHARF,
};

class NPCView : public PopupView
{
public:
	CREATE_FUNC(NPCView);
	static cocos2d::Layer* createScene();
	static cocos2d::Layer* createScene(int type);

public:
	NPCView();
	virtual ~NPCView();
	virtual bool init() override;
	virtual void onEnter() override;

	void menuCloseCallback(cocos2d::Ref* pSender);
	void touchButton(cocos2d::Ref* object, cocos2d::ui::TouchEventType type);

	virtual bool onTouchBegan(cocos2d::Touch* touch, cocos2d::Event* event) override;
	virtual void onTouchMoved(cocos2d::Touch* touch, cocos2d::Event* event) override;
	virtual void onTouchEnded(cocos2d::Touch* touch, cocos2d::Event* event) override;

	void loadMissionList(int state,int type);
	void taskDialog(std::vector<std::string> list);

private:
	void initTargetCity();
	cocos2d::Node* createOption(std::string content, std::string state, cocos2d::Color3B color, int tag, cocos2d::Ref* data);

private:
	cocos2d::Node* _ui;
	cocostudio::ActionObject* _action;
};


#endif