#ifndef __SOS_LOCALIZE_SERVICE__
#define __SOS_LOCALIZE_SERVICE__

#include "cocos2d.h"
#include "../SOSConfig.h"

class LocalizeService : public cocos2d::Ref
{
public:
	FUNC_INSTANCE(LocalizeService);

public:
	void loadStrings();

	inline std::string getString(const std::string& key) { return (key == "" || key == "0") ? "" : _strings[key].asString(); }

private:
	cocos2d::ValueMap _strings;
};

#endif //__SOS_LOCALIZE_SERVICE__