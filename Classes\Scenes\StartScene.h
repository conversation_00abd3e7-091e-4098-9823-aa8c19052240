#ifndef __SOS_START_SCENE__
#define __SOS_START_SCENE__

#include "cocos2d.h"
#include "BaseScene.h"
#include "cocostudio/CocoStudio.h"
#include "ui/CocosGUI.h"

class StartScene : public BaseScene
{
public:
	CREATE_FUNC(StartScene);
	static cocos2d::Scene* createScene();

public:
	StartScene(){};
	~StartScene();

	virtual bool init() override;
	virtual void onEnter() override;

	void onAnimationEvent(cocostudio::Armature *armature, cocostudio::MovementEventType movementType, const std::string& movementID);
	void onFadeInCallback();
	void onFadeOutCallback();

private:
	cocos2d::Node* _ui;
};

#endif