#include "NoticeView.h"

USING_NS_CC;
USING_NS_CC_CCS;
USING_NS_CC_UI;

enum TagNoticeView
{
	UI_BUTTON_CLOSE = 1180,
};

bool NoticeView::init()
{
	if (!PopupView::init())
	{
		return false;
	}

	auto layer = CCS_CREATE_LAYER("UI_Notice_1");
	this->addChild(layer);

	CCS_GET_CHILD(layer, Button, closeBtn, UI_BUTTON_CLOSE);
	closeBtn->setPressedActionEnabled(true);
	closeBtn->addTouchEventListener(this, toucheventselector(NoticeView::touchButton));

	return true;
}

void NoticeView::touchButton(Ref* obj, cocos2d::ui::TouchEventType eventType)
{
	auto widget = dynamic_cast<Widget*>(obj);
	int tag = widget->getTag();

	switch (eventType)
	{
	case TouchEventType::TOUCH_EVENT_BEGAN:
		break;
	case TouchEventType::TOUCH_EVENT_ENDED:
		if (tag == UI_BUTTON_CLOSE)
		{
			this->removeFromParent();
		}
		break;
	}
}