﻿#include "ShipScene.h"
#include "CityScene.h"
#include "../Views/AppointmentView.h"
#include "../Views/TrainingView.h"
#include "../Views/ShipRoomUpgradeView.h"
#include "../Views/WarehouseView.h"
#include "../Services/NewguidanceService.h"
#include "../Services/LocalizeService.h"
#include "../Views/AlertView.h"

USING_NS_CC;
USING_NS_CC_CCS;
USING_NS_CC_UI;

enum tagShip
{
	UI_SHIP_1 = 10005,
	UI_CLOSE = 11000,

	UI1_TEXTBMFONT_SHIPNAME = 937,
	UI1_TEXTBMFONT_TEXT1 = 938,
	UI1_TEXTBMFONT_TEXT2 = 939,
	UI1_BUTTON_1 = 940,		//主炮
	UI1_BUTTON_2 = 941,		//货仓
	UI1_BUTTON_3 = 935,		//训练	
	UI1_BUTTON_4 = 934,		//任命
	UI1_BUTTON_5 = 1420,	//建造
	UI1_BUTTON_CLOSE = 298,

	UI2_TEXT_WEIGHT = 947,
	UI2_TEXT_COIN = 950,
	UI2_BUTTON_CLOSE = 951,
	UI2_SCROLLVIEW = 961,
	UI2_OTHER_IMAGEVIEW = 815,
	UI2_OTHER_TEXT_PRICE = 814,

};

ShipScene::ShipScene()
{

}

ShipScene::~ShipScene()
{
	_warehouse->release();
	this->getEventDispatcher()->removeCustomEventListeners(EVENT_CABIN_UPGRADED);
}

Scene* ShipScene::createScene()
{
	auto scene = Scene::create();
	auto layer = ShipScene::create();
	scene->addChild(layer);
	return scene;
}

bool ShipScene::init()
{
	if (!BaseScene::init())
	{
		return false;
	}
	
	node = CCS_CREATE_SCENE("Scene_Ship");
	node->setTag(TagBaseScene::LAYER);
	addChild(node);

	CCS_GET_COMPONENT_FROM_SCENE(node, Layer, layer, UI_SHIP_1);
	CCS_GET_CHILD(layer, Button, btn1, UI1_BUTTON_1);
	btn1->addTouchEventListener(this, toucheventselector(ShipScene::touchButton));
	CCS_GET_CHILD(layer, Button, btn2, UI1_BUTTON_2);
	btn2->addTouchEventListener(this, toucheventselector(ShipScene::touchButton));
	CCS_GET_CHILD(layer, Button, btn3, UI1_BUTTON_3);
	btn3->addTouchEventListener(this, toucheventselector(ShipScene::touchButton));
	CCS_GET_CHILD(layer, Button, btn4, UI1_BUTTON_4);
	btn4->addTouchEventListener(this, toucheventselector(ShipScene::touchButton));
	CCS_GET_CHILD(layer, Button, btn5, UI1_BUTTON_5);
	btn5->addTouchEventListener(this, toucheventselector(ShipScene::touchButton));

	CCS_GET_COMPONENT_FROM_SCENE(node, Layer, closeLayer, UI_CLOSE);
	CCS_GET_CHILD(closeLayer, Button, closeBtn, UI1_BUTTON_CLOSE);
	closeBtn->addTouchEventListener(this, toucheventselector(ShipScene::touchButton));

	_warehouse = CCS_CREATE_LAYER("UI_ship_2");
	_warehouse->retain();
	CCS_GET_CHILD(_warehouse, Button, closeBtn2, UI2_BUTTON_CLOSE);
	closeBtn2->addTouchEventListener(this, toucheventselector(ShipScene::touchButton));

	auto userData = GameData::getInstance()->getUserData();
	CCS_GET_CHILD(layer, TextBMFont, name, UI1_TEXTBMFONT_SHIPNAME);
	CCS_GET_CHILD(layer, TextBMFont, weight, UI1_TEXTBMFONT_TEXT1);
	CCS_GET_CHILD(layer, TextBMFont, count, UI1_TEXTBMFONT_TEXT2);

	int w = 0;
	for (auto ship : userData->getShips())
	{
		if (ship.second->getInfo().use)
		{
			name->setText(ship.second->getInfo().name.c_str());
			for (auto cabin : ship.second->getCabins())
			{
				if (cabin->getInfo().type == CabinData::CabinType::REST_HOUSE)
					count->setText(STRING(cabin->getInfo().subjoin1).c_str());
				if (cabin->getInfo().type == CabinData::CabinType::WARE_HOUSE)
					w += cabin->getInfo().subjoin1;
			}
			break;
		}
	}
	weight->setText(STRING(w).c_str());

	Texture2D::setDefaultAlphaPixelFormat(Texture2D::PixelFormat::RGB888);
	auto imgBg = Sprite::create("ss_ty_bg1.png");
	auto size = Director::getInstance()->getWinSize();
	imgBg->setScale(size.width / 960, size.height / 640);
	imgBg->setAnchorPoint(Point(0, 0));
	this->addChild(imgBg, -1);
	Texture2D::setDefaultAlphaPixelFormat(Texture2D::PixelFormat::DEFAULT);

	return true;
}

void ShipScene::menuCloseCallback(Ref* pSender)
{
	Director::getInstance()->end();

#if (CC_TARGET_PLATFORM == CC_PLATFORM_IOS)
	exit(0);
#endif
}

void ShipScene::touchButton(Ref* obj, cocos2d::ui::TouchEventType eventType)
{
	auto widget = dynamic_cast<Widget*>(obj);
	int tag = widget->getTag();
	switch (eventType)
	{
	case TouchEventType::TOUCH_EVENT_BEGAN:
		break;
	case TouchEventType::TOUCH_EVENT_ENDED:
		if (tag == UI1_BUTTON_1)
		{
			this->addChild(AlertView::create(LocalizeService::getInstance()->getString("4001"), AlertType::IDLE), 999999);
		}
		else if (tag == UI1_BUTTON_2)
		{
			this->addChild(WarehouseView::createScene(), this->getChildrenCount() + 1);
		}
		else if (tag == UI1_BUTTON_3)
		{
			this->addChild(AlertView::create(LocalizeService::getInstance()->getString("4001"), AlertType::IDLE), 999999);
			//this->addChild(TrainingView::createScene(), this->getChildrenCount() + 1);
		}
		else if (tag == UI1_BUTTON_4)
		{
			this->addChild(AppointmentView::createScene(), this->getChildrenCount() + 1, TagBaseScene::SHIP_APPOINT_VIEW);
		}
		else if (tag == UI1_BUTTON_5)
		{
			this->addChild(ShipRoomUpgradeView::createScene(), this->getChildrenCount() + 1, TagBaseScene::SHIP_UPGRADE_VIEW);
		}
		else if (tag == UI1_BUTTON_CLOSE)
		{
			Director::getInstance()->replaceScene(CityScene::createScene());
		}
		else if (tag == UI2_BUTTON_CLOSE)
		{
			_warehouse->removeFromParent();
		}

		break;
	}
}

void ShipScene::onEnter()
{
	this->getEventDispatcher()->addCustomEventListener(EVENT_CABIN_UPGRADED, [this](EventCustom* event){
		auto userData = GameData::getInstance()->getUserData();
		CCS_GET_COMPONENT_FROM_SCENE(node, Layer, layer, UI_SHIP_1);
		CCS_GET_CHILD(layer, TextBMFont, name, UI1_TEXTBMFONT_SHIPNAME);
		CCS_GET_CHILD(layer, TextBMFont, weight, UI1_TEXTBMFONT_TEXT1);
		CCS_GET_CHILD(layer, TextBMFont, count, UI1_TEXTBMFONT_TEXT2);
		int w = 0;
		for (auto ship : userData->getShips())
		{
			if (ship.second->getInfo().use)
			{
				name->setText(ship.second->getInfo().name.c_str());
				for (auto cabin : ship.second->getCabins())
				{
					if (cabin->getInfo().type == CabinData::CabinType::REST_HOUSE)
						count->setText(STRING(cabin->getInfo().subjoin1).c_str());
					if (cabin->getInfo().type == CabinData::CabinType::WARE_HOUSE)
						w += cabin->getInfo().subjoin1;
				}
				break;
			}
		}
		weight->setText(STRING(w).c_str());
	});

// 	if (GameData::getInstance()->getUserData()->getInfo().state == UserData::StateType::GUIDER)
// 	{
// 		NewguidanceService::getInstance()->createLayer(this);
// 	}

	BaseScene::onEnter();
}