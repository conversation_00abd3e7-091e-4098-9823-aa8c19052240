#ifndef __SOS_FIGHT_DATA__
#define __SOS_FIGHT_DATA__

#include "cocos2d.h"
#include "../SOSConfig.h"
#include "TeamData.h"

class FightData : public cocos2d::Ref
{
public:
	FUNC_CREATE(FightData);

public:
	enum SkillType : int
	{
		DEFAULT,
		CRIT,
		LUCKY,
		WRECK,
		NO_TYPE_4,
		NO_TYPE_5,
	};

	struct Info
	{
		std::string id;
		int scene;
		bool upgrade = false;

		bool isWin;
	};

	struct Round
	{
		struct Action
		{
			enum DefenseType
			{
				HITTED,
				EVADE,
				PARRY,
			};

			struct Target
			{
				std::string pid;
				int life;
				int energe;
				int strike;
				int defense;    //DefenseType
				bool dead;
			};

			std::string pid;
			int skill;			//SkillType && SkillId
			int energe;
			bool dead;
			std::vector<Target> targets;
		};

		std::vector<Action> actions;
	};

	typedef std::map<int, TeamData *> TeamMap;
	typedef std::vector<Round> RoundVector;
	typedef std::map<int, int> SettleMap;

public:
	inline Info& getInfo() { return _info; }
	inline void setInfo(Info& info) { _info = info; }

	inline TeamData* getTeam(int face) { return _teams[face]; }
	inline void setTeam(TeamData* team, int face) { CC_SAFE_RELEASE_NULL(_teams[face]); _teams[face] = team; CC_SAFE_RETAIN(_teams[face]); }

	inline RoundVector& getRounds() { return _rounds; }
	inline Round& getRound(int num) { return _rounds[num]; }

	inline SettleMap& getSettles() { return _settles; }
	inline void setSettles(SettleMap& settles) { _settles = settles; }

public:
	Info _info;
	TeamMap _teams;
	RoundVector _rounds;
	SettleMap _settles;
};

#endif