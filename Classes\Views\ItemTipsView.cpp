#include "ItemTipsView.h"
#include "cocostudio/CocoStudio.h"
#include "../SOSConfig.h"
#include "../Services/LocalizeService.h"
#include "../Services/NewguidanceService.h"
#include "../GameData.h"
#include "../Scenes/BaseScene.h"

USING_NS_CC;
USING_NS_CC_CCS;
USING_NS_CC_UI;

enum TagItemTipsView
{
	UI_IMAGE_ICON = 20060,
	UI_TEXT_NAME = 20061,
	UI_TEXT_LIMIT = 20111,
	UI_TEXT_DESCRIPTION = 20112,
	UI_TEXT_SELL = 20114,
	UI_BUTTON_SELL = 20115,
	UI_BUTTON_USE = 20116,
	UI_IMAGE_BG = 20064,
	UI_BUTTON_COMPARE = 20065,
	UI_TEXT_EFFECT_NAME_1 = 20102,
	UI_TEXT_EFFECT_NAME_2 = 20103,
	UI_TEXT_EFFECT_NAME_3 = 20104,
	UI_TEXT_EFFECT_VALUE_1 = 20105,
	UI_TEXT_EFFECT_VALUE_2 = 20106,
	UI_TEXT_EFFECT_VALUE_3 = 20107,
	UI_TEXT_ATTRI_HP = 20078,
	UI_TEXT_ATTRI_STR = 20079,
	UI_TEXT_ATTRI_INC = 20080,
	UI_TEXT_ATTRI_ATK = 20081,
	UI_TEXT_ATTRI_DEF = 20082,
	UI_TEXT_ATTRI_MAG = 20083,
	UI_TEXT_ATTRI_RES = 20084,
	UI_TEXT_ATTRI_ATT = 20085,
	UI_TEXT_ATTRI_CRT = 20086,
	UI_TEXT_ATTRI_LUK = 20087,
	UI_TEXT_ATTRI_EVA = 20088,
	UI_TEXT_ATTRI_BLK = 20089,
	UI_TEXT_ATTRI_HP_NEW = 20090,
	UI_TEXT_ATTRI_STR_NEW = 20091,
	UI_TEXT_ATTRI_INC_NEW = 20092,
	UI_TEXT_ATTRI_ATK_NEW = 20093,
	UI_TEXT_ATTRI_DEF_NEW = 20094,
	UI_TEXT_ATTRI_MAG_NEW = 20095,
	UI_TEXT_ATTRI_RES_NEW = 20096,
	UI_TEXT_ATTRI_ATT_NEW = 20097,
	UI_TEXT_ATTRI_CRT_NEW = 20098,
	UI_TEXT_ATTRI_LUK_NEW = 20099,
	UI_TEXT_ATTRI_EVA_NEW = 20100,
	UI_TEXT_ATTRI_BLK_NEW = 20101,

	UI_IMG = 40001,
};

ItemData* r_item = nullptr;
PlayerData* r_player = nullptr;
PlayerData::Attribute r_attribute;
PlayerData::Attribute r_attributeNew;
bool r_open = false;

ItemTipsView* ItemTipsView::create(ItemData* item, PlayerData* player)
{
	r_item = item;
	r_player = player;
	return ItemTipsView::create();
}

ItemTipsView::~ItemTipsView()
{
	//ActionManagerEx::getInstance()->
	ActionManagerEx::getInstance()->releaseActionByName("UI_Tips_2.json");
	//ActionManagerEx::getInstance()->releaseActions();
	r_open = false;
}

bool ItemTipsView::init()
{
	if (!PopupView::init())
	{
		return false;
	}

	auto ui = CCS_CREATE_LAYER("UI_Tips_2");
	ui->setTag(TagBaseScene::PERSON_BAG_EQUIP_VIEW);
	this->setCenter(ui);
	this->addChild(ui);

	if (r_item)
	{
		CCS_GET_CHILD(ui, ImageView, iconImage, UI_IMAGE_ICON);
		CCS_GET_CHILD(ui, Text, nameText, UI_TEXT_NAME);
		CCS_GET_CHILD(ui, Text, limitText, UI_TEXT_LIMIT);
		CCS_GET_CHILD(ui, Text, descripText, UI_TEXT_DESCRIPTION);
		CCS_GET_CHILD(ui, TextBMFont, sellText, UI_TEXT_SELL);
		CCS_GET_CHILD(ui, Button, useButton, UI_BUTTON_USE);
		CCS_GET_CHILD(ui, Button, sellButton, UI_BUTTON_SELL);
		CCS_GET_CHILD(ui, Button, compareButton, UI_BUTTON_COMPARE);
		CCS_GET_CHILD(ui, ImageView, bgImage, UI_IMAGE_BG);
		CCS_GET_CHILD(ui, ImageView, img, UI_IMG);
		useButton->setPressedActionEnabled(true);
		sellButton->setPressedActionEnabled(true);
		compareButton->setPressedActionEnabled(true);

		if (r_player == NULL)
		{
			useButton->setVisible(false);
			useButton->setTouchEnabled(false);
			sellButton->setVisible(false);
			sellButton->setTouchEnabled(false);
			compareButton->setVisible(false);
			compareButton->setTouchEnabled(false);
			bgImage->setVisible(false);
		}
		if (r_item->getInfo().type == ItemData::ItemType::EQUIP && r_item->getInfo().quality > 0)
		{
			img->loadTexture(GET_PROP_ICON_MASK(r_item->getInfo().icon), TextureResType::PLIST);
			img->setColor(GetEquipQualityColor(r_item->getInfo().quality));
		}
		else
		{
			img->setVisible(false);
		}
		iconImage->loadTexture(GET_PROP_ICON(r_item->getInfo().icon), TextureResType::PLIST);
		iconImage->setVisible(true);
		nameText->setText(LocalizeService::getInstance()->getString(r_item->getInfo().name));
		limitText->setText(STRING(r_item->getInfo().require));
		if (r_player != nullptr)
		{
			limitText->setColor(r_player->getInfo().level >= r_item->getInfo().require ? Color3B(28, 92, 0) : Color3B(225, 36, 0));
		}
		descripText->setText(LocalizeService::getInstance()->getString(r_item->getInfo().descrip));
		sellText->setText(STRING(r_item->getInfo().sell).c_str());

		

		CCS_GET_CHILD(ui, TextBMFont, hpText, UI_TEXT_ATTRI_HP);
		CCS_GET_CHILD(ui, TextBMFont, strText, UI_TEXT_ATTRI_STR);
		CCS_GET_CHILD(ui, TextBMFont, incText, UI_TEXT_ATTRI_INC);
		CCS_GET_CHILD(ui, TextBMFont, atkText, UI_TEXT_ATTRI_ATK);
		CCS_GET_CHILD(ui, TextBMFont, defText, UI_TEXT_ATTRI_DEF);
		CCS_GET_CHILD(ui, TextBMFont, magText, UI_TEXT_ATTRI_MAG);
		CCS_GET_CHILD(ui, TextBMFont, resText, UI_TEXT_ATTRI_RES);
		CCS_GET_CHILD(ui, TextBMFont, attText, UI_TEXT_ATTRI_ATT);
		CCS_GET_CHILD(ui, TextBMFont, crtText, UI_TEXT_ATTRI_CRT);
		CCS_GET_CHILD(ui, TextBMFont, lukText, UI_TEXT_ATTRI_LUK);
		CCS_GET_CHILD(ui, TextBMFont, evaText, UI_TEXT_ATTRI_EVA);
		CCS_GET_CHILD(ui, TextBMFont, blkText, UI_TEXT_ATTRI_BLK);

		if (r_player != NULL)
		{
			r_attribute.HP = r_player->getHP();
			r_attribute.STR = r_player->getSTR();
			r_attribute.INC = r_player->getINC();
			r_attribute.ATK = r_player->getATK();
			r_attribute.DEF = r_player->getDEF();
			r_attribute.MAG = r_player->getMAG();
			r_attribute.RES = r_player->getRES();
			r_attribute.ATT = r_player->getATT();
			r_attribute.CRT = r_player->getCRT();
			r_attribute.LUK = r_player->getLUK();
			r_attribute.EVA = r_player->getEVA();
			r_attribute.BLK = r_player->getBLK();

			r_attributeNew = r_attribute;

			auto equips = r_player->getEquips();
			for (auto equip : equips)
			{
				if (equip.second->getInfo().equip == r_item->getInfo().equip)
				{
					for (auto effect : equip.second->getInfo().effects)
					{
						switch (effect.type)
						{
						case ItemData::Info::EffectType::HP:
							r_attributeNew.HP -= effect.value;
							break;
						case ItemData::Info::EffectType::STR:
							r_attributeNew.STR -= effect.value;
							break;
						case ItemData::Info::EffectType::INC:
							r_attributeNew.INC -= effect.value;
							break;
						case ItemData::Info::EffectType::ATK:
							r_attributeNew.ATK -= effect.value;
							break;
						case ItemData::Info::EffectType::DEF:
							r_attributeNew.DEF -= effect.value;
							break;
						case ItemData::Info::EffectType::MAG:
							r_attributeNew.MAG -= effect.value;
							break;
						case ItemData::Info::EffectType::RES:
							r_attributeNew.RES -= effect.value;
							break;
						case ItemData::Info::EffectType::ATT:
							r_attributeNew.ATT -= effect.value;
							break;
						case ItemData::Info::EffectType::CRT:
							r_attributeNew.CRT -= effect.value;
							break;
						case ItemData::Info::EffectType::LUK:
							r_attributeNew.LUK -= effect.value;
							break;
						case ItemData::Info::EffectType::EVA:
							r_attributeNew.EVA -= effect.value;
							break;
						case ItemData::Info::EffectType::BLK:
							r_attributeNew.BLK -= effect.value;
							break;
						default:
							break;
						}
					}
					break;
				}
			}

			hpText->setText(STRING(r_player->getHP()).c_str());
			strText->setText(STRING(r_player->getSTR()).c_str());
			incText->setText(STRING(r_player->getINC()).c_str());
			atkText->setText(STRING(r_player->getATK()).c_str());
			defText->setText(STRING(r_player->getDEF()).c_str());
			magText->setText(STRING(r_player->getMAG()).c_str());
			resText->setText(STRING(r_player->getRES()).c_str());
			attText->setText((STRING(r_player->getATT()) + "%").c_str());
			crtText->setText((STRING(r_player->getCRT()) + "%").c_str());
			lukText->setText((STRING(r_player->getLUK()) + "%").c_str());
			evaText->setText((STRING(r_player->getEVA()) + "%").c_str());
			blkText->setText((STRING(r_player->getBLK()) + "%").c_str()); 


			compareButton->setVisible(true);
			compareButton->setTouchEnabled(true);
			compareButton->addTouchEventListener(this, toucheventselector(ItemTipsView::touchButton));

			if (r_item->getInfo().type == ItemData::ItemType::EQUIP || r_item->getInfo().type == ItemData::ItemType::GIFT)
			{
				useButton->setVisible(true);
				useButton->setTouchEnabled(true);
				useButton->addTouchEventListener(this, toucheventselector(ItemTipsView::touchButton));
			}

			if (r_item->getInfo().sell > 0)
			{
				sellButton->setVisible(true);
				sellButton->setTouchEnabled(true);
				sellButton->addTouchEventListener(this, toucheventselector(ItemTipsView::touchButton));
			}
		}

		auto i = 0;
		for (auto effect : r_item->getInfo().effects)
		{
			CCS_GET_CHILD(ui, TextBMFont, effectType, UI_TEXT_EFFECT_NAME_1 + i);
			//CCS_GET_CHILD(ui, TextBMFont, effectValue, UI_TEXT_EFFECT_VALUE_1 + i);

			if (effect.type <= 0)
			{
				continue;
			}

			effectType->setText((LocalizeService::getInstance()->getString(STRING(3000 + effect.type)) + "+" + STRING(effect.value)).c_str());
			effectType->setVisible(true);
			// 			effectValue->setText(().c_str());
			// 			effectValue->setVisible(true);

			switch (effect.type)
			{
			case ItemData::Info::EffectType::HP:
				r_attributeNew.HP += effect.value;
				break;
			case ItemData::Info::EffectType::STR:
				r_attributeNew.STR += effect.value;
				break;
			case ItemData::Info::EffectType::INC:
				r_attributeNew.INC += effect.value;
				break;
			case ItemData::Info::EffectType::ATK:
				r_attributeNew.ATK += effect.value;
				break;
			case ItemData::Info::EffectType::DEF:
				r_attributeNew.DEF += effect.value;
				break;
			case ItemData::Info::EffectType::MAG:
				r_attributeNew.MAG += effect.value;
				break;
			case ItemData::Info::EffectType::RES:
				r_attributeNew.RES += effect.value;
				break;
			case ItemData::Info::EffectType::ATT:
				r_attributeNew.ATT += effect.value;
				break;
			case ItemData::Info::EffectType::CRT:
				r_attributeNew.CRT += effect.value;
				break;
			case ItemData::Info::EffectType::LUK:
				r_attributeNew.LUK += effect.value;
				break;
			case ItemData::Info::EffectType::EVA:
				r_attributeNew.EVA += effect.value;
				break;
			case ItemData::Info::EffectType::BLK:
				r_attributeNew.BLK += effect.value;
				break;
			default:
				break;
			}
			i++;
		}

		CCS_GET_CHILD(ui, TextBMFont, hpNewText, UI_TEXT_ATTRI_HP_NEW);
		CCS_GET_CHILD(ui, TextBMFont, strNewText, UI_TEXT_ATTRI_STR_NEW);
		CCS_GET_CHILD(ui, TextBMFont, incNewText, UI_TEXT_ATTRI_INC_NEW);
		CCS_GET_CHILD(ui, TextBMFont, atkNewText, UI_TEXT_ATTRI_ATK_NEW);
		CCS_GET_CHILD(ui, TextBMFont, defNewText, UI_TEXT_ATTRI_DEF_NEW);
		CCS_GET_CHILD(ui, TextBMFont, magNewText, UI_TEXT_ATTRI_MAG_NEW);
		CCS_GET_CHILD(ui, TextBMFont, resNewText, UI_TEXT_ATTRI_RES_NEW);
		CCS_GET_CHILD(ui, TextBMFont, attNewText, UI_TEXT_ATTRI_ATT_NEW);
		CCS_GET_CHILD(ui, TextBMFont, crtNewText, UI_TEXT_ATTRI_CRT_NEW);
		CCS_GET_CHILD(ui, TextBMFont, lukNewText, UI_TEXT_ATTRI_LUK_NEW);
		CCS_GET_CHILD(ui, TextBMFont, evaNewText, UI_TEXT_ATTRI_EVA_NEW);
		CCS_GET_CHILD(ui, TextBMFont, blkNewText, UI_TEXT_ATTRI_BLK_NEW);

		showCompare(hpNewText, r_attributeNew.HP - r_attribute.HP);
		showCompare(strNewText, r_attributeNew.STR - r_attribute.STR);
		showCompare(incNewText, r_attributeNew.INC - r_attribute.INC);
		showCompare(atkNewText, r_attributeNew.ATK - r_attribute.ATK);
		showCompare(defNewText, r_attributeNew.DEF - r_attribute.DEF);
		showCompare(magNewText, r_attributeNew.MAG - r_attribute.MAG);
		showCompare(resNewText, r_attributeNew.RES - r_attribute.RES);
		showCompare(attNewText, r_attributeNew.ATT - r_attribute.ATT);
		showCompare(crtNewText, r_attributeNew.CRT - r_attribute.CRT);
		showCompare(lukNewText, r_attributeNew.LUK - r_attribute.LUK);
		showCompare(evaNewText, r_attributeNew.EVA - r_attribute.EVA);
		showCompare(blkNewText, r_attributeNew.BLK - r_attribute.BLK);

		
	
	}

// 	if (GameData::getInstance()->isCreate)
// 		this->addChild(NewguidanceService::getInstance()->createLayer(), this->getChildrenCount() + 3);
	return true;
}

void ItemTipsView::touchButton(Ref* object, TouchEventType type)
{
	auto button = static_cast<Widget*>(object);

	switch (type)
	{
	case TouchEventType::TOUCH_EVENT_ENDED:
		if (button->getTag() == UI_BUTTON_USE)
		{
			this->getEventDispatcher()->dispatchCustomEvent(EVENT_USE_ITEM, r_item);
		}
		else if (button->getTag() == UI_BUTTON_COMPARE)
		{
			ActionManagerEx::getInstance()->playActionByName("UI_Tips_2.json", r_open ? "Animation1" : "Animation0");

			// 			auto ui = this->getChildByTag(99999);
			// 
			// 			CCS_GET_CHILD(ui, TextBMFont, hpNewText, UI_TEXT_ATTRI_HP_NEW);
			// 			CCS_GET_CHILD(ui, TextBMFont, strNewText, UI_TEXT_ATTRI_STR_NEW);
			// 			CCS_GET_CHILD(ui, TextBMFont, incNewText, UI_TEXT_ATTRI_INC_NEW);
			// 			CCS_GET_CHILD(ui, TextBMFont, atkNewText, UI_TEXT_ATTRI_ATK_NEW);
			// 			CCS_GET_CHILD(ui, TextBMFont, defNewText, UI_TEXT_ATTRI_DEF_NEW);
			// 			CCS_GET_CHILD(ui, TextBMFont, magNewText, UI_TEXT_ATTRI_MAG_NEW);
			// 			CCS_GET_CHILD(ui, TextBMFont, resNewText, UI_TEXT_ATTRI_RES_NEW);
			// 			CCS_GET_CHILD(ui, TextBMFont, attNewText, UI_TEXT_ATTRI_ATT_NEW);
			// 			CCS_GET_CHILD(ui, TextBMFont, crtNewText, UI_TEXT_ATTRI_CRT_NEW);
			// 			CCS_GET_CHILD(ui, TextBMFont, lukNewText, UI_TEXT_ATTRI_LUK_NEW);
			// 			CCS_GET_CHILD(ui, TextBMFont, evaNewText, UI_TEXT_ATTRI_EVA_NEW);
			// 			CCS_GET_CHILD(ui, TextBMFont, blkNewText, UI_TEXT_ATTRI_BLK_NEW);
			// 
			// 			showCompare(hpNewText, r_attributeNew.HP - r_attribute.HP);
			// 			showCompare(strNewText, r_attributeNew.STR - r_attribute.STR);
			// 			showCompare(incNewText, r_attributeNew.INC - r_attribute.INC);
			// 			showCompare(atkNewText, r_attributeNew.ATK - r_attribute.ATK);
			// 			showCompare(defNewText, r_attributeNew.DEF - r_attribute.DEF);
			// 			showCompare(magNewText, r_attributeNew.MAG - r_attribute.MAG);
			// 			showCompare(resNewText, r_attributeNew.RES - r_attribute.RES);
			// 			showCompare(attNewText, r_attributeNew.ATT - r_attribute.ATT);
			// 			showCompare(crtNewText, r_attributeNew.CRT - r_attribute.CRT);
			// 			showCompare(lukNewText, r_attributeNew.LUK - r_attribute.LUK);
			// 			showCompare(evaNewText, r_attributeNew.EVA - r_attribute.EVA);
			// 			showCompare(blkNewText, r_attributeNew.BLK - r_attribute.BLK);

			r_open = !r_open;
		}
		else if (button->getTag() == UI_BUTTON_SELL)
		{
			this->removeFromParent();
		}
		break;
	default:
		break;
	}
}

void ItemTipsView::showCompare(TextBMFont* text, int compare)
{
	if (compare != 0)
	{
		text->setText(((compare > 0 ? "+" : "") + STRING(compare)).c_str());
		text->setFntFile(compare > 0 ? "Fonts/Minus_Font.fnt" : "Fonts/Add_Font.fnt");
		//text->setColor(compare > 0 ? Color3B(28, 92, 0) : Color3B(255, 36, 0));
	}
}

void ItemTipsView::showCompare(TextBMFont* text, float compare)
{
	if (compare != 0)
	{
		text->setText(((compare > 0 ? "+" : "") + STRING(compare) + "%").c_str());
		text->setFntFile(compare > 0 ? "Fonts/Minus_Font.fnt" : "Fonts/Add_Font.fnt");
		//text->setColor(compare > 0 ? Color3B(28, 92, 0) : Color3B(255, 36, 0));
	}
}