#ifndef __APPONTMENT_VIEW_H__
#define __APPONTMENT_VIEW_H__

#include "cocos2d.h"
#include "ui/CocosGUI.h"
#include "cocostudio/CocoStudio.h"
#include "../SOSConfig.h"
#include "../GameData.h"
#include "PopupView.h"
#include "../Transactions/ShipTransaction.h"

class AppointmentView : public PopupView
{
public:
	CREATE_FUNC(AppointmentView);

	static cocos2d::Layer* createScene();

public:
	AppointmentView();
	virtual ~AppointmentView();
	virtual bool init() override;
	virtual void onEnter() override;

	void touchButton(cocos2d::Ref* object, cocos2d::ui::TouchEventType type);
	void loadScroolView();
	void checkAppointment(std::string pID,int type);
	void checkSeleteState(int type);
private:
	cocos2d::Node* node;
	cocos2d::ui::CheckBox* _captainBtn;
	cocos2d::ui::CheckBox* _operateBtn;
	cocos2d::ui::CheckBox* _navigationBtn;
};

#endif