﻿#include "MissionView.h"
#include "../Services/LocalizeService.h"
#include "../Views/ItemTipsView.h"
#include "../Scenes/MinimapScene.h"

USING_NS_CC;
USING_NS_CC_CCS;
USING_NS_CC_UI;

std::vector<CheckBox*> mission_CheckList;

enum tagMisson
{
	UI_SCROLLVIEW = 69,
	UI_TEXT_TITLE = 70,
	UI_BUTTON_CLOSE = 71,
	UI_TEXTAREA = 72,
	UI_TEXT_TIPS = 73,
	UI_BUTTON_HINT = 74,
	UI_SCROLLVIEW_REWARD = 75,
	UI_TEXT_EXP = 20032,

	UI_IMAGEVIEW_BG = 91,
	UI_MISSION3_TITLE = 92,
	UI_MISSION3_INFO = 93,
	UI_MISSION3_MARK = 94,

	UI_OTHER8_IMAGEVIEW_REPLACE = 655,
	UI_OTHER8_TEXTBMFONT_NUMBER = 656,
	UI_OTHER8_IMAGEVIEW_QUALITY = 37000,
	UI_OTHER8_ITEM = 898989,
};

MissionView::MissionView()
{

}

MissionView::~MissionView()
{
	mission_CheckList.clear();
}

Layer* MissionView::createScene()
{
	auto layer = MissionView::create();
	return layer;
}

bool MissionView::init()
{
	if (!PopupView::init())
	{
		return false;
	}
	_autoClose = false;

	_myLayout = CCS_CREATE_LAYER("UI_Mission_2");
	addChild(_myLayout);
	auto closeBtn = dynamic_cast<Button*>(_myLayout->getChildByTag(UI_BUTTON_CLOSE));
	auto scrollView = dynamic_cast<ui::ScrollView*>(_myLayout->getChildByTag(UI_SCROLLVIEW));
	closeBtn->addTouchEventListener(this, toucheventselector(MissionView::touchButton));
	CCS_GET_CHILD(_myLayout, Button, hintBtn, UI_BUTTON_HINT);
	//hintBtn->setBright(false);
	//hintBtn->setTouchEnabled(false);
	hintBtn->setPressedActionEnabled(true);
	hintBtn->addTouchEventListener(this, toucheventselector(MissionView::touchButton));

	loadMissionList(2, 1);
	loadMissionList(2, 2);
	loadMissionList(1, 1);
	loadMissionList(1, 2);
	//auto userData = GameData::getInstance()->g etUserData();
	//int height = 0;
	//int i = 0;
	//auto tasks = userData->getTasks();
	//for (auto task : tasks)
	//{
	//	if (task.second->getInfo().state == 1 || task.second->getInfo().state == 2)
	//	{
	//		auto taskInfo = task.second->getInfo();
	//		auto mission3 = cocostudio::CCS_CREATE_LAYER("UI_Mission_3.json");
	//		auto bg = dynamic_cast<CheckBox*>(mission3->getChildByTag(UI_IMAGEVIEW_BG));
	//		auto title = dynamic_cast<Text*>(mission3->getChildByTag(UI_MISSION3_TITLE));
	//		auto info = dynamic_cast<Text*>(mission3->getChildByTag(UI_MISSION3_INFO));
	//		auto mark = dynamic_cast<ImageView*>(mission3->getChildByTag(UI_MISSION3_MARK));
	//		if (taskInfo.state == 1)
	//			mark->loadTexture("ss_task_taskno.png", TextureResType::PLIST);
	//		else
	//			mark->loadTexture("rss_myship_select.png", TextureResType::PLIST);
	//		if (i == 0)
	//		{
	//			bg->setSelectedState(true);
	//			/*title->setColor(Color3B(255, 222, 0));
	//			info->setColor(Color3B(255, 222, 0));*/
	//		}
	//		else
	//		{
	//			/*title->setColor(Color3B(79, 54, 14));
	//			info->setColor(Color3B(79, 54, 14));*/
	//		}
	//		title->setText(LocalizeService::getInstance()->getString(TASK_TYPE[taskInfo.type].c_str()));
	//		info->setText(LocalizeService::getInstance()->getString(taskInfo.name));
	//		bg->setTag(task.second->getInfo().mid + 100000);
	//		bg->setUserObject(task.second);
	//		bg->addTouchEventListener(this, toucheventselector(MissionView::touchButton));
	//		height = mission3->getContentSize().height;
	//		i++;
	//		mission_CheckList.push_back(bg);
	//		scrollView->addChild(mission3);
	//	}
	//}
	//scrollView->setInnerContainerSize(Size(scrollView->getContentSize().width, scrollView->getChildrenCount()*height));

	for (auto item : mission_CheckList)
	{
		if (item->getSelectedState())
		{
			auto taskData = dynamic_cast<TaskData*>(item->getUserObject());
			auto configs = Configuration::getInstance()->getValue(TASK_CONFIG).asValueMap();
			auto config1 = configs[STRING(taskData->getInfo().mid)].asValueMap();

			if (taskData->getInfo().state == TaskData::StateType::ACCEPTED)
			{
				hintBtn->setVisible(bool(taskData->getInfo().targetCityID != 0));
				hintBtn->setTouchEnabled(bool(taskData->getInfo().targetCityID != 0));
			}
			else if (taskData->getInfo().state == TaskData::StateType::FINISHED)
			{
				hintBtn->setVisible(bool(taskData->getInfo().deliveryCityID != 0));
				hintBtn->setTouchEnabled(bool(taskData->getInfo().deliveryCityID != 0));
			}

			CCS_GET_CHILD(_myLayout, Text, titleTxt, UI_TEXT_TITLE);
			titleTxt->setText(LocalizeService::getInstance()->getString(taskData->getInfo().name));
			CCS_GET_CHILD(_myLayout, Text, descriptionTxt, UI_TEXTAREA);
			//descriptionTxt->setText(LocalizeService::getInstance()->getString(config1["Description"].asString()));
			descriptionTxt->setText(LocalizeService::getInstance()->getString(taskData->getInfo().description));
			//initTips(taskData->getFactor());
			CCS_GET_CHILD(_myLayout, Text, tipsTxt, UI_TEXT_TIPS);
			tipsTxt->setText(LocalizeService::getInstance()->getString(STRING(taskData->getInfo().target)));
			//loadReward(config1["Reward"].asString());
			loadReward(taskData->getInfo().reward);
			break;
		}
	}

	return true;
}

void MissionView::menuCloseCallback(Ref* pSender)
{
	Director::getInstance()->end();

#if (CC_TARGET_PLATFORM == CC_PLATFORM_IOS)
	exit(0);
#endif
}

void MissionView::touchButton(Ref* obj, cocos2d::ui::TouchEventType eventType)
{
	auto widget = dynamic_cast<Widget*>(obj);
	int tag = widget->getTag();
	switch (eventType)
	{
	case TouchEventType::TOUCH_EVENT_BEGAN:
		break;
	case TouchEventType::TOUCH_EVENT_ENDED:
		if (tag == UI_BUTTON_CLOSE)
		{
			this->removeFromParent();
		}
		else if (tag == UI_BUTTON_HINT)
		{

			for (auto check : mission_CheckList)
			{
				if (check->getSelectedState())
				{
					auto taskData = dynamic_cast<TaskData*>(check->getUserObject());
					this->addChild(MinimapScene::createScene(taskData), this->getChildrenCount() + 1);
					break;
				}
			}
		}
		else if (tag > 100000)
		{
			for (auto check : mission_CheckList)
			{
				check->setSelectedState(false);
			}

			auto taskData = dynamic_cast<TaskData*>(widget->getUserObject());
			CCS_GET_CHILD(_myLayout, Button, hintBtn, UI_BUTTON_HINT);
			if (taskData->getInfo().state == TaskData::StateType::ACCEPTED)
			{
				hintBtn->setVisible(bool(taskData->getInfo().targetCityID != 0));
				hintBtn->setTouchEnabled(bool(taskData->getInfo().targetCityID != 0));
			}
			else if (taskData->getInfo().state == TaskData::StateType::FINISHED)
			{
				hintBtn->setVisible(bool(taskData->getInfo().deliveryCityID != 0));
				hintBtn->setTouchEnabled(bool(taskData->getInfo().deliveryCityID != 0));
			}
			CCS_GET_CHILD(_myLayout, Text, titleTxt, UI_TEXT_TITLE);
			titleTxt->setText(LocalizeService::getInstance()->getString(taskData->getInfo().name));
			CCS_GET_CHILD(_myLayout, Text, descriptionTxt, UI_TEXTAREA);
			descriptionTxt->setText(LocalizeService::getInstance()->getString(taskData->getInfo().description));
			CCS_GET_CHILD(_myLayout, Text, tipsTxt, UI_TEXT_TIPS);
			tipsTxt->setText(LocalizeService::getInstance()->getString(STRING(taskData->getInfo().target)));
			//initTips(taskData->getFactor());
			loadReward(taskData->getInfo().reward);

		}
		else
		{
			auto equip = dynamic_cast<ItemData*>(widget->getUserObject());
			this->addChild(ItemTipsView::create(equip, NULL), 999999, 99999);
		}
		break;
	}
}

bool MissionView::onTouchBegan(Touch* touch, Event* event)
{
	CCLOG("ccTouchBegan");
	return true;
}

void MissionView::onTouchMoved(Touch* touch, Event* event){
	CCLOG("ccTouchMoved");
}

void MissionView::onTouchEnded(Touch* touch, Event* event)
{
	CCLOG("ccTouchEnded");
}

void MissionView::loadReward(std::string reward)
{
	CCS_GET_CHILD(_myLayout, cocos2d::ui::ScrollView, svReward, UI_SCROLLVIEW_REWARD);
	svReward->removeAllChildren();
	std::vector<std::string> result = split(reward, "|");
	CCS_GET_CHILD(_myLayout, Text, expText, UI_TEXT_EXP);
	expText->setText("");
	for (int i = 0; i < result.size(); i++)
	{
		auto res = split(result[i], ",");

		if (res[1] == "0")
		{
			continue;
		}

		if (res[0] == "-2")
		{
			expText->setText("+" + STRING(res[1]));
		}
		else{
			auto other8 = CCS_CREATE_LAYER("UI_Other_8");

			CCS_GET_CHILD(other8, ImageView, img, UI_OTHER8_IMAGEVIEW_REPLACE);
			CCS_GET_CHILD(other8, TextBMFont, num, UI_OTHER8_TEXTBMFONT_NUMBER);
			CCS_GET_CHILD(other8, ImageView, quality, UI_OTHER8_IMAGEVIEW_QUALITY);

			if (res[0] > "0")			//奖励道具
			{
				auto item = GameData::getInstance()->getItem(INT(res[0]));

				img->loadTexture(GET_PROP_ICON(item.icon), TextureResType::PLIST);
				img->setTouchEnabled(true);
				img->setVisible(true);
				num->setText(res[1].c_str());
				if (item.type == ItemData::ItemType::EQUIP && item.quality > 0)
				{
					quality->loadTexture(GET_PROP_ICON_MASK(item.icon), TextureResType::PLIST);
					quality->setColor(GetEquipQualityColor(item.quality));
				}
				else
				{
					quality->setVisible(false);
				}

				auto equip = ItemData::create();
				equip->setInfo(item);
				img->setUserObject(equip);
				img->addTouchEventListener(this, toucheventselector(MissionView::touchButton));
			}
			else if (res[0] == "0")		//奖励金钱
			{
				img->loadTexture("ss_ty_gold.png", TextureResType::PLIST);
				img->setVisible(true);
				num->setText(res[1].c_str());
			}
			else if (res[0] == "-1")	//奖励钻石
			{
				img->loadTexture("ss_ty_diamond.png", TextureResType::PLIST);
				img->setVisible(true);
				num->setText(res[1].c_str());
			}
			else if (res[0] == "-3")	//奖励伙伴
			{
				std::vector<std::string> r = split(res[1], "/");
				img->loadTexture(GET_PLAYER_HEAD2(INT(r[0])), TextureResType::PLIST);
				img->setVisible(true);
			}

			svReward->addChild(other8);
			//svReward->setTouchEnabled(true);
		}
	}
}

void MissionView::loadMissionList(int state, int type)
{
	auto scrollView = dynamic_cast<ui::ScrollView*>(_myLayout->getChildByTag(UI_SCROLLVIEW));
	auto userData = GameData::getInstance()->getUserData();
	int height = 0;
	auto tasks = userData->getTasks();
	for (auto task : tasks)
	{
		if (task.second->getInfo().state == state&&task.second->getInfo().type == type)
		{
			auto taskInfo = task.second->getInfo();
			auto mission3 = CCS_CREATE_LAYER("UI_Mission_3");
			auto bg = dynamic_cast<CheckBox*>(mission3->getChildByTag(UI_IMAGEVIEW_BG));
			auto title = dynamic_cast<Text*>(mission3->getChildByTag(UI_MISSION3_TITLE));
			auto info = dynamic_cast<Text*>(mission3->getChildByTag(UI_MISSION3_INFO));
			auto mark = dynamic_cast<ImageView*>(mission3->getChildByTag(UI_MISSION3_MARK));
			if (taskInfo.state == 1)
				mark->loadTexture("ss_task_taskno.png", TextureResType::PLIST);
			else
				mark->loadTexture("rss_myship_select.png", TextureResType::PLIST);
			if (scrollView->getChildrenCount() == 0)
			{
				bg->setSelectedState(true);
				/*title->setColor(Color3B(255, 222, 0));
				info->setColor(Color3B(255, 222, 0));*/
			}
			else
			{
				/*title->setColor(Color3B(79, 54, 14));
				info->setColor(Color3B(79, 54, 14));*/
			}
			title->setText(LocalizeService::getInstance()->getString(TASK_TYPE[taskInfo.type].c_str()));
			info->setText(LocalizeService::getInstance()->getString(taskInfo.name));
			bg->setTag(task.second->getInfo().mid + 100000);
			bg->setUserObject(task.second);
			bg->addTouchEventListener(this, toucheventselector(MissionView::touchButton));
			height = mission3->getContentSize().height;
			//i++;
			mission_CheckList.push_back(bg);
			scrollView->addChild(mission3);
		}
	}
	scrollView->setInnerContainerSize(Size(scrollView->getContentSize().width, scrollView->getChildrenCount()*height));
}