#ifndef __SOS_SOUND_SERVICE__
#define __SOS_SOUND_SERVICE__

#include "cocos2d.h"
#include "../SOSConfig.h"

class SoundService : public cocos2d::Ref
{
public:
	FUNC_INSTANCE(SoundService);

public:
	void playBGM(std::string sound = "", bool init = false);
	void playEAX(std::string sound);
	void playSFX(std::string sound);

	void unload(std::string sound);

	void openOrclose(bool b);

private:
	std::string _sound;
};

#endif //__SOS_SOUND_SERVICE__