#ifndef __WAREHOUSE_VIEW_H__
#define __WAREHOUSE_VIEW_H__

#include "cocos2d.h"
#include "ui/CocosGUI.h"
#include "cocostudio/CocoStudio.h"
#include "../SOSConfig.h"
#include "../GameData.h"
#include "PopupView.h"

class WarehouseView : public PopupView
{
public:
	CREATE_FUNC(WarehouseView);
	static cocos2d::Layer* createScene();

public:
	WarehouseView();
	virtual ~WarehouseView();
	virtual bool init() override;
	virtual void onEnter() override;
	void menuCloseCallback(cocos2d::Ref* pSender);
	void touchButton(cocos2d::Ref* object, cocos2d::ui::TouchEventType type);

	void pageViewEvent(cocos2d::Ref *pSender, cocos2d::ui::PageViewEventType type);

private:
	cocos2d::Node* _warehouse;

};

#endif