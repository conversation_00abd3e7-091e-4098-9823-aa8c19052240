#ifndef __FORMATION_SCENE_H__
#define __FORMATION_SCENE_H__

#include "cocos2d.h"
#include "ui/CocosGUI.h"
#include "cocostudio/CocoStudio.h"
#include "../SOSConfig.h"
#include "../GameData.h"
#include "../Transactions/PartnerTransaction.h"
#include "BaseScene.h"

class FormationScene : public BaseScene
{
public:
	static cocos2d::Scene* createScene();

	CREATE_FUNC(FormationScene);

public:
	enum class TouchType
	{
		NOTHING,
		PLAYER_IN_READY,
		PLAYER_IN_POSITIONS,
	};

	FormationScene();
	virtual ~FormationScene();

	virtual bool init() override;
	virtual void onEnter() override;
	virtual void update(float dt) override;
	virtual bool onTouchBegan(cocos2d::Touch* touch, cocos2d::Event* event) override;
	virtual void onTouchMoved(cocos2d::Touch* touch, cocos2d::Event* event) override;
	virtual void onTouchEnded(cocos2d::Touch* touch, cocos2d::Event* event) override;

	void initFormation(float dt);
	void menuCloseCallback(cocos2d::Ref* pSender);
	void touchButton(cocos2d::Ref* object, cocos2d::ui::TouchEventType type);
	// void touchCheckBox(cocos2d::Ref* object, cocos2d::ui::CheckBoxEventType type);

private:
	void initSkillButton();
	int countPlayers();

private:
	cocos2d::EventListenerTouchOneByOne* _listener;
	cocos2d::ui::ScrollView* scrollview;
	Node* node;

	std::vector<cocos2d::ui::Widget *> _actionVector;
	cocos2d::ui::Widget *_touchAction;
	TouchType _touchType;
	int _touchPosition;
	int _index;

	cocos2d::ui::Widget *_handAction;

};

#endif // __FORMATION_SCENE_H__