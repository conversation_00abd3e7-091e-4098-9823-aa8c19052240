#include "RegisterView.h"
#include "../Transactions/LoginTransaction.h"
#include "../GameData.h"
#include "../Services/LocalizeService.h"
#include "../Views/AlertView.h"
#include "../Scenes/BaseScene.h"
#include "../Services/NewguidanceService.h"

USING_NS_CC;
USING_NS_CC_CCS;
USING_NS_CC_UI;

enum TagRegisterView
{
	UI_BUTTON_DAY = 70010,
	UI_IMAGEVIEW_DAY = 70011,
	UI_BUTTON_CLOSE = 70024,

	UI_LAYOUT = 70025,
	UI_BUTTON_GET = 70026,
};

RegisterView::RegisterView()
{

}

RegisterView::~RegisterView()
{
	this->getEventDispatcher()->removeCustomEventListeners(EVENT_GAME_LOGINREWARD);
	this->getEventDispatcher()->removeCustomEventListeners(EVENT_GAME_LOGINREWARD);
}


bool RegisterView::init()
{
	if (!PopupView::init())
	{
		return false;
	}

	_autoClose = false;

	ui = CCS_CREATE_LAYER("UI_Register_1");
	ui->setTag(TagBaseScene::SIGNIN_VIEW);
	this->addChild(ui);

	auto userData = GameData::getInstance()->getUserData();
	for (int i = 0; i < 7; i++)
	{
		CCS_GET_CHILD(ui, Button, btn, UI_BUTTON_DAY + i * 2);
		CCS_GET_CHILD(ui, ImageView, img, UI_IMAGEVIEW_DAY + i * 2);
		btn->setPressedActionEnabled(true);
		if (i + 1 == userData->getInfo().loginCount)
		{
			if (userData->getInfo().isReward)
			{
				img->setVisible(true);
			}
			else
			{
				btn->setTouchEnabled(true);
				btn->addTouchEventListener(this, toucheventselector(RegisterView::touchButton));
			}
			btn->loadTextures(GET_LOGIN_DAY(i + 1), GET_LOGIN_DAY(i + 1), "", TextureResType::PLIST);
		}
		if (userData->getInfo().loginCount > i + 1)
		{
			btn->loadTextures(GET_LOGIN_DAY(i + 1), GET_LOGIN_DAY(i + 1), "", TextureResType::PLIST);
			img->setVisible(true);
		}
	}

	CCS_GET_CHILD(ui, Button, closeBtn, UI_BUTTON_CLOSE);
	closeBtn->setPressedActionEnabled(true);
	closeBtn->addTouchEventListener(this, toucheventselector(RegisterView::touchButton));
	return true;
}

void RegisterView::onEnter()
{
	this->getEventDispatcher()->addCustomEventListener(EVENT_GAME_LOGINREWARD, [=](EventCustom* event)
	{
		this->removeChildByTag(99999);

		auto layer = CCS_CREATE_LAYER("UI_Register_2");
		layer->setTag(TagBaseScene::SIGNIN_ALERT_VIEW);

		CCS_GET_CHILD(layer, Button, btn, UI_BUTTON_GET);
		CCS_GET_CHILD(layer, Layout, panel, UI_LAYOUT);
		btn->setPressedActionEnabled(true);
		btn->addTouchEventListener(this, toucheventselector(RegisterView::touchButton));
		this->addChild(layer, this->getChildrenCount() + 1, TagBaseScene::SIGNIN_ALERT_VIEW);

		auto records = GameData::getInstance()->getRecordReward();
		std::string str = "";
		auto label = LabelTTF::create();
		label->setHorizontalAlignment(cocos2d::TextHAlignment::CENTER);
		label->setFontSize(28);
		label->setPosition(panel->getContentSize().width / 2, panel->getContentSize().height / 2);
		panel->addChild(label);
		for (auto record : records)
		{
			if (record.second.rewardID == GameData::getInstance()->getUserData()->getInfo().loginCount)
			{
				std::vector<std::string> result = split(record.second.reward, "|");
				std::map<int, int> maps;
				for (auto a : result)
				{
					std::vector<std::string> res = split(a, ",");
					if (INT(res[0]) == -3)
					{
						str += LocalizeService::getInstance()->getString("655") + " : " + GameData::getInstance()->getPlayerInfo(INT(res[1])).name + "\r\n";
					}
					else if (INT(res[0]) == -2)
					{
						str += LocalizeService::getInstance()->getString("650") + " : " + STRING(res[1]) + "\r\n";
					}
					else if (INT(res[0]) == -1)
					{
						str += LocalizeService::getInstance()->getString("648") + " : " + STRING(res[1]) + "\r\n";
					}
					else if (INT(res[0]) == 0)
					{
						str += LocalizeService::getInstance()->getString("649") + " : " + STRING(res[1]) + "\r\n";
					}
					else if (INT(res[0]) > 0)
					{
						auto item = GameData::getInstance()->getItem(INT(res[0]));
						str += LocalizeService::getInstance()->getString("656") + " : " + LocalizeService::getInstance()->getString(item.name) + " * " + STRING(res[1]) + "\r\n";
					}
				}
				break;
			}
		}
		label->setString(str);
		GameData::getInstance()->getUserData()->getInfo().isReward = true;

		Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_NEWGUIDANCE_START);
	});

	PopupView::onEnter();
}

void RegisterView::touchButton(Ref* obj, cocos2d::ui::TouchEventType eventType)
{
	auto widget = dynamic_cast<Widget*>(obj);
	int tag = widget->getTag();

	switch (eventType)
	{
	case TouchEventType::TOUCH_EVENT_BEGAN:
		break;
	case TouchEventType::TOUCH_EVENT_ENDED:
		if (tag == (INT(widget->getName()) - 1) * 2 + UI_BUTTON_DAY)
		{
			bool b = true;
			auto records = GameData::getInstance()->getRecordReward();
			auto userData = GameData::getInstance()->getUserData();
			for (auto record : records)
			{
				if (record.second.rewardID == GameData::getInstance()->getUserData()->getInfo().loginCount)
				{
					std::vector<std::string> result = split(record.second.reward, "|");
					for (auto a : result)
					{
						std::vector<std::string> res = split(a, ",");
						if (INT(res[0]) == -3)
						{
							int playerNum = 0;
							auto players = userData->getPlayers();
							for (auto player : players)
							{
								if (player->getInfo().appoint != 1)
								{
									playerNum++;
								}
							}

							int restNum = 0;
							auto ships = userData->getShips();
							for (auto ship : ships)
							{
								if (ship.second->getInfo().use)
								{
									auto cabins = ship.second->getCabins();
									for (auto cab : cabins)
									{
										if (cab->getInfo().type == CabinData::CabinType::REST_HOUSE)
										{
											restNum = cab->getInfo().subjoin1;
											break;
										}
									}
									break;
								}
							}

							if (restNum > playerNum)
								b = true;
							else
								b = false;
							break;
						}
					}
				}
			}
			if (b)
			{
				LoginTransaction::getInstance()->getLoginReward();
				this->addChild(AlertView::create(LocalizeService::getInstance()->getString("632"), AlertType::LOADING), 10000, 99999);
			}
			else
				this->addChild(AlertView::create(LocalizeService::getInstance()->getString("4013"), AlertType::IDLE), 999999);
		}
		else if (tag == UI_BUTTON_CLOSE)
		{
			this->removeFromParent();
		}
		else if (tag == UI_BUTTON_GET)
		{
			Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_NEWGUIDANCE_START);
			this->removeFromParent();
		}
		break;
	}
}