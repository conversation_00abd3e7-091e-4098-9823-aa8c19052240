//
//  ShipData.cpp
//  TestSOS
//
//  Created by <PERSON><PERSON> on 14-1-22.
//
//

#include "ShipData.h"
#include "GameData.h"

USING_NS_CC;

int ShipData::getCP()
{
	int cp = 0;
	for (auto cabin : _cabins)
	{
		if (cabin->getInfo().type == CabinData::CabinType::CAPTAIN_HOUSE)
		{
			cp += cabin->getInfo().subjoin1;
			if (cabin->getInfo().appoint != "")
			{
				auto player = GameData::getInstance()->getPlayer(cabin->getInfo().appoint);
				cp += player->getCP();
			}
		}
	}
	return cp;
}

int ShipData::getOP()
{
	int op = 0;
	for (auto cabin : _cabins)
	{
		if (cabin->getInfo().type == CabinData::CabinType::OPERATE_HOUSE)
		{
			op += cabin->getInfo().subjoin1;
			if (cabin->getInfo().appoint != "")
			{
				auto player = GameData::getInstance()->getPlayer(cabin->getInfo().appoint);
				op += player->getOP();
			}
		}
	}
	return op;
}

int ShipData::getSpace()
{
	int space = 0;
	for (auto cabin : _cabins)
	{
		if (cabin->getInfo().type == CabinData::CabinType::WARE_HOUSE)
		{
			CCLOG("mid: %d, space: %f", cabin->getInfo().mid, cabin->getInfo().subjoin1);
			space += cabin->getInfo().subjoin1;
		}
	}
	return space;
}

int ShipData::getPlayersLimit()
{
	int limit = 0;
	for (auto cabin : _cabins)
	{
		if (cabin->getInfo().type == CabinData::CabinType::REST_HOUSE)
		{
			limit += cabin->getInfo().subjoin1;
		}
	}
	return limit;
}

int ShipData::getTrainingLimit()
{
	int limit = 0;
	for (auto cabin : _cabins)
	{
		if (cabin->getInfo().type == CabinData::CabinType::TRAIN_HOUSE)
		{
			limit += cabin->getInfo().subjoin1;
		}
	}
	return limit;
}

float ShipData::getBuyDiscount()
{
	float discount = 0;
	for (auto cabin : _cabins)
	{
		if (cabin->getInfo().type == CabinData::CabinType::OPERATE_HOUSE)
		{
			discount += cabin->getInfo().subjoin1;
		}
	}
	return discount;
}

float ShipData::getSellDiscount()
{
	float discount = 0;
	for (auto cabin : _cabins)
	{
		if (cabin->getInfo().type == CabinData::CabinType::OPERATE_HOUSE)
		{
			discount += cabin->getInfo().subjoin2;
		}
	}
	return discount;
}

