#ifndef __SOS_DIALOG_VIEW__
#define __SOS_DIALOG_VIEW__

#include "cocos2d.h"
#include "../SOSConfig.h"
#include "ui/CocosGUI.h"

static const std::string EVENT_DIALOG_CLOSED = "event_dialog_closed";

class DialogView : public cocos2d::Layer
{
public:
	CREATE_FUNC(DialogView);

	static DialogView* create(std::string dialogID);

public:
	virtual bool init() override;
	virtual bool onTouchBegan(cocos2d::Touch* touch, cocos2d::Event* event) override;
	virtual void onTouchMoved(cocos2d::Touch* touch, cocos2d::Event* event) override;
	virtual void onTouchEnded(cocos2d::Touch* touch, cocos2d::Event* event) override;

	void taskDialog(std::vector<std::string> list);
};

#endif