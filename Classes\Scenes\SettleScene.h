#ifndef __SETTLE_SCENE_H__
#define __SETTLE_SCENE_H__

#include "cocos2d.h"
#include "ui/CocosGUI.h"
#include "cocostudio/CocoStudio.h"
#include "../GameData.h"
#include "../Views/PopupView.h"
#include "../Datas/FightData.h"

static const std::string EVENT_SETTLE_CHANGED = "event_settle_changed";

class SettleScene : public PopupView
{
public:
	CREATE_FUNC(SettleScene);

	static cocos2d::Layer* createScene(FightData* fight);

public:
	SettleScene();
	virtual ~SettleScene();
	virtual bool init() override;
	virtual void onEnter() override;

	void jjcInit();
	void copiesInit();

	void touchButton(cocos2d::Ref* object, cocos2d::ui::TouchEventType type);
	void onStartEvent(cocostudio::Armature *armature, cocostudio::MovementEventType movementType, const std::string& movementID);
	
private:
	cocos2d::Node* node;
};

#endif // __SETTLE_SCENE_H__

