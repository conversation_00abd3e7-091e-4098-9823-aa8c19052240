//
//  HTTPService.h
//  TestSOS
//
//  Created by <PERSON><PERSON> on 13-12-17.
//
//

#ifndef __TestSOS__HTTPService__
#define __TestSOS__HTTPService__

#include "cocos2d.h"
#include "network/HttpClient.h"
#include "json/document.h"
#include "../SOSConfig.h"

static const std::string EVENT_CONNECT_ERROR = "event_connect_error";
static const std::string EVENT_SERVER_ERROR = "event_server_error";
static const std::string EVENT_CLIENT_ERROR = "event_client_error";
static const std::string EVENT_HTTP_SERVER_ERROR = "event_http_server_error";
static const std::string EVENT_HTTP_CLIENT_ERROR = "event_http_client_error";

static rapidjson::Document::AllocatorType _allocator;

class HTTPData : public cocos2d::Ref
{
public:
	FUNC_CREATE(HTTPData);

public:
	HTTPData();
	virtual ~HTTPData();

public:
	inline rapidjson::Value& getRoot() { return _root; }
	inline void setRoot(rapidjson::Value& root) { _root = root; }

public:
	inline int readInt(const char* name) { return _root[name].GetInt(); }
	inline bool readBool(const char* name) { return _root[name].GetBool(); }
	inline double readDouble(const char* name) { return _root[name].GetDouble(); }
	inline const char* readString(const char* name) { 
		if(_root[name].IsNull()){
			return "";
		}
		return _root[name].GetString();
	}

	HTTPData* readData(const char* name);
	std::vector<HTTPData *> readDatas(const char* name);

public:
	inline void write(const char* name, int value) { _root.AddMember(name, value, _allocator); }
	inline void write(const char* name, bool value) { _root.AddMember(name, value, _allocator); }
	inline void write(const char* name, double value) { _root.AddMember(name, value, _allocator); }
	inline void write(const char* name, const char* value) { _root.AddMember(name, value, _allocator); }

	void write(const char* name, std::vector<HTTPData *> value);

public:
	void update(const char* name, const char* value);

private:
	rapidjson::Value _root;
};

class HTTPService : public cocos2d::Ref
{
public:
	FUNC_INSTANCE(HTTPService)

public:
	HTTPService();
	virtual ~HTTPService();

public:
	inline void setHeadInfo(std::string key, std::string value) { 
		_headers.clear();
		_headers.push_back(key + ": " + value); 
	}

	void request(const char* url, HTTPData* data);
	void request(const char* url, HTTPData* data, const char* tag);
	void request(const char* url, const char* tag);
	void onResponseCallback(cocos2d::network::HttpClient *sender, cocos2d::network::HttpResponse *response);

private:
	void send();
	void send(cocos2d::network::HttpRequest* request);

private:
	std::vector<std::string> _headers;
	cocos2d::Vector<cocos2d::network::HttpRequest*> _requests;
};

#endif /* defined(__TestSOS__HTTPService__) */
