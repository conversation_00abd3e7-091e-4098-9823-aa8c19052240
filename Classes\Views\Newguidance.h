#ifndef __NEWGUIDANCE_3_H__
#define __NEWGUIDANCE_3_H__

#include "cocos2d.h"
#include "ui/CocosGUI.h"
#include "cocostudio/CocoStudio.h"
#include "../SOSConfig.h"
#include "PopupView.h"
#include "../GameData.h"

static const std::string EVENT_GUIDANCE_FINISHED = "event_guidance_finished";
static const std::string EVENT_GUIDANCE_ALL_FINISHED = "event_guidance_all_finished";

class Newguidance : public cocos2d::Layer
{
public:
	CREATE_FUNC(Newguidance);
	static cocos2d::Layer* createScene();

public:
	Newguidance();
	virtual ~Newguidance();

	virtual bool init();
	void menuCloseCallback(cocos2d::Ref* pSender);
	virtual bool onTouchBegan(cocos2d::Touch* touch, cocos2d::Event* event);
	virtual void onTouchMoved(cocos2d::Touch* touch, cocos2d::Event* event);
	virtual void onTouchEnded(cocos2d::Touch* touch, cocos2d::Event* event);
	virtual void onEnter() override;

	void createView();
	void getRunScene();
	void removeFromParentWithDT(float dt);
	
private:
	cocos2d::Node* _myLayout;
	cocos2d::EventListenerTouchOneByOne* _listener;
	NoviceData* _noviceData;
	cocos2d::Point _point;
};

#endif 