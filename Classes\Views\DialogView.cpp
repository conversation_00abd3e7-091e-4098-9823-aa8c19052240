#include "DialogView.h"
#include "cocostudio/CocoStudio.h"
#include "../Services/LocalizeService.h"
#include "../GameData.h"

USING_NS_CC;
USING_NS_CC_CCS;
USING_NS_CC_UI;

enum TagDialogView
{
	UI_FIGHT_4 = 9999,
	UI_IMAGE_NPC = 22000,
	UI_TEXT_NPCNAME = 22002,
	UI_TEXT_CONTENT = 22003,
	UI_ARMATURE_SPEAK = 8563,
	UI_IMAGE_BG = 22001,
};

std::vector<std::string> r_dialogStrs;
int r_dialogIndex;
std::string r_dialogID;

DialogView* DialogView::create(std::string dialogID)
{
	r_dialogID = dialogID;
	return DialogView::create();
}

bool DialogView::init()
{
	if (!Layer::init())
	{
		return false;
	}

	auto ui = CCS_CREATE_LAYER("UI_Fight_4");
	ui->setTag(UI_FIGHT_4);
	this->addChild(ui);

	CCS_GET_CHILD(ui, ImageView, npcImage, UI_IMAGE_NPC);
	CCS_GET_CHILD(ui, Text, nameText, UI_TEXT_NPCNAME);
	CCS_GET_CHILD(ui, Text, contentText, UI_TEXT_CONTENT);

	if (r_dialogID != "")
	{
		auto dialogStr = LocalizeService::getInstance()->getString(r_dialogID);

		r_dialogIndex = 0;
		r_dialogStrs = split(dialogStr, "|");

		if (r_dialogStrs.size() > 1)
		{
			CCS_GET_CHILD(ui, ImageView, bgImage, UI_IMAGE_BG);
			auto point = bgImage->convertToWorldSpace(Point(750, 50));
			ArmatureDataManager::getInstance()->addArmatureFileInfo("Animation_Speak.ExportJson");
			auto armature = Armature::create("Animation_Speak");
			armature->getAnimation()->play("Animation_Speak");
			armature->setPosition(point);
			this->addChild(armature, ui->getChildrenCount() + 1, UI_ARMATURE_SPEAK);
		}

		taskDialog(r_dialogStrs);

		auto listener = EventListenerTouchOneByOne::create();
		listener->setSwallowTouches(true);
		listener->onTouchBegan = CC_CALLBACK_2(DialogView::onTouchBegan, this);
		listener->onTouchMoved = CC_CALLBACK_2(DialogView::onTouchMoved, this);
		listener->onTouchEnded = CC_CALLBACK_2(DialogView::onTouchEnded, this);
		this->getEventDispatcher()->addEventListenerWithSceneGraphPriority(listener, this);
	}

	return true;
}

void DialogView::taskDialog(std::vector<std::string> list)
{
	CCS_GET_CHILD(this, Node, ui, UI_FIGHT_4);
	CCS_GET_CHILD(ui, ImageView, npcImage, UI_IMAGE_NPC);
	CCS_GET_CHILD(ui, Text, nameText, UI_TEXT_NPCNAME);
	CCS_GET_CHILD(ui, Text, contentText, UI_TEXT_CONTENT);

	auto talks = split(list[r_dialogIndex], "^");
	auto talkNpc = talks[0];
	auto talkDialog = talks[1];

	if (talkNpc == "0")
	{
		auto role = GameData::getInstance()->getPlayer(GameData::getInstance()->getUserData()->getInfo().role);
		npcImage->loadTexture(GET_PLAYER_IMAGE(role->getInfo().mid), TextureResType::PLIST);
		nameText->setText(role->getInfo().name + LocalizeService::getInstance()->getString("628"));
	}
	else
	{
		npcImage->loadTexture(GET_NPC_IMAGE(talkNpc.c_str()), TextureResType::PLIST);
		nameText->setText(LocalizeService::getInstance()->getString(NPC_NAME(talkNpc)) + LocalizeService::getInstance()->getString("628"));
	}

	contentText->setText(talkDialog);

	r_dialogIndex++;
}

bool DialogView::onTouchBegan(Touch* touch, Event* event)
{
	return true;
}

void DialogView::onTouchMoved(Touch* touch, Event* event)
{
	
}

void DialogView::onTouchEnded(Touch* touch, Event* event)
{
	if (r_dialogStrs.size() > r_dialogIndex)
	{
		taskDialog(r_dialogStrs);

		Director::getInstance()->getEventDispatcher()->dispatchCustomEvent("event_button_touch_ended", NULL);

		if (r_dialogIndex >= r_dialogStrs.size() || r_dialogStrs.size() == 1)
		{
			this->removeChildByTag(UI_ARMATURE_SPEAK);
		}
	}
	else
	{
		Director::getInstance()->getEventDispatcher()->dispatchCustomEvent("event_button_touch_ended", NULL);
		Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_DIALOG_CLOSED, NULL);
		this->removeFromParent();
	}
}