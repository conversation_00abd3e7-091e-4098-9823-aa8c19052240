#include "HeadView.h"
#include "../Transactions/PersonTransaction.h"

USING_NS_CC;
USING_NS_CC_CCS;
USING_NS_CC_UI;

enum TagHeadView
{
	UI_LAYOUT = 85001,
	UI_BUTTON_CLOSE = 85002,

	UI_BUTTON_HEAD = 85003,
	UI_IMAGEVIEW_SELECT = 85004,
};

std::vector<Widget*> hv_Layers;

HeadView::HeadView()
{

}

HeadView::~HeadView()
{
	hv_Layers.clear();
}

bool HeadView::init()
{
	if (!PopupView::init())
	{
		return false;
	}

	_autoClose = false;

	auto layer = CCS_CREATE_LAYER("UI_Head_1");
	this->addChild(layer);
	CCS_GET_CHILD(layer, Layout, layout, UI_LAYOUT);
	CCS_GET_CHILD(layer, Button, btnClose, UI_BUTTON_CLOSE);
	btnClose->setPressedActionEnabled(true);
	btnClose->addTouchEventListener(this, toucheventselector(HeadView::touchButton));

	auto userData = GameData::getInstance()->getUserData();
	auto players = userData->getPlayers();
	int i = 0;
	int x = -1;

	std::vector<int> intVec;
	bool b = true;
	for (auto player : players)
	{
		auto playerInfo = player->getInfo();
		for (int i : intVec)
		{
			if (i == playerInfo.type)
			{
				b = false;
				break;
			}
			else
			{
				b = true;
			}
		}
		if (b)
			intVec.push_back(playerInfo.type);
	}

	/*if (playerInfo.playType != PlayerData::PlayType::LEAVE)
	{*/
	for (auto index : intVec)
	{
		auto ui = CCS_CREATE_LAYER("UI_Head_2");
		CCS_GET_CHILD(ui, ImageView, img, UI_IMAGEVIEW_SELECT);
		CCS_GET_CHILD(ui, Button, btn, UI_BUTTON_HEAD);
		btn->setPressedActionEnabled(true);
		btn->addTouchEventListener(this, toucheventselector(HeadView::touchButton));
		btn->loadTextures(GET_PLAYER_HEAD2(index), GET_PLAYER_HEAD2(index), "", TextureResType::PLIST);
		layout->addChild(ui);
		btn->setName(STRING(index));
		if (index == GameData::getInstance()->getUserData()->getInfo().face)
		{
			img->setVisible(true);
		}

		if (i % 5 == 0)
		{
			x++;
			ui->setPosition(Point(0, layout->getContentSize().height - ui->getContentSize().height*x - 100));
			i = 0;
		}
		else
		{
			ui->setPosition(Point(ui->getContentSize().width*i, layout->getContentSize().height - ui->getContentSize().height*x - 100));
		}
		i++;

		hv_Layers.push_back(ui);
		//}
	}

	return true;
}

void HeadView::onEnter()
{
	Layer::onEnter();
}

void HeadView::touchButton(Ref* obj, cocos2d::ui::TouchEventType eventType)
{
	auto widget = dynamic_cast<Widget*>(obj);
	int tag = widget->getTag();

	switch (eventType)
	{
	case TouchEventType::TOUCH_EVENT_BEGAN:
		break;
	case TouchEventType::TOUCH_EVENT_ENDED:
		if (tag == UI_BUTTON_CLOSE)
		{
			std::string face;
			for (auto ui : hv_Layers)
			{
				CCS_GET_CHILD(ui, ImageView, img, UI_IMAGEVIEW_SELECT);
				CCS_GET_CHILD(ui, Button, btn, UI_BUTTON_HEAD);
				if (img->isVisible())
				{
					face = btn->getName();
					break;
				}
			}
			PersonTransaction::getInstance()->updateFace(GameData::getInstance()->getUserData()->getInfo().uid, face);
			Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_FACE_CHANGED, &face);
			this->removeFromParent();
		}
		else if (tag == UI_BUTTON_HEAD)
		{
			for (auto ui : hv_Layers)
			{
				CCS_GET_CHILD(ui, ImageView, img, UI_IMAGEVIEW_SELECT);
				CCS_GET_CHILD(ui, Button, btn, UI_BUTTON_HEAD);
				if (btn == widget)
					img->setVisible(true);
				else
					img->setVisible(false);
			}
		}
		break;
	}
}