﻿#include "RecruitView.h"
#include "../GameData.h"
#include "../Services/LocalizeService.h"
#include "../Services/NewguidanceService.h"
#include "AlertView.h"
#include "../Services/SoundService.h"
#include "../Scenes/BaseScene.h"

USING_NS_CC;
USING_NS_CC_CCS; 
USING_NS_CC_UI;

enum tagRecruit
{
	UI_TEXT_NAME = 884,
	UI_TEXBMFONT_JOB = 885,
	UI_IMAGEVIEW_LEVEL = 886,
	UI_TEXT_POWER = 887,
	UI_TEXT_TEXT1 = 891,
	UI_TEXT_TEXT2 = 893,
	UI_TEXT_TEXT3 = 895,
	UI_TEXT_TEXT4 = 897,
	UI_TEXT_TEXT5 = 899,
	UI_TEXT_TEXT6 = 901,
	UI_TEXT_TEXT7 = 903,
	UI_TEXT_TEXT8 = 905,
	UI_BUTTON_GAIN = 906,
	UI_LAYOUT_REPLACE = 907,
	UI_BUTTON_CANNEL = 22010,

	UI3_TEXT_NAME = 859,
	UI3_TEXBMFONT_JOB = 860,
	UI3_IMAGEVIEW_LEVEL = 861,
	UI3_TEXT_POWER = 862,
	UI3_TEXT_TEXT1 = 866,
	UI3_TEXT_TEXT2 = 868,
	UI3_TEXT_TEXT3 = 870,
	UI3_TEXT_TEXT4 = 872,
	UI3_TEXT_TEXT5 = 874,
	UI3_TEXT_TEXT6 = 876,
	UI3_TEXT_TEXT7 = 878,
	UI3_TEXT_TEXT8 = 880,
	UI3_BUTTON_CLOSE = 881,
	UI3_LAYOUT_REPLACE = 882,
};

PlayerData* r_playerData = nullptr;
int mid = 0;
int type = 0;

RecruitView::RecruitView()
{

}

RecruitView::~RecruitView()
{
	this->getEventDispatcher()->removeCustomEventListeners(EVENT_RECRUIT_EXECUTE_SUCCESS);
	r_playerData->release();
}

Layer* RecruitView::createScene()
{
	auto layer = RecruitView::create();
	return layer;
}

Layer* RecruitView::createScene(PlayerData* data , int t)
{
	r_playerData = data;
	r_playerData->retain();
	mid = r_playerData->getInfo().mid;
	type = t;
	auto layer = RecruitView::create();
	return layer;
}

bool RecruitView::init()
{
	if ( !PopupView::init() )
	{
		return false;
	}

	_autoClose = false;

	if(type==1)
	{
		LoadUI();
	}
	else
	{
		LoadUI3();
	}

	return true;
}

void RecruitView::LoadUI()
{
	auto playerInfo = r_playerData->getInfo();

	auto uiLayer = CCS_CREATE_LAYER("UI_Recruit_4");
	uiLayer->setTag(TagBaseScene::RECRUIT_VIEW);
	this->addChild(uiLayer);

	auto gainBtn = dynamic_cast<Button*>(uiLayer->getChildByTag(UI_BUTTON_GAIN));  
	gainBtn->addTouchEventListener(this,toucheventselector(RecruitView::touchButton));  
	auto cannelBtn = dynamic_cast<Button*>(uiLayer->getChildByTag(UI_BUTTON_CANNEL));
	cannelBtn->addTouchEventListener(this, toucheventselector(RecruitView::touchButton));

	auto name=dynamic_cast<Text*>(uiLayer->getChildByTag(UI_TEXT_NAME));
	name->setText(playerInfo.name);
	name->setColor(getAppraisalColor(playerInfo.appraisal));
	auto job= dynamic_cast<TextBMFont*>(uiLayer->getChildByTag(UI_TEXBMFONT_JOB));
	job->setText(LocalizeService::getInstance()->getString(PLAYER_JOB_NAMES[playerInfo.job]).c_str());
	auto level = dynamic_cast<ImageView*>(uiLayer->getChildByTag(UI_IMAGEVIEW_LEVEL));
	if (playerInfo.appraisal == 6)
		level->loadTexture("ss_partner_character_6.png", TextureResType::PLIST);
	else
		level->loadTexture(GET_PLAYER_STAR(playerInfo.appraisal), TextureResType::PLIST);
	//level->setText(LocalizeService::getInstance()->getString(PLAYER_APPRAISAL[playerInfo.appraisal]));
	//level->setColor(getAppraisalColor(playerInfo.appraisal));
	auto power=dynamic_cast<Text*>(uiLayer->getChildByTag(UI_TEXT_POWER));
	power->setText(STRING(r_playerData->getFP()));
	auto maxLife=dynamic_cast<Text*>(uiLayer->getChildByTag(UI_TEXT_TEXT1));
	maxLife->setText(STRING(r_playerData->getHP()));
	auto physicalAttacks=dynamic_cast<Text*>(uiLayer->getChildByTag(UI_TEXT_TEXT2));
	physicalAttacks->setText(STRING(r_playerData->getATK()));
	auto physicalDefense=dynamic_cast<Text*>(uiLayer->getChildByTag(UI_TEXT_TEXT3));
	physicalDefense->setText(STRING(r_playerData->getDEF()));
	auto magicAttacks=dynamic_cast<Text*>(uiLayer->getChildByTag(UI_TEXT_TEXT4));
	magicAttacks->setText(STRING(r_playerData->getMAG()));
	auto magicDefense=dynamic_cast<Text*>(uiLayer->getChildByTag(UI_TEXT_TEXT5));
	magicDefense->setText(STRING(r_playerData->getRES()));
	auto captainValue=dynamic_cast<Text*>(uiLayer->getChildByTag(UI_TEXT_TEXT6));
	captainValue->setText(STRING(r_playerData->getCP()));
	auto navigationValue=dynamic_cast<Text*>(uiLayer->getChildByTag(UI_TEXT_TEXT7));
	navigationValue->setText(STRING(r_playerData->getNP()));
	auto operateValue=dynamic_cast<Text*>(uiLayer->getChildByTag(UI_TEXT_TEXT8));
	operateValue->setText(STRING(r_playerData->getOP()));
	//auto layout=dynamic_cast<Layout*>(uiLayer->getChildByTag(UI_LAYOUT_REPLACE));

	CCS_GET_CHILD(uiLayer, ImageView, layout, UI_LAYOUT_REPLACE);
	layout->loadTexture(GET_PLAYER_IDLE(playerInfo.type, playerInfo.appraisal), TextureResType::PLIST);

// 	Armature* arm = Armature::create(GET_PLAYER_NAME(playerInfo.type, playerInfo.appraisal<4 ? 1 : playerInfo.appraisal));
// 	arm->getAnimation()->play(PlayerAction::IDLE);
// 	layout->addNode(arm);

	auto size = Director::getInstance()->getWinSize();

	auto emitter = ParticleSystemQuad::create("Efficiency/star2.plist");
	emitter->setPosition(Point(size.width / 2, size.height / 2));
	this->addChild(emitter);

	SoundService::getInstance()->playSFX(SOUND_EFFECT_RECRUIT);
}

void RecruitView::LoadUI3()
{
	

	auto playerInfo = r_playerData->getInfo();

	auto uiLayer = CCS_CREATE_LAYER("UI_Recruit_3");
	addChild(uiLayer);  
	auto closeBtn = dynamic_cast<Button*>(uiLayer->getChildByTag(UI3_BUTTON_CLOSE));  
	closeBtn->addTouchEventListener(this,toucheventselector(RecruitView::touchButton));  

	auto name=dynamic_cast<Text*>(uiLayer->getChildByTag(UI3_TEXT_NAME));
	name->setText(playerInfo.name);
	name->setColor(getAppraisalColor(playerInfo.appraisal));
	auto job= dynamic_cast<TextBMFont*>(uiLayer->getChildByTag(UI3_TEXBMFONT_JOB));
	job->setText(LocalizeService::getInstance()->getString(PLAYER_JOB_NAMES[playerInfo.job]).c_str());
	auto level=dynamic_cast<ImageView*>(uiLayer->getChildByTag(UI3_IMAGEVIEW_LEVEL));
	if (playerInfo.appraisal == 6)
		level->loadTexture("ss_partner_character_6.png", TextureResType::PLIST);
	else
		level->loadTexture(GET_PLAYER_STAR(playerInfo.appraisal), TextureResType::PLIST);
	//level->setText(LocalizeService::getInstance()->getString(PLAYER_APPRAISAL[playerInfo.appraisal]));
	//level->setColor(getAppraisalColor(playerInfo.appraisal));
	auto power=dynamic_cast<Text*>(uiLayer->getChildByTag(UI3_TEXT_POWER));
	power->setText(STRING(r_playerData->getFP()));
	auto maxLife=dynamic_cast<Text*>(uiLayer->getChildByTag(UI3_TEXT_TEXT1));
	maxLife->setText(STRING(r_playerData->getHP()));
	auto physicalAttacks=dynamic_cast<Text*>(uiLayer->getChildByTag(UI3_TEXT_TEXT2));
	physicalAttacks->setText(STRING(r_playerData->getATK()));
	auto physicalDefense=dynamic_cast<Text*>(uiLayer->getChildByTag(UI3_TEXT_TEXT3));
	physicalDefense->setText(STRING(r_playerData->getDEF()));
	auto magicAttacks=dynamic_cast<Text*>(uiLayer->getChildByTag(UI3_TEXT_TEXT4));
	magicAttacks->setText(STRING(r_playerData->getMAG()));
	auto magicDefense=dynamic_cast<Text*>(uiLayer->getChildByTag(UI3_TEXT_TEXT5));
	magicDefense->setText(STRING(r_playerData->getRES()));
	auto captainValue=dynamic_cast<Text*>(uiLayer->getChildByTag(UI3_TEXT_TEXT6));
	captainValue->setText(STRING(r_playerData->getCP()));
	auto navigationValue=dynamic_cast<Text*>(uiLayer->getChildByTag(UI3_TEXT_TEXT7));
	navigationValue->setText(STRING(r_playerData->getNP()));
	auto operateValue=dynamic_cast<Text*>(uiLayer->getChildByTag(UI3_TEXT_TEXT8));
	operateValue->setText(STRING(r_playerData->getOP()));
	//auto layout=dynamic_cast<Layout*>(uiLayer->getChildByTag(UI3_LAYOUT_REPLACE));

	CCS_GET_CHILD(uiLayer, ImageView, layout, UI3_LAYOUT_REPLACE);
	layout->loadTexture(GET_PLAYER_IDLE(playerInfo.type, playerInfo.appraisal), TextureResType::PLIST);

// 	Armature* arm = Armature::create(GET_PLAYER_NAME(playerInfo.type, playerInfo.appraisal < 4 ? 1 : playerInfo.appraisal));
// 	arm->getAnimation()->play(PlayerAction::IDLE);  
// 	arm->getAnimation()->setSpeedScale(1); 
// 	layout->addNode(arm);
	
}

void RecruitView::onEnter()
{
	this->getEventDispatcher()->addCustomEventListener(EVENT_RECRUIT_EXECUTE_SUCCESS, [=](EventCustom* event) {
		this->removeChildByTag(99999);

		auto onlyID = static_cast<std::string *>(event->getUserData());
		r_playerData->getInfo().id = *onlyID;
		GameData::getInstance()->setPlayer(r_playerData);
		GameData::getInstance()->getUserData()->getPlayers().pushBack(r_playerData);
		GameData::getInstance()->isTaskComplete(GameData::MissionType::PLAYER, 0, 0);
		Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_PUBSCENE_CHANGED, NULL);
		Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_NEWGUIDANCE_START);
		this->removeFromParent();
	});

	PopupView::onEnter();
}

void RecruitView::menuCloseCallback(Ref* pSender)
{
	Director::getInstance()->end();

#if (CC_TARGET_PLATFORM == CC_PLATFORM_IOS)
	exit(0);
#endif
}


void RecruitView::touchButton(Ref* obj,cocos2d::ui::TouchEventType eventType)  
{  
	auto button = dynamic_cast<Button*>(obj);  
	int tag = button->getTag();  
	switch (eventType)
	{
	case TouchEventType::TOUCH_EVENT_ENDED: 
		if(tag==UI_BUTTON_GAIN)
		{
			auto userData = GameData::getInstance()->getUserData();
			auto userInfo = userData->getInfo();
			RecruitTransaction::getInstance()->Execute(userInfo.uid, mid,userInfo.city);
			this->addChild(AlertView::create(LocalizeService::getInstance()->getString("632"), AlertType::LOADING), 1000, 99999);
			//this->addChild(LoadingView::create(), 1000, 99999);
		}
		else if (tag == UI_BUTTON_CANNEL)
		{
			Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_RECRUIT_CLOSE, NULL);
			this->removeFromParent();
		}
		else
		{
			Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_RECRUIT_CLOSE, NULL);
			this->removeFromParent();
		}
		break;
	}
}
