#ifndef __SOS_FIGHT_TRANSACTION__
#define __SOS_FIGHT_TRANSACTION__

#include "cocos2d.h"
#include "../SOSConfig.h"

static const std::string EVENT_FIGHT_BEGIN_SUCCESS = "event_fight_begin_success";
static const std::string EVENT_GAME_PROPS_GET = "event_props_get_success";

class FightTransaction : public cocos2d::Ref
{
public:
	FUNC_INSTANCE(FightTransaction);

public:
	FightTransaction();
	virtual ~FightTransaction();

	void begin(const std::string& uid, const std::string& targetID);
	void getRewards(const std::string& uid, const int& explore, const std::string& adjunct);
};

#endif