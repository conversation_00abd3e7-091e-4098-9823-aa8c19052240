//
//  Ship.cpp
//  TestSOS
//
//  Created by <PERSON><PERSON> on 14-1-9.
//
//

#include "Ship.h"
#include "SOSConfig.h"

USING_NS_CC;
USING_NS_CC_CCS;

using namespace std;

static const char* SHIP_ACTIONS[8] = { "Face_a", "Face_b", "Face_c", "Face_d", "Face_e", "Face_f", "Face_g", "Face_h" };
static const double PI = 3.1415926;

Ship* Ship::create(string type)
{
	auto ship = new Ship();
	if (ship && ship->init(type))
	{
		ship->autorelease();
		return ship;
	}

	CC_SAFE_DELETE(ship);
	return NULL;
}

Ship::Ship()
: _face(SHIP_FACE::LEFT_UP)
, _speed(150)
, _moving(nullptr)
{

}

Ship::~Ship()
{

}

bool Ship::init()
{
	return true;
}

bool Ship::init(string type)
{
	if (!this->init())
	{
		CCLOG("ship init fail ...");
		return false;
	}

	_armature = Armature::create(type);
	_armature->getAnimation()->play(SHIP_ACTIONS[_face]);
	_armature->setPositionY(60);
	this->addChild(_armature);

	//this->scheduleUpdate();

	return true;
}

void Ship::update(float delta)
{
	//    auto dis = this->getPosition().getDistance(_targetPos);
	//    if (dis >= _speed)
	//    {
	//        
	//    }
}

void Ship::setFace(SHIP_FACE face)
{
	_face = face;
	_armature->getAnimation()->play(SHIP_ACTIONS[_face]);
}

void Ship::faceTo(Point point)
{
	CCLOG("target point: %f, %f", point.x, point.y);
	CCLOG("current point: %f, %f", this->getPositionX(), this->getPositionY());

	if (point.x > this->getPositionX())
	{
		if (point.y == this->getPositionY())
		{
			this->setFace(RIGHT);
		}
		else if (point.y > this->getPositionY())
		{
			this->setFace(RIGHT_UP);
		}
		else
		{
			this->setFace(RIGHT_DOWN);
		}
	}
	else if (point.x < this->getPositionX())
	{
		if (point.y == this->getPositionY())
		{
			this->setFace(LEFT);
		}
		else if (point.y > this->getPositionY())
		{
			this->setFace(LEFT_UP);
		}
		else
		{
			this->setFace(LEFT_DOWN);
		}
	}
	else
	{
		if (point.y > this->getPositionY())
		{
			this->setFace(UP);
		}
		else if (point.y < this->getPositionY())
		{
			this->setFace(DOWN);
		}
	}
	//     auto pos = this->getPosition();
	//     auto angle = this->pointToAngle(point, pos);
	//     
	//     SHIP_FACE face;
	//     
	//     if (point.x >= pos.x)
	//     {
	//         if (angle <= 30 && angle >= -30)
	//         {
	//             face = SHIP_FACE::RIGHT;
	//         }
	//         else if (angle > 30 && angle < 60)
	//         {
	//             face = SHIP_FACE::RIGHT_UP;
	//         }
	//         else if (angle >= 60 && angle <= 90)
	//         {
	//             face = SHIP_FACE::UP;
	//         }
	//         else if (angle < -30 && angle > -60)
	//         {
	//             face = SHIP_FACE::RIGHT_DOWN;
	//         }
	//         else if (angle <= -60 && angle >= -90)
	//         {
	//             face = SHIP_FACE::DOWN;
	//         }
	//     }
	//     else
	//     {
	//         if (angle <= 30 && angle >= -30)
	//         {
	//             face = SHIP_FACE::LEFT;
	//         }
	//         else if (angle > 30 && angle < 60)
	//         {
	//             face = SHIP_FACE::LEFT_DOWN;
	//         }
	//         else if (angle >= 60 && angle <= 90)
	//         {
	//             face = SHIP_FACE::DOWN;
	//         }
	//         else if (angle < -30 && angle > -60)
	//         {
	//             face = SHIP_FACE::LEFT_UP;
	//         }
	//         else if (angle <= -60 && angle >= -90)
	//         {
	//             face = SHIP_FACE::UP;
	//         }
	//     }
	//     
	//     this->setFace(face);
}

void Ship::runTo(Point point)
{
	_paths.clear();

	int tx = point.x;
	int ty = point.y;
	int px = this->getPositionX();
	int py = this->getPositionY();

	auto offsetX = abs(tx - px);
	auto offsetY = abs(ty - py);

	if (offsetX != 0 && offsetY != 0 && offsetX != offsetY)
	{
		if (offsetX < offsetY)
		{
			if (ty > py)
			{
				_paths.push_back(Point(px, ty - offsetX));
			}
			else if (ty < py)
			{
				_paths.push_back(Point(px, ty + offsetX));
			}
		}
		else if (offsetX > offsetY)
		{
			if (tx > px)
			{
				_paths.push_back(Point(tx - offsetY, py));
			}
			else if (tx < px)
			{
				_paths.push_back(Point(tx + offsetY, py));
			}
		}
	}

	_paths.push_back(Point(tx, ty));

	for (auto path : _paths)
	{
		CCLOG("path: (%f, %f)", path.x, path.y);
	}

	this->setPositionX(INT(this->getPositionX()));
	this->setPositionY(INT(this->getPositionY()));

	moving();
}

void Ship::runTo(vector<Point> paths)
{
	_paths = paths;

	for (auto path : _paths)
	{
		CCLOG("map path: (%f, %f)", path.x, path.y);
	}

	this->setPositionX(INT(this->getPositionX()));
	this->setPositionY(INT(this->getPositionY()));

	moving();
}

double Ship::pointToAngle(Point p1, Point p2)
{
	if (p2.x - p1.x == 0)
	{
		if (p2.y - p1.y > 0)
		{
			return 90;
		}
		else if (p2.y - p1.y < 0)
		{
			return -90;
		}
		else
		{
			return 0;
		}
	}

	auto angle = atan((p2.y - p1.y) / (p2.x - p1.x)) / PI * 180;
	CCLOG("tan x/y: %f", angle);

	return angle;
}

void Ship::moving()
{
	this->stop();

	if (_paths.size() <= 0)
	{
		return;
	}

	auto point = _paths[0];
	_paths.erase(_paths.begin());

	this->faceTo(point);

	auto dis = this->getPosition().getDistance(point);

	_moving = Sequence::create(
		MoveTo::create(dis / this->getSpeed(), point),
		CallFunc::create(CC_CALLBACK_0(Ship::onMovingCallback, this)),
		NULL);
	this->runAction(_moving);
}

void Ship::onMovingCallback()
{
	moving();
}