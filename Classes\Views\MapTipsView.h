#ifndef __SOS_MAPTIPS_VIEW__
#define __SOS_MAPTIPS_VIEW__

#include "cocos2d.h"
#include "ui/CocosGUI.h"
#include "cocostudio/CocoStudio.h"
#include "PopupView.h"
#include "../SOSConfig.h"

class MapTipsView : public PopupView
{
public:
	CREATE_FUNC(MapTipsView);
	static cocos2d::Layer* createScene(int cityID);

public:
	MapTipsView();
	virtual ~MapTipsView();
	virtual bool init() override;
	void menuCloseCallback(cocos2d::Ref* pSender);

	void touchButton(cocos2d::Ref* object, cocos2d::ui::TouchEventType type);

};

#endif
