#ifndef __WEAPONSHOP_SCENE_H__
#define __WEAPONSHOP_SCENE_H__

#include "cocos2d.h"
#include "ui/CocosGUI.h"
#include "cocostudio/CocoStudio.h"
#include "BaseScene.h"

class WeaponShopScene : public BaseScene
{
public:
	static cocos2d::Scene* createScene();
	virtual bool init();  
	void menuCloseCallback(cocos2d::Ref* pSender);
	void touchButton(cocos2d::Ref* object, cocos2d::ui::TouchEventType type);
	virtual bool onTouchBegan(cocos2d::Touch* touch, cocos2d::Event* event) ;  
	virtual void onTouchMoved(cocos2d::Touch* touch, cocos2d::Event* event) ;  
	virtual void onTouchEnded(cocos2d::Touch* touch, cocos2d::Event* event) ;  
	CREATE_FUNC(WeaponShopScene);

private:
	cocos2d::Node* node;

};

#endif // __WEAPONSHOP_SCENE_H__