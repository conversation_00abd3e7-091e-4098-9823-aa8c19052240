#ifndef __TestSOS__ArenaTransaction__
#define __TestSOS__ArenaTransaction__

#include "cocos2d.h"
#include "../SOSConfig.h"

static const char* EVENT_GAME_RANKGET_SUCCESS = "event_game_rankget_success";

class ArenaTransaction : public cocos2d::Ref
{
public:
	FUNC_INSTANCE(ArenaTransaction);

public:
	ArenaTransaction();
	virtual ~ArenaTransaction();

	void rankGet(std::string uid, int type);

};

#endif