#include "StartScene.h"
#include "../SOSConfig.h"
#include "LoadingResourceScene.h"
#include "../Views/DialogView.h"
#include "../Services/SoundService.h"

USING_NS_CC;
USING_NS_CC_CCS;
USING_NS_CC_UI;

#if (CC_TARGET_PLATFORM == CC_PLATFORM_ANDROID || CC_TARGET_PLATFORM == CC_PLATFORM_IOS)
using namespace cocos2d::experimental::ui;
#endif

Scene* StartScene::createScene()
{
	auto scene = Scene::create();
	auto layer = StartScene::create();
	scene->addChild(layer);
	return scene;
}

StartScene::~StartScene()
{
	this->getEventDispatcher()->removeCustomEventListeners(EVENT_DIALOG_CLOSED);

	ArmatureDataManager::getInstance()->removeArmatureFileInfo("Animation_Open.ExportJson");

// 	for (auto i = 0; i < 4; i++)
// 	{
// 		auto textureName = String::createWithFormat("Animation_Open%d.png", i)->getCString();
// 		Director::getInstance()->getTextureCache()->removeTextureForKey(textureName);
// 
// // 		auto frameName = String::createWithFormat("Animation_Open%d.plist", i)->getCString();
// // 		SpriteFrameCache::getInstance()->removeSpriteFramesFromFile(frameName);
// 	}
}

bool StartScene::init()
{
	if (!BaseScene::init())
	{
		return false;
	}

	this->addChild(DialogView::create("701"));

	return true;
}

void StartScene::onEnter()
{
	SoundService::getInstance()->playBGM(SOUND_FIGHT);

	this->getEventDispatcher()->addCustomEventListener(EVENT_DIALOG_CLOSED, [=](EventCustom* event){
		SoundService::getInstance()->playBGM(SOUND_CITY);
		auto size = Director::getInstance()->getWinSize();
		auto layer = LayerColor::create(Color4B(255, 255, 255, 0), size.width, size.height);
		layer->setAnchorPoint(Point::ZERO);
		layer->runAction(Sequence::create(
			FadeIn::create(1.0f),
			CallFunc::create(CC_CALLBACK_0(StartScene::onFadeInCallback, this)),
			NULL));
		this->addChild(layer, this->getChildrenCount() + 1);
	});

// #if (CC_TARGET_PLATFORM == CC_PLATFORM_ANDROID || CC_TARGET_PLATFORM == CC_PLATFORM_IOS)
// 	CCLOG("play start.mp4");
// 	auto size = Director::getInstance()->getWinSize();
// 	auto vp = VideoPlayer::create();
// 	vp->setFileName("start.mp4");
// 	vp->play();
// 	//vp->setPosition(centerPos);
// 	vp->setAnchorPoint(Vec2::ZERO);
// 	vp->setContentSize(Size(size.width, size.height));
// 	this->addChild(vp);
// 	//_videoPlayer->addEventListener(CC_CALLBACK_2(VideoPlayerTest::videoEventCallback, this));
// #else
// 	ArmatureDataManager::getInstance()->addArmatureFileInfo("Animation_Open.ExportJson");
// 	auto armature = Armature::create("Animation_Open");
// 	armature->getAnimation()->play("Animation1");
// 	auto size = Director::getInstance()->getWinSize();
// 	armature->setPosition(Point(size.width * 0.5, size.height * 0.5));
// 	armature->getAnimation()->setMovementEventCallFunc(CC_CALLBACK_0(StartScene::onAnimationEvent, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3));
// 	this->addChild(armature);
// #endif

// 	ArmatureDataManager::getInstance()->addArmatureFileInfo("Animation_Open.ExportJson");
// 	auto armature = Armature::create("Animation_Open");
// 	armature->getAnimation()->play("Animation1");
// 	auto size = Director::getInstance()->getWinSize();
// 	armature->setPosition(Point(size.width * 0.5, size.height * 0.5));
// 	armature->getAnimation()->setMovementEventCallFunc(CC_CALLBACK_0(StartScene::onAnimationEvent, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3));
// 	this->addChild(armature);

	Layer::onEnter();
}

void StartScene::onFadeInCallback()
{
	Director::getInstance()->replaceScene(LoadingResourceScene::createScene());
}

void StartScene::onFadeOutCallback()
{
	//this->removeChildByTag(989898);
	this->addChild(DialogView::create("701"), this->getChildrenCount() + 1);
	//Director::getInstance()->replaceScene(LoadingResourceScene::createScene());
}

void StartScene::onAnimationEvent(Armature *armature, MovementEventType movementType, const std::string& movementID)
{
	if (movementType == COMPLETE)
	{
		if (movementID == "Animation1")
		{
			armature->getAnimation()->play("Animation2");
		}
		else if (movementID == "Animation2")
		{
			armature->getAnimation()->play("Animation3");
		}
		else if (movementID == "Animation3")
		{
			armature->getAnimation()->play("Animation4");
		}
		else if (movementID == "Animation4")
		{
			auto size = Director::getInstance()->getWinSize();
			auto layer = LayerColor::create(Color4B(0, 0, 0, 0), size.width, size.height);
			layer->setAnchorPoint(Point::ZERO);
			layer->runAction(Sequence::create(
				FadeIn::create(1.0f),
				CallFunc::create(CC_CALLBACK_0(StartScene::onFadeOutCallback, this)),
				NULL));
			this->addChild(layer, this->getChildrenCount() + 1);
			//this->addChild(DialogView::create("701"));
		}
	}
}