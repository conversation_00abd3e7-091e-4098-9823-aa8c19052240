//
//  TaskData.h
//  TestSOS
//
//  Created by <PERSON><PERSON> on 14-2-6.
//
//

#ifndef __TestSOS__TaskData__
#define __TestSOS__TaskData__

#include "cocos2d.h"
#include "../SOSConfig.h"

class TaskData : public cocos2d::Ref
{
public:
    FUNC_CREATE(TaskData)
    
public:
	enum TaskType : int
	{
		MAIN = 1,
		QUESTS,
		EXPLORE
	};

	enum StateType : int
	{
		NO_MISSION = -1,
		UNCLAIMED = 0,
		ACCEPTED = 1,
		FINISHED = 2,
	};

    struct Info
    {
        int mid;
        std::string name;
        int type;
        int state;
        int receivenpc;
		int paynpc;
		int city;
		int deliveryCityID;
		int target;
		std::string description;
		std::string reward;
		std::string receiveTaskDialog;
		std::string payTaskDialog;
		int targetCityID;
    };
    
    struct Factor
    {
        int id;
        int type;
        int value;
    };
    
public:
    inline Info& getInfo() { return _info; }
    inline void setInfo(Info& info) { _info = info; }
    
    inline Factor& getFactor() { return _factor; }
    inline void setFactor(Factor& factor) { _factor = factor; }
    
private:
    Info _info;
    Factor _factor;
};

#endif /* defined(__TestSOS__TaskData__) */
