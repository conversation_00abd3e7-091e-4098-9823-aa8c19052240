#include "EffectService.h"

USING_NS_CC;
USING_NS_CC_CCS;

FUNC_GET_INSTANCE(EffectService);

std::vector<Armature*> r_effects;

void EffectService::play(std::string name, std::string animation)
{
	ArmatureDataManager::getInstance()->addArmatureFileInfo(name + ".ExportJson");
	auto armature = Armature::create(name);
	auto size = Director::getInstance()->getWinSize();
	auto scene = Director::getInstance()->getRunningScene();
	armature->setPosition(Point(size.width / 2, size.height / 2));
	armature->getAnimation()->play(animation);
	armature->getAnimation()->setMovementEventCallFunc(CC_CALLBACK_0(EffectService::onAnimationEvent, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3));
	scene->addChild(armature);
}

void EffectService::onAnimationEvent(Armature *armature, MovementEventType movementType, const std::string& movementID)
{
	if (movementType == COMPLETE)
	{
// 		armature->getAnimation()->stop();
// 		armature->stopAllActions();
// 		armature->removeFromParent();
	}
}