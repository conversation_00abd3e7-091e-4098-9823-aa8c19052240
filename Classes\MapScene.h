//
//  MapScene.h
//  TestSOS
//
//  Created by <PERSON><PERSON> on 14-1-10.
//
//

#ifndef __TestSOS__MapScene__
#define __TestSOS__MapScene__

#include "cocos2d.h"
#include "Ship.h"
#include "Scenes/BaseScene.h"

class MapScene : public cocos2d::Scene
{
public:
	CREATE_FUNC(MapScene);
	static Ship* getShip();
public:
	virtual bool init() override;
};

class MapLayer : public BaseScene
{
public:
	CREATE_FUNC(MapLayer);

public:
	MapLayer() : _map(nullptr), _move(false) {};
	//MapLayer() : _map(nullptr), _ship(nullptr), _move(false) {};
	virtual ~MapLayer();

	virtual bool init() override;
	virtual void update(float dt) override;
	virtual void onEnter() override;

	virtual bool onTouchBegan(cocos2d::Touch *touch, cocos2d::Event *event);
	virtual void onTouchMoved(cocos2d::Touch *touch, cocos2d::Event *event);
	virtual void onTouchEnded(cocos2d::Touch *touch, cocos2d::Event *event);

	void updateWave(float dt);
	void touchButton(cocos2d::Ref* object, cocos2d::ui::TouchEventType type);
	void onMovementEvent(cocostudio::Armature *armature, cocostudio::MovementEventType movementType, const std::string& movementID);
	void onCloudClose();

	void setMapPointByShip();
	void runShip();
private:
	cocos2d::Point convertToMap(cocos2d::Point point);
	cocos2d::Point convertToTiled(cocos2d::Point point);
	std::vector<cocos2d::Point> countPaths(cocos2d::Point startPoint, cocos2d::Point targetPoint);

protected:
	cocos2d::Node* _map;
	cocos2d::Node* _cities;
	//Ship* _ship;
	bool _move;
	cocos2d::Sprite* _sp;
	int _r = 0;
	float _nowAngle = 0;
    //Ship* _ship;
	cocos2d::Sprite* _cloud;
	
	
	//cocos2d::Sprite* _sp;
	//int _r = 0;
	//float _nowAngle = 0;
};

#endif /* defined(__TestSOS__MapScene__) */
