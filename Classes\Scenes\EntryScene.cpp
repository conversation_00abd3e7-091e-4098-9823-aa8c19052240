#include "EntryScene.h"
#include "cocostudio/CocoStudio.h"
#include "../SOSConfig.h"
#include "../Transactions/LoginTransaction.h"
#include "../Transactions/DockTransaction.h"
#include "../Transactions/FormationTransaction.h"
#include "../Transactions/PartnerTransaction.h"
#include "../GameData.h"
#include "../Scenes/CreatePersonScene.h"
#include "../LoadingScene.h"
#include "../Views/AlertView.h"
#include "../Scenes/LoadingResourceScene.h"
#include "../Services/SoundService.h"
#include "../Services/LocalizeService.h"
#include "../Transactions/ArenaTransaction.h"

USING_NS_CC;
USING_NS_CC_UI;
USING_NS_CC_CCS;

enum TagEntryScene
{
	UI1_BUTTON_ENTER = 20018
};

Scene* EntryScene::createScene()
{
	auto layer = EntryScene::create();
	auto scene = Scene::create();
	scene->addChild(layer);
	return scene;
}

EntryScene::~EntryScene()
{
	this->getEventDispatcher()->removeCustomEventListeners(EVENT_GAME_VERSION);
	this->getEventDispatcher()->removeCustomEventListeners(EVENT_GAME_VERIFY_SUCCESS);
	this->getEventDispatcher()->removeCustomEventListeners(EVENT_GAME_LOGIN_CREATE);
	this->getEventDispatcher()->removeCustomEventListeners(EVENT_GAME_LOGIN_SUCCESS);
	this->getEventDispatcher()->removeCustomEventListeners(EVENT_DOCK_GET_SUCCESS);
	this->getEventDispatcher()->removeCustomEventListeners(EVENT_FORMATION_GET_SUCCESS);
	this->getEventDispatcher()->removeCustomEventListeners(EVENT_PASSIVE_SKILLS_GET_SUCCESS);
	this->getEventDispatcher()->removeCustomEventListeners(EVENT_GAME_MISSION_QUERY);
	this->getEventDispatcher()->removeCustomEventListeners(EVENT_GAME_RANKGET_SUCCESS);

	Director::getInstance()->getTextureCache()->removeTextureForKey("UI_Star/ss_sign_bg.jpg");
	Director::getInstance()->getTextureCache()->removeTextureForKey("UI_Star/UI_Star_1.png");
	SpriteFrameCache::getInstance()->removeSpriteFramesFromFile("UI_Star/UI_Star_1.plist");
}

bool EntryScene::init()
{
	if (!BaseScene::init())
	{
		return false;
	}

	auto ui = CCS_CREATE_LAYER("UI_Star/UI_Star_1");
	this->addChild(ui);

	CCS_GET_CHILD(ui, Button, enterBtn, UI1_BUTTON_ENTER);

	enterBtn->addTouchEventListener(this, toucheventselector(EntryScene::touchButton));

	return true;
}

void EntryScene::onEnter()
{
	SoundService::getInstance()->playBGM(SOUND_CITY, true);

	auto userData = GameData::getInstance()->getUserData();

	this->getEventDispatcher()->addCustomEventListener(EVENT_GAME_VERSION, [=](EventCustom* event) {
		LoginTransaction::getInstance()->verify(GameData::getInstance()->token);
	});

	this->getEventDispatcher()->addCustomEventListener(EVENT_GAME_VERIFY_SUCCESS, [=](EventCustom* event) {
		LoginTransaction::getInstance()->login(GameData::getInstance()->getUserData()->getInfo().uid);
	});

	this->getEventDispatcher()->addCustomEventListener(EVENT_GAME_LOGIN_CREATE, [=](EventCustom* event) {
// 		UserDefault::getInstance()->setIntegerForKey("City", 14);
// 		UserDefault::getInstance()->setBoolForKey("InCity", true);
// 		UserDefault::getInstance()->setIntegerForKey("GuideSave", 1);
// 		UserDefault::getInstance()->setIntegerForKey("TargetCity", 2);
// 		UserDefault::getInstance()->setBoolForKey("Guider", true);
// 		UserDefault::getInstance()->flush();
// 
// 		auto guider = UserDefault::getInstance()->getBoolForKey("Guider");
// 		GameData::getInstance()->getUserData()->getInfo().city = UserDefault::getInstance()->getIntegerForKey("City");;
// 		GameData::getInstance()->getUserData()->getInfo().targetCity = UserDefault::getInstance()->getIntegerForKey("TargetCity");
// 		GameData::getInstance()->getUserData()->getInfo().inCity = UserDefault::getInstance()->getBoolForKey("InCity");
// 		GameData::getInstance()->getUserData()->getInfo().state = (guider ? UserData::StateType::GUIDER : 0);
// 
// 		auto guideSave = UserDefault::getInstance()->getIntegerForKey("GuideSave");
// 		GameData::getInstance()->getUserData()->getGuidance().id = guideSave;
// 		GameData::getInstance()->getUserData()->getGuidance().action = 1;

		Director::getInstance()->replaceScene(CreatePersonScene::createScene());
	});

	this->getEventDispatcher()->addCustomEventListener(EVENT_GAME_LOGIN_SUCCESS, [=](EventCustom* event) {
		userData->getInfo().login = true;
		DockTransaction::getInstance()->getShip(userData->getInfo().uid);
	});

	this->getEventDispatcher()->addCustomEventListener(EVENT_DOCK_GET_SUCCESS, [=](EventCustom* event) {
		FormationTransaction::getInstance()->Get(userData->getInfo().uid);
	});

	this->getEventDispatcher()->addCustomEventListener(EVENT_FORMATION_GET_SUCCESS, [=](EventCustom* event) {
		MissionTransaction::getInstance()->query(userData->getInfo().uid, userData->getInfo().city);
	});

	this->getEventDispatcher()->addCustomEventListener(EVENT_GAME_MISSION_QUERY, [=](EventCustom* event){
		ArenaTransaction::getInstance()->rankGet(userData->getInfo().uid, 1);
	});

	this->getEventDispatcher()->addCustomEventListener(EVENT_GAME_RANKGET_SUCCESS, [=](EventCustom* event){
		PartnerTransaction::getInstance()->getPassiveSkills(userData->getInfo().uid);
	});

	this->getEventDispatcher()->addCustomEventListener(EVENT_PASSIVE_SKILLS_GET_SUCCESS, [=](EventCustom* event) {
		auto onlyID = static_cast<std::string *>(event->getUserData());
		auto players = userData->getPlayers();
		for (auto player : players)
		{
			if (player->getInfo().id == *onlyID)
			{
				userData->getTeam()->getInfo().pid = player->getInfo().id;
				break;
			}
		}

		std::vector<std::string> loadings;
		if (userData->getInfo().inCity)
		{
			for (auto i = 1; i <= 16; i++)
			{
				loadings.push_back(String::createWithFormat("ALLUI_%d.png", i)->getCString());
			}
			Director::getInstance()->replaceScene(LoadingScene::createScene(LoadingScene::SceneType::CITY, loadings));
		}
		else
		{
			for (auto i = 1; i <= 12; i++)
			{
				loadings.push_back(String::createWithFormat("Maps/map_1_%02d.png", i)->getCString());
			}
			Director::getInstance()->replaceScene(LoadingScene::createScene(LoadingScene::SceneType::MAP, loadings));
		}
	});

	Layer::onEnter();
}

void EntryScene::touchButton(Ref* object, TouchEventType type)
{
	auto widget = dynamic_cast<Widget*>(object);

	switch (type)
	{
	case TouchEventType::TOUCH_EVENT_ENDED:
		if (widget->getTag() == UI1_BUTTON_ENTER)
		{
			this->addChild(AlertView::create(LocalizeService::getInstance()->getString("632"), AlertType::LOADING));
			LoginTransaction::getInstance()->getVersion(UserDefault::getInstance()->getStringForKey("Version"));
		}
		break;
	default:
		break;
	}
}