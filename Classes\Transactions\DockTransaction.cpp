﻿#include "DockTransaction.h"
#include "../Services/HTTPService.h"
#include "../GameData.h"

USING_NS_CC;
USING_STD;

static const string TRANS_GAME_SHIP_TRANSACTION = "Game_Ship_Transaction.ashx";
static const string TRANS_GAME_MYSHIP_GET = "Game_MyShip_Get.ashx";

FUNC_GET_INSTANCE(DockTransaction);

DockTransaction::DockTransaction()
{
	Director::getInstance()->getEventDispatcher()->addCustomEventListener(TRANS_GAME_SHIP_TRANSACTION, [=](EventCustom* event) {
		auto data = static_cast<HTTPData *>(event->getUserData());
		std::string shipGuid = data->readString("ShipGuid");
		Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_DOCK_TRANSACTION_SUCCESS, &shipGuid);
	});

	Director::getInstance()->getEventDispatcher()->addCustomEventListener(TRANS_GAME_MYSHIP_GET, [=](EventCustom* event) {
		auto data = static_cast<HTTPData *>(event->getUserData());

		auto user = GameData::getInstance()->getUserData();
		user->getShips().clear();

		auto shipList = data->readDatas("List");
		for (auto shipData : shipList)
		{
			auto ship = GameData::getInstance()->getShip(shipData->readInt("ShipID"));
			ship->getInfo().id = shipData->readString("ShipGuid");
			ship->getInfo().use = shipData->readInt("IsUse");

			ship->getCabins().clear();
			auto cabinList = shipData->readDatas("CabinList");
			for (auto cabinData : cabinList)
			{
				auto mid = cabinData->readInt("CabinID");

				if (mid <= 0) continue;

				auto cabin = GameData::getInstance()->getCabin(mid);
				cabin->getInfo().appoint = cabinData->readString("CabinAppoint");
				ship->getCabins().pushBack(cabin);
			}

			user->getShips().insert(ship->getInfo().mid, ship);
		}

		Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_DOCK_GET_SUCCESS, NULL);
	});
}

DockTransaction::~DockTransaction()
{
	Director::getInstance()->getEventDispatcher()->removeCustomEventListeners(TRANS_GAME_SHIP_TRANSACTION);
	Director::getInstance()->getEventDispatcher()->removeCustomEventListeners(TRANS_GAME_MYSHIP_GET);
}

void DockTransaction::transcate(std::string UID,int ShipID,int Type,std::string ShipGuid,int CityID)
{
	auto data = HTTPData::create();
	data->write("UID",UID.c_str());
	data->write("ShipID",ShipID);
	data->write("Type",Type);
	data->write("ShipGuid",ShipGuid.c_str());
	data->write("CityID",CityID);
	auto url = WEB_HOST + TRANS_GAME_SHIP_TRANSACTION;
	HTTPService::getInstance()->request(url.c_str(), data,TRANS_GAME_SHIP_TRANSACTION.c_str());
}

void DockTransaction::getShip(std::string UID)
{
	auto data = HTTPData::create();
	data->write("UID",UID.c_str());
	auto url = WEB_HOST + TRANS_GAME_MYSHIP_GET;
	HTTPService::getInstance()->request(url.c_str(), data,TRANS_GAME_MYSHIP_GET.c_str());
}
