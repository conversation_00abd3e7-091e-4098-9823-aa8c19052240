#ifndef __REGISTER_VIEW_H__
#define __REGISTER_VIEW_H__

#include "cocos2d.h"
#include "ui/CocosGUI.h"
#include "cocostudio/CocoStudio.h"
#include "../Views/PopupView.h"
#include "../SOSConfig.h"

class RegisterView :public PopupView
{
public:
	CREATE_FUNC(RegisterView);
public:
	RegisterView();
	virtual ~RegisterView();
	virtual bool init() override;
	virtual void onEnter() override;
	void touchButton(cocos2d::Ref* object, cocos2d::ui::TouchEventType type);
private:
	cocos2d::ui::Widget* ui;
};

#endif 