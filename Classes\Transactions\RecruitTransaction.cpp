#include "RecruitTransaction.h"
#include "../Services/HTTPService.h"
#include "../GameData.h"

USING_NS_CC;
USING_STD;

static const string TRANS_GAME_RECRUIT_QUERY = "Game_Recruit_Query.ashx";
static const string TRANS_GAME_RECRUIT_EXECUTE = "Game_Recruit_Execute.ashx";

FUNC_GET_INSTANCE(RecruitTransaction);

RecruitTransaction::RecruitTransaction()
{
	Director::getInstance()->getEventDispatcher()->addCustomEventListener(TRANS_GAME_RECRUIT_QUERY, [=](EventCustom* event) {
		auto data = static_cast<HTTPData *>(event->getUserData());
		auto list = data->readDatas("List");
		PlayerData* playerData;
        for (auto element : list)
        {
// 			playerData = PlayerData::create(std::atoi(element->readString("RoleID")));
// 
// 			PlayerData::Info& playerInfo = playerData->getInfo();
//             playerInfo.isUse = false;
// 			playerInfo.appoint = PlayerData::AppointType::IDLE;
// 			playerInfo.playType = PlayerData::PlayType::PLAYING;
// 			playerInfo.exp = 0;
// 			playerData->setLevel(1);

			auto mid = INT(element->readString("RoleID"));
			playerData = PlayerData::create(mid);

			PlayerData::Info playerInfo = GameData::getInstance()->getPlayerInfo(mid);
			playerInfo.isUse = false;
			playerInfo.appoint = PlayerData::AppointType::IDLE;
			playerInfo.playType = PlayerData::PlayType::PLAYING;
			playerInfo.exp = 0;
			playerData->setInfo(playerInfo);

			PlayerData::Attribute playerAttri = GameData::getInstance()->getPlayerAttribute(mid);
			playerData->setAttribute(playerAttri);

			playerData->setLevel(1);

			auto defaultSkillData = GameData::getInstance()->getSkill(playerInfo.defaultSkill);
			playerData->setDefaultSkill(defaultSkillData);

            auto skillID = element->readInt("MagicID");
            playerData->setSkill(GameData::getInstance()->getSkill(skillID));

			auto passiveSkillsID = element->readInt("PassiveSkillsID");
			if (passiveSkillsID > 0)
			{
				playerData->setTeamSkill(GameData::getInstance()->getSkill(passiveSkillsID));
			}
		}
		Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_RECRUIT_QUERY_SUCCESS, playerData);
	});

	Director::getInstance()->getEventDispatcher()->addCustomEventListener(TRANS_GAME_RECRUIT_EXECUTE, [=](EventCustom* event) {
		auto data = static_cast<HTTPData *>(event->getUserData());
		std::string onlyID = data->readString("OnlyID");
		//GameData::getInstance()->isTaskComplete(GameData::MissionType::RANDOMPLAYER, 0, 0);
		Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_RECRUIT_EXECUTE_SUCCESS, &onlyID);
	});
}

RecruitTransaction::~RecruitTransaction()
{
	Director::getInstance()->getEventDispatcher()->removeCustomEventListeners(TRANS_GAME_RECRUIT_QUERY);
	Director::getInstance()->getEventDispatcher()->removeCustomEventListeners(TRANS_GAME_RECRUIT_EXECUTE);
}

void RecruitTransaction::Query(std::string uID, int cityID, int submitType, int costType)
{
	auto data = HTTPData::create();
	data->write("UID", uID.c_str());
	data->write("CityID", cityID);
	data->write("SubmitType", submitType);
	data->write("CostType", costType);
	auto url = WEB_HOST + TRANS_GAME_RECRUIT_QUERY;
	HTTPService::getInstance()->request(url.c_str(), data,TRANS_GAME_RECRUIT_QUERY.c_str());

}


void RecruitTransaction::Execute(std::string uID, int roleID, int cityID)
{
	auto data= HTTPData::create();
	data->write("UID", uID.c_str());
	data->write("CityID", cityID);
	data->write("RoleID", roleID);

	auto url = WEB_HOST + TRANS_GAME_RECRUIT_EXECUTE;
	HTTPService::getInstance()->request(url.c_str(),data,TRANS_GAME_RECRUIT_EXECUTE.c_str());


}
