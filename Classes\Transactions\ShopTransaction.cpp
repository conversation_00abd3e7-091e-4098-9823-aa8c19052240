#include "ShopTransaction.h"
#include "../Services/HTTPService.h"
#include "../GameData.h"

USING_NS_CC;
USING_STD;

static const string TRANS_GAME_ENTER_SHOP = "Game_Enter_Shop.ashx";
static const string TRANS_GAME_SHOP_BUY = "Game_Shop_Buy.ashx";
static const string TRANS_GAME_SHOP_SELL = "Game_Shop_Sell.ashx";
static const string TRANS_GAME_SHOP_SELL_ALL = "Game_Shop_Sell_All.ashx";
static const string TRANS_GAME_WEAPON_BUY = "Game_Smithy_Buy.ashx";
static const string TRANS_GAME_WEAPON_SELL = "Game_Smithy_Sell.ashx";


FUNC_GET_INSTANCE(ShopTransaction);

ShopTransaction::ShopTransaction()
{
	Director::getInstance()->getEventDispatcher()->addCustomEventListener(TRANS_GAME_ENTER_SHOP, [=](EventCustom* event) {
		CCLOG("[TRANS] response: enter shop ...");

		auto data = static_cast<HTTPData *>(event->getUserData());

		std::vector<UserData::Cargo> cargos;

		auto shopItemList = data->readDatas("ShopItemList");
		for (auto shopItem : shopItemList)
		{
			auto mid = shopItem->readInt("ID");
			auto cargo = GameData::getInstance()->getCargo(mid);
			cargo.count = 1;
			cargo.buy = shopItem->readInt("BuyMoney");
			cargo.sell = shopItem->readInt("SellMoney");

			cargos.push_back(cargo);
		}
		Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_ENTER_SHOP_SUCCESS, &cargos);
	});

	Director::getInstance()->getEventDispatcher()->addCustomEventListener(TRANS_GAME_WEAPON_BUY, [=](EventCustom* event) {
		CCLOG("[TRANS] response: enter shop ...");

		auto data = static_cast<HTTPData *>(event->getUserData());
		auto id = data->readString("PropID");
		
		Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_WEAPON_BUY_SUCCESS, &id);
	});
}

ShopTransaction::~ShopTransaction()
{
	Director::getInstance()->getEventDispatcher()->removeCustomEventListeners(TRANS_GAME_ENTER_SHOP);
	Director::getInstance()->getEventDispatcher()->removeCustomEventListeners(TRANS_GAME_WEAPON_BUY);
}

void ShopTransaction::enter(int city)
{
	auto data = HTTPData::create();
	data->write("CityID", city);
	data->write("ShopID", city);

	auto url = WEB_HOST + TRANS_GAME_ENTER_SHOP;

	HTTPService::getInstance()->request(url.c_str(), data, TRANS_GAME_ENTER_SHOP.c_str());
}

void ShopTransaction::buy(int city, int mid, int count)
{
	auto data = HTTPData::create();
	data->write("CityID", city);
	data->write("ItemID", mid);
	data->write("ItemCount", count);

	auto url = WEB_HOST + TRANS_GAME_SHOP_BUY;

	HTTPService::getInstance()->request(url.c_str(), data, TRANS_GAME_SHOP_BUY.c_str());
}

void ShopTransaction::sell(int city, int mid, int count)
{
	auto data = HTTPData::create();
	data->write("CityID", city);
	data->write("ItemID", mid);
	data->write("ItemCount", count);

	auto url = WEB_HOST + TRANS_GAME_SHOP_SELL;

	HTTPService::getInstance()->request(url.c_str(), data, TRANS_GAME_SHOP_SELL.c_str());
}

void ShopTransaction::sell(int city)
{
	auto data = HTTPData::create();
	data->write("CityID", city);

	auto url = WEB_HOST + TRANS_GAME_SHOP_SELL_ALL;

	HTTPService::getInstance()->request(url.c_str(), data, TRANS_GAME_SHOP_SELL_ALL.c_str());
}

void ShopTransaction::smithyBuy(std::string userID,int cityID, int mid)
{
	auto data = HTTPData::create();
	data->write("UserID", userID.c_str());
	data->write("CityID", cityID);
	data->write("MID", mid);
	data->write("Count", 1);

	auto url = WEB_HOST + TRANS_GAME_WEAPON_BUY;

	HTTPService::getInstance()->request(url.c_str(), data, TRANS_GAME_WEAPON_BUY.c_str());
}

void ShopTransaction::smithySell(std::string userID, std::string guid)
{
	auto data = HTTPData::create();
	data->write("UserID", userID.c_str());
	data->write("PropID", guid.c_str());

	auto url = WEB_HOST + TRANS_GAME_WEAPON_SELL;

	HTTPService::getInstance()->request(url.c_str(), data, TRANS_GAME_WEAPON_SELL.c_str());
}