#ifndef __RECRUIT_VIEW_H__
#define __RECRUIT_VIEW_H__

#include "cocos2d.h"
#include "ui/CocosGUI.h"
#include "cocostudio/CocoStudio.h"
#include "../SOSConfig.h"
#include "../GameData.h"
#include "../Transactions/RecruitTransaction.h"
#include "PopupView.h"

static const char* EVENT_PUBSCENE_CHANGED = "event_pubscene_changed";
static const char* EVENT_RECRUIT_CLOSE = "event_recruit_close";

class RecruitView : public PopupView
{
public:
	CREATE_FUNC(RecruitView);

	static cocos2d::Layer* createScene();
	static cocos2d::Layer* createScene(PlayerData* data , int t);

public:
	RecruitView();
	virtual ~RecruitView();
	
	virtual bool init() override;
	virtual void onEnter() override;

	void menuCloseCallback(cocos2d::Ref* pSender);

	void touchButton(cocos2d::Ref* object, cocos2d::ui::TouchEventType type);

	void LoadUI();
	void LoadUI3();

};

#endif // __RECRUIT_VIEW_H__