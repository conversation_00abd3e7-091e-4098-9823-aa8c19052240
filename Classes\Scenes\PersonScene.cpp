#include "PersonScene.h"
#include "PartnerScene.h"
#include "../Transactions/PersonTransaction.h"
#include "../Services/LocalizeService.h"
#include "../Services/NewguidanceService.h"
#include "../Views/ItemTipsView.h"
#include "../Views/AlertView.h"
#include "../Views/WeaponView.h"
#include "../Services/SoundService.h"
#include "../ShaderSprite.h"
#include "../Item.h"

USING_NS_CC;
USING_NS_CC_CCS;
USING_NS_CC_UI;

enum tagPerson
{
	UI_PERSONVIEW_1 = 10005,
	UI_PERSONVIEW_2 = 10007,
	UI_PERSONVIEW_3 = 10006,
	UI_BUTTON_BAG = 107,
	UI_BUTTON_PROPERTY = 108,
	UI_BUTTON_UP = 109,
	UI_BUTTON_SKILL = 110,
	UI_BUTTON_BACK = 112,
	UI_LAYOUT_PANEL = 131,
	UI_TEXT_NAME = 132,
	UI_TEXTBMFONT_LEVEL = 133,
	UI_TEXT_FIGHT = 134,
	UI_IMAGEVIEW_STAR = 11000,
	UI_IMAGEVIEW_JOB = 37005,

	UI5_BUTTON_CHARGE = 162,
	UI5_PAGEVIEW_CELLS = 765,

	UI4_TEXT_LEVEL = 139,
	UI4_TEXT_EXP = 140,
	UI4_LOADINGBAR_EXP = 142,
	UI4_TEXTBMFONT_JOB = 143,
	UI4_TEXT_FORTUNE = 144,
	UI4_TEXT_POWER = 145,
	UI4_TEXT_FIGHT = 146,
	UI4_TEXT_HP = 152,
	UI4_TEXT_STR = 153,
	UI4_TEXT_INC = 156,
	UI4_TEXT_ATK = 154,
	UI4_TEXT_DEF = 155,
	UI4_TEXT_MAG = 157,
	UI4_TEXT_RES = 158,
	UI4_TEXT_CAPTAINVALUE = 159,
	UI4_TEXT_NAVIGATEVALUE = 160,
	UI4_TEXT_OPERATEVALUE = 161,

	UI6_IMAGEVIEW_SKILLICON = 228,
	UI6_TEXT_SKILLNAME = 229,
	UI6_TEXT_LEVEL = 230,
	UI6_TEXT_FFECTVALUE = 231,
	UI6_TEXT_EXTENTYPE = 232,
	UI6_TEXT_NEXTFFECTVALUE = 233,
	UI6_TEXT_NEXTLEVEL = 234,
	UI6_IMAGEVIEW_COIN = 235,
	UI6_TEXT_MONEY = 236,
	UI6_BUTTON_SKILLUP = 237,
	UI6_TEXT_PASSIVESKILLDES = 238,
	UI6_TEXT_DIAMOND = 241,
	UI6_BUTTON_REFRESH = 242,
	UI6_TEXT_XIAOHAO = 239,
	UI6_IMAGEVIEW_DIAMOND = 240,
	UI6_TEXT_CDTIME = 24001,
	UI6_BUTTON_SPEEDUP = 33001,

	UI7_TEXT_STR1 = 244,
	UI7_TEXT_STR2 = 245,
	UI7_TEXT_STR3 = 246,
	UI7_TEXT_INT1 = 247,
	UI7_TEXT_INT2 = 248,
	UI7_TEXT_INT3 = 249,
	UI7_BUTTON_COMMON = 250,
	UI7_BUTTON_STRENGTHEN = 251,
	UI7_BUTTON_CANCEL = 254,
	UI7_BUTTON_SAVE = 255,
	UI7_TEXT_COMMONPRICE = 252,
	UI7_TEXT_STRENGTHENPRICE = 253,

	SCENE_BAG_NODE_CELL = 10004,
	SCENE_NODE_EQUIP = 30001,

	UI_PERSONVIEW_3_IMAGEIVEW = 135,
	UI_PERSONVIEW_3_ICON = 136,
	UI_PERSONVIEW_3_BG = 39500,

	UI_OTHER_7_BUTTON_BG = 651,
	UI_OTHER_7_TEXT_COUNT = 653,
	//UI_OTHER_7_IMAGE_ICON = 652,
	//UI_OTHER_7_IMAGE_BG = 37000,
	UI_OTHER_7_ITEM = 987987,

	UI_ITEM_TIPS = 99999,
};

static PlayerData* s_playerData = nullptr;

std::vector<Sprite *> r_bags;
std::vector<Node *> r_cellNodes;
bool r_onBag;
int r_bagIndex;
int r_skillDiamond;

PersonScene::PersonScene()
: _touchTarget(nullptr)
, _movedTarget(nullptr)
, _equipTarget(nullptr)
{

}

PersonScene::~PersonScene()
{
	this->getEventDispatcher()->removeCustomEventListeners(EVENT_PASSIVESKILL_REFRESH);
	this->getEventDispatcher()->removeCustomEventListeners(EVENT_CULTIVATE_EXECUTE_SUCCESS);
	this->getEventDispatcher()->removeCustomEventListeners(EVENT_EQUIPS_CHANGED);
	this->getEventDispatcher()->removeCustomEventListeners(EVENT_USE_ITEM);
	this->getEventDispatcher()->removeCustomEventListeners(EVENT_BUTTON_YES);
	this->getEventDispatcher()->removeCustomEventListeners(EVENT_UNLOAD_ITEM);

	_bagLayer->release();
	_propertyLayer->release();
	_trainLayer->release();
	_skillLayer->release();

	r_bags.clear();
	r_cellNodes.clear();
	r_onBag = false;
	r_bagIndex = 0;
	r_skillDiamond = 0;
	s_playerData = nullptr;
	this->unschedule(schedule_selector(PersonScene::update));
}

Scene* PersonScene::createScene()
{
	// 'scene' is an autorelease object
	auto scene = Scene::create();

	// 'layer' is an autorelease object
	auto layer = PersonScene::create();

	// add layer as a child to scene
	scene->addChild(layer);

	// return the scene
	return scene;
}

Scene* PersonScene::createScene(PlayerData* playerInfo)
{
	s_playerData = playerInfo;

	// 'scene' is an autorelease object
	auto scene = Scene::create();

	// 'layer' is an autorelease object
	auto layer = PersonScene::create();

	// add layer as a child to scene
	scene->addChild(layer);

	// return the scene
	return scene;
}

bool PersonScene::init()
{
	if (!BaseScene::init())
	{
		return false;
	}

	node = CCS_CREATE_SCENE("Scene_PersonView");
	node->setTag(TagBaseScene::LAYER);
	this->addChild(node);

	CCS_GET_COMPONENT_FROM_SCENE(node, Layer, closeLayer, 11000);
	CCS_GET_CHILD(closeLayer, Button, closeBtn, 298);
	closeBtn->addTouchEventListener(this, toucheventselector(PersonScene::touchButton));

	this->scheduleUpdate();

	// 	auto kuang =Sprite::create("ss_ty_kuang1.png");
	// 	kuang->setAnchorPoint(Point(0,0));
	// 	node->addChild(kuang, 2);

	//_bagLayer = SceneReader::getInstance()->createNodeWithSceneFile("Scene_Bag.json");
	_bagLayer = CCS_CREATE_LAYER("UI_PersonView_5");
	_bagLayer->setTag(TagBaseScene::PERSON_BAG_VIEW);
	_bagLayer->retain();
	CCS_GET_CHILD(_bagLayer, PageView, pageView, UI5_PAGEVIEW_CELLS);
	pageView->addEventListenerPageView(this, pagevieweventselector(PersonScene::touchPageView));

	_propertyLayer = CCS_CREATE_LAYER("UI_PersonView_4");
	_propertyLayer->setTag(TagBaseScene::PERSON_BAG_VIEW);
	_propertyLayer->retain();
	this->addChild(_propertyLayer, 1);
	_trainLayer = CCS_CREATE_LAYER("UI_PersonView_7");
	_trainLayer->setTag(TagBaseScene::PERSON_BAG_VIEW);
	_trainLayer->retain();
	_skillLayer = CCS_CREATE_LAYER("UI_PersonView_6");
	_skillLayer->setTag(TagBaseScene::PERSON_SKILL_VIEW);
	_skillLayer->retain();

	initEquipments();

	auto playerInfo = s_playerData->getInfo();

	CCS_GET_COMPONENT_FROM_SCENE(node, Layer, layer1, UI_PERSONVIEW_1);
	CCS_GET_CHILD(layer1, CheckBox, bagBtn, UI_BUTTON_BAG);
	CCS_GET_CHILD(layer1, CheckBox, propertyBtn, UI_BUTTON_PROPERTY);
	CCS_GET_CHILD(layer1, CheckBox, upBtn, UI_BUTTON_UP);
	CCS_GET_CHILD(layer1, CheckBox, skillBtn, UI_BUTTON_SKILL);
	bagBtn->addTouchEventListener(this, toucheventselector(PersonScene::touchButton));
	propertyBtn->addTouchEventListener(this, toucheventselector(PersonScene::touchButton));
	upBtn->addTouchEventListener(this, toucheventselector(PersonScene::touchButton));
	skillBtn->addTouchEventListener(this, toucheventselector(PersonScene::touchButton));
	propertyBtn->setSelectedState(true);
	propertyBtn->setTouchEnabled(false);

	CCS_GET_COMPONENT_FROM_SCENE(node, Layer, layer, UI_PERSONVIEW_2);
	//CCS_GET_CHILD(layer, Button, backBtn, UI_BUTTON_BACK);
	CCS_GET_CHILD(layer, ImageView, layout, UI_LAYOUT_PANEL);
	CCS_GET_CHILD(layer, Text, name, UI_TEXT_NAME);
	CCS_GET_CHILD(layer, TextBMFont, level, UI_TEXTBMFONT_LEVEL);
	CCS_GET_CHILD(layer, Text, fight, UI_TEXT_FIGHT);
	CCS_GET_CHILD(layer, ImageView, star, UI_IMAGEVIEW_STAR);
	CCS_GET_CHILD(layer, ImageView, job, UI_IMAGEVIEW_JOB);

	//backBtn->addTouchEventListener(this, toucheventselector(PersonScene::touchButton));
	layout->loadTexture(GET_PLAYER_IDLE(playerInfo.type, playerInfo.appraisal), TextureResType::PLIST);
	name->setColor(getAppraisalColor(playerInfo.appraisal));
	name->setText(playerInfo.name);
	level->setText(("Lv." + STRING(playerInfo.level)).c_str());
	fight->setText(LocalizeService::getInstance()->getString(PLAYER_JOB_NAMES[0]) + STRING(s_playerData->getFP()));
	if (playerInfo.appraisal == 6)
		star->loadTexture("ss_partner_character_6.png", TextureResType::PLIST);
	else
		star->loadTexture(GET_PLAYER_STAR(playerInfo.appraisal), TextureResType::PLIST);
	job->loadTexture(GET_PLAYER_JOB(playerInfo.job), TextureResType::PLIST);

	// 	Armature* arm = Armature::create(GET_PLAYER_NAME(playerInfo.type, playerInfo.appraisal < 4 ? 1 : playerInfo.appraisal));
	// 	arm->getAnimation()->play(PlayerAction::IDLE);  
	// 	arm->getAnimation()->setSpeedScale(1); 
	// 	arm->setScale(1.2f);
	//	layout->addNode(arm);

	//CCS_GET_COMPONENT_FROM_SCENE(node, Layer, propertyLayer, UI_PERSONVIEW_3);
	initPropertyLayer(_propertyLayer);

	auto userData = GameData::getInstance()->getUserData();
	for (auto item : userData->getQuickens())
	{
		if (item->getInfo().type == QuickenData::QuickType::PLAYER)
		{
			_cd = item->getInfo().totalTime * 60;
			break;
		}
	}

	Texture2D::setDefaultAlphaPixelFormat(Texture2D::PixelFormat::RGB888);
	auto imgBg = Sprite::create("ss_ty_bg1.png");
	auto size = Director::getInstance()->getWinSize();
	imgBg->setScale(size.width / 960, size.height / 640);
	imgBg->setAnchorPoint(Point(0, 0));
	this->addChild(imgBg, -1);
	Texture2D::setDefaultAlphaPixelFormat(Texture2D::PixelFormat::DEFAULT);

	return true;
}

void PersonScene::onEnter()
{
	this->getEventDispatcher()->addCustomEventListener(EVENT_UNLOAD_ITEM, [=](EventCustom* event) {

		auto item = static_cast<ItemData*>(event->getUserData());
		if (unequipItem(item))
		{
			this->removeChildByTag(TagBaseScene::PERSON_BAG_EQUIP_VIEW);
		}
	});

	this->getEventDispatcher()->addCustomEventListener(EVENT_BUTTON_YES, [=](EventCustom* event){
		if (r_skillDiamond > 0)
		{
			auto userData = GameData::getInstance()->getUserData();
			if (userData->getInfo().diamond >= r_skillDiamond)
			{
				PartnerTransaction::getInstance()->refreshCDTime(GameData::CDTimeType::CD_SKILL);
				userData->getInfo().diamond -= r_skillDiamond;
				GameData::getInstance()->skillTime = 0;

				Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_NEWGUIDANCE_START);
			}
			else
			{
				this->addChild(AlertView::create(LocalizeService::getInstance()->getString("645"), AlertType::IDLE), 10000, 99999);
			}
			r_skillDiamond = 0;
		}
	});
	this->getEventDispatcher()->addCustomEventListener(EVENT_CULTIVATE_EXECUTE_SUCCESS, [=](EventCustom* event) {
		this->removeChildByTag(99999);

		auto i = static_cast<std::vector<int>*>(event->getUserData());
		s_playerData->getCultivate().STR = i->at(0);
		s_playerData->getCultivate().INC = i->at(1);

		auto str3 = dynamic_cast<TextBMFont*>(_trainLayer->getChildByTag(UI7_TEXT_STR3));
		std::string s1 = "+" + STRING(s_playerData->getCultivate().STR);
		str3->setText(s1.c_str());
		str3->setVisible(true);
		auto int3 = dynamic_cast<TextBMFont*>(_trainLayer->getChildByTag(UI7_TEXT_INT3));
		std::string s2 = "+" + STRING(s_playerData->getCultivate().INC);
		int3->setText(s2.c_str());
		int3->setVisible(true);
	});

	this->getEventDispatcher()->addCustomEventListener(EVENT_PASSIVESKILL_REFRESH, [=](EventCustom* event) {
		auto skillID = static_cast<int*>(event->getUserData());
		//auto userData = GameData::getInstance()->getUserData();
		s_playerData->setTeamSkill(GameData::getInstance()->getSkill(*skillID));
		// 		UserData::Info& userInfo = userData->getInfo();
		// 		PlayerVector& players = userData->getPlayers();
		// 		for (auto player : players)
		// 		{
		// 			if(s_playerData->getInfo().id == player->getInfo().id)
		// 			{
		// 
		// 				//player->setTeamSkill(GameData::getInstance()->getSkill(*skillID));
		// 				player->setTeamSkill(SkillData::create(*skillID));
		// 				s_playerData->setTeamSkill(SkillData::create(*skillID));
		// 				break;
		// 			}
		// 		}

		initSkillLayer(_skillLayer);
	});

	this->getEventDispatcher()->addCustomEventListener(EVENT_EQUIPS_CHANGED, [=](EventCustom* event){
		CCS_GET_COMPONENT_FROM_SCENE(node, Layer, layer, UI_PERSONVIEW_2);
		CCS_GET_CHILD(layer, Text, fight, UI_TEXT_FIGHT);
		fight->setText(LocalizeService::getInstance()->getString(PLAYER_JOB_NAMES[0]) + STRING(s_playerData->getFP()));

		GameData::getInstance()->isTaskComplete(GameData::MissionType::USEITEM, 0, 0);
	});

	this->getEventDispatcher()->addCustomEventListener(EVENT_USE_ITEM, [=](EventCustom* event) {

		auto item = static_cast<ItemData*>(event->getUserData());
		if (equipItem(item))
		{
			this->removeChildByTag(TagBaseScene::PERSON_BAG_EQUIP_VIEW);
			Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_NEWGUIDANCE_START);
		}
	});

// 	if (GameData::getInstance()->getUserData()->getInfo().state == UserData::StateType::GUIDER)
// 	{
// 		NewguidanceService::getInstance()->createLayer(this);
// 	}

	BaseScene::onEnter();
}

void PersonScene::menuCloseCallback(Ref* pSender)
{
	Director::getInstance()->end();

#if (CC_TARGET_PLATFORM == CC_PLATFORM_IOS)
	exit(0);
#endif
}

void PersonScene::touchButton(Ref* obj, cocos2d::ui::TouchEventType eventType)
{
	auto userData = GameData::getInstance()->getUserData();
	auto playerInfo = s_playerData->getInfo();
	auto button = dynamic_cast<Widget *>(obj);
	int tag = button->getTag();

	auto child = node->getChildByTag(UI_PERSONVIEW_1);
	auto reader = (ComRender*)child->getComponent("GUIComponent");
	auto layer = (Layer*)reader->getNode();
	auto bagBtn = dynamic_cast<CheckBox*>(layer->getChildByTag(UI_BUTTON_BAG));
	auto propertyBtn = dynamic_cast<CheckBox*>(layer->getChildByTag(UI_BUTTON_PROPERTY));
	auto upBtn = dynamic_cast<CheckBox*>(layer->getChildByTag(UI_BUTTON_UP));
	auto skillBtn = dynamic_cast<CheckBox*>(layer->getChildByTag(UI_BUTTON_SKILL));

	// 	if (GameData::getInstance()->isCreate)
	// 		this->addChild(NewguidanceService::getInstance()->createLayer(), this->getChildrenCount() + 3);

	switch (eventType)
	{
	case TouchEventType::TOUCH_EVENT_BEGAN:
		break;
	case TouchEventType::TOUCH_EVENT_ENDED:
		if (tag == UI_OTHER_7_BUTTON_BG)
		{
			auto equip = dynamic_cast<ItemData*>(button->getUserObject());
			if (equip != nullptr)
			{
				if (equip->getInfo().type == ItemData::ItemType::EQUIP)
					this->addChild(ItemTipsView::create(equip, s_playerData), 999999, TagBaseScene::PERSON_BAG_EQUIP_VIEW);
				else
					this->addChild(WeaponView::create(equip, WeaponView::Type::IDLE), 999999, TagBaseScene::PERSON_BAG_EQUIP_VIEW);
			}
		}
		else if (tag == UI_PERSONVIEW_3_ICON)
		{
			auto equip = dynamic_cast<ItemData*>(button->getUserObject());
			if (equip != nullptr)
			{
				this->addChild(WeaponView::create(equip, WeaponView::Type::UNLOAD), 999999, TagBaseScene::PERSON_BAG_EQUIP_VIEW);
			}
		}
		else if (tag == 298)
		{
			PersonTransaction::getInstance()->updateEquips(userData->getInfo().uid, s_playerData);
			Director::getInstance()->replaceScene(PartnerScene::createScene());
		}
		else if (tag == UI6_BUTTON_REFRESH)
		{
			if (userData->getInfo().diamond >= _refresh){
				PersonTransaction::getInstance()->refreshPassiveSkill(userData->getInfo().uid, playerInfo.id, 2);
				userData->getInfo().diamond -= _refresh;
			}
			else
			{
				this->addChild(AlertView::create(LocalizeService::getInstance()->getString("4009"), AlertType::IDLE), 999999);
			}
		}
		else if (tag == UI7_BUTTON_COMMON || tag == UI7_BUTTON_STRENGTHEN || tag == UI7_BUTTON_CANCEL || tag == UI7_BUTTON_SAVE)
		{

			auto cultivate1 = GameData::getInstance()->getCultivate(GameData::CultivateType::NORMAL);
			auto cultivate2 = GameData::getInstance()->getCultivate(GameData::CultivateType::STRENGTHEN);

			if (tag == UI7_BUTTON_COMMON || tag == UI7_BUTTON_STRENGTHEN)
			{
				if (tag == UI7_BUTTON_COMMON)
				{
					if (cultivate1.getMoney(playerInfo.level) <= userData->getInfo().money)
					{
						CCS_GET_CHILD(_trainLayer, Button, saveBtn, UI7_BUTTON_SAVE);
						saveBtn->setVisible(true);
						userData->getInfo().money -= cultivate1.getMoney(playerInfo.level);
						PersonTransaction::getInstance()->executeCultivate(userData->getInfo().uid, playerInfo.id, 1);
						this->addChild(AlertView::create(LocalizeService::getInstance()->getString("632"), AlertType::LOADING), 1000, 99999);

					}
					else
					{
						this->addChild(AlertView::create(LocalizeService::getInstance()->getString("4010"), AlertType::IDLE), 999999);
					}
				}
				else
				{
					if (cultivate2.diamond <= userData->getInfo().diamond)
					{
						CCS_GET_CHILD(_trainLayer, Button, saveBtn, UI7_BUTTON_SAVE);
						saveBtn->setVisible(true);
						userData->getInfo().diamond -= cultivate2.diamond;
						PersonTransaction::getInstance()->executeCultivate(userData->getInfo().uid, playerInfo.id, 2);
						this->addChild(AlertView::create(LocalizeService::getInstance()->getString("632"), AlertType::LOADING), 1000, 99999);

					}
					else
					{
						this->addChild(AlertView::create(LocalizeService::getInstance()->getString("4009"), AlertType::IDLE), 999999);
					}
				}
			}
			else if (tag == UI7_BUTTON_SAVE)
			{
				CCS_GET_CHILD(_trainLayer, Button, saveBtn, UI7_BUTTON_SAVE);
				saveBtn->setVisible(false);
				if (s_playerData->getCultivate().STR != 0 && s_playerData->getCultivate().INC != 0)
				{
					PersonTransaction::getInstance()->saveCultivate(userData->getInfo().uid, playerInfo.id, 1);
				}

				s_playerData->getAttribute().STR_U = s_playerData->getCultivate().STR;
				s_playerData->getAttribute().INC_U = s_playerData->getCultivate().INC;

				auto str2 = dynamic_cast<Text*>(_trainLayer->getChildByTag(UI7_TEXT_STR2));
				if (s_playerData->getCultivate().STR != 0)
					str2->setText("+" + STRING(s_playerData->getCultivate().STR));
				auto str3 = dynamic_cast<TextBMFont*>(_trainLayer->getChildByTag(UI7_TEXT_STR3));
				str3->setVisible(false);
				auto int2 = dynamic_cast<Text*>(_trainLayer->getChildByTag(UI7_TEXT_INT2));
				if (s_playerData->getCultivate().INC != 0)
					int2->setText("+" + STRING(s_playerData->getCultivate().INC));
				auto int3 = dynamic_cast<TextBMFont*>(_trainLayer->getChildByTag(UI7_TEXT_INT3));
				int3->setVisible(false);
			}
			else
			{
				this->removeFromParent();
			}
		}
		else if (tag == UI_BUTTON_BAG || tag == UI_BUTTON_PROPERTY || tag == UI_BUTTON_UP || tag == UI_BUTTON_SKILL)
		{
			r_onBag = false;

			this->removeChild(_bagLayer);
			this->removeChild(_propertyLayer);
			this->removeChild(_trainLayer);
			this->removeChild(_skillLayer);

			for (auto sprite : r_bags)
			{
				this->removeChild(sprite);
			}
			r_bags.clear();

			if (tag == UI_BUTTON_BAG)
			{
				this->addChild(_bagLayer, 1);
				bagBtn->setTouchEnabled(false);
				propertyBtn->setTouchEnabled(true);
				upBtn->setTouchEnabled(true);
				skillBtn->setTouchEnabled(true);
				propertyBtn->setSelectedState(false);
				upBtn->setSelectedState(false);
				skillBtn->setSelectedState(false);
				initBagLayer(_bagLayer);

				this->scheduleOnce(schedule_selector(BaseScene::onEventGuidance), 1.0f / 60);

				//Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_NEWGUIDANCE_START);
			}
			else if (tag == UI_BUTTON_PROPERTY)
			{
				this->addChild(_propertyLayer, 1);
				bagBtn->setTouchEnabled(true);
				propertyBtn->setTouchEnabled(false);
				upBtn->setTouchEnabled(true);
				skillBtn->setTouchEnabled(true);
				bagBtn->setSelectedState(false);
				upBtn->setSelectedState(false);
				skillBtn->setSelectedState(false);
				initPropertyLayer(_propertyLayer);
			}
			else if (tag == UI_BUTTON_UP)
			{
				this->addChild(_trainLayer, 1);
				bagBtn->setTouchEnabled(true);
				propertyBtn->setTouchEnabled(true);
				upBtn->setTouchEnabled(false);
				skillBtn->setTouchEnabled(true);
				bagBtn->setSelectedState(false);
				propertyBtn->setSelectedState(false);
				skillBtn->setSelectedState(false);
				initTrainLayer(_trainLayer);
			}
			else if (tag == UI_BUTTON_SKILL)
			{
				this->addChild(_skillLayer, 1);
				bagBtn->setTouchEnabled(true);
				propertyBtn->setTouchEnabled(true);
				upBtn->setTouchEnabled(true);
				skillBtn->setTouchEnabled(false);
				bagBtn->setSelectedState(false);
				propertyBtn->setSelectedState(false);
				upBtn->setSelectedState(false);
				initSkillLayer(_skillLayer);

				this->scheduleOnce(schedule_selector(BaseScene::onEventGuidance), 1.0f / 60);

				//Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_NEWGUIDANCE_START);
			}

			for (int i = SCENE_NODE_EQUIP; i < SCENE_NODE_EQUIP + 6; i++)
			{
				CCS_GET_COMPONENT_FROM_SCENE(node, Layer, equipLayer, i);
				CCS_GET_CHILD(equipLayer, ImageView, equipIcon, UI_PERSONVIEW_3_ICON);
				equipIcon->setTouchEnabled(tag == UI_BUTTON_BAG ? true : false);
			}

		}
		else if (tag == UI6_BUTTON_SKILLUP)
		{
			if (!_isUp)
			{
				this->addChild(AlertView::create(LocalizeService::getInstance()->getString("604"), AlertType::IDLE), 999999);
				r_skillDiamond = 0;
				return;
			}
			auto skillData = s_playerData->getSkill();
			auto need = skillData->getNeed();
			if (need.money > userData->getInfo().money)
			{
				this->addChild(AlertView::create(LocalizeService::getInstance()->getString("660"), AlertType::IDLE), 999999);
				r_skillDiamond = 0;
				return;
			}
			else if (need.level > s_playerData->getInfo().level)
			{
				this->addChild(AlertView::create(LocalizeService::getInstance()->getString("603"), AlertType::IDLE), 999999);
				r_skillDiamond = 0;
				return;
			}
			else
			{
				//if (GameData::getInstance()->skillTime - get_date_now() <= 0)
				//	GameData::getInstance()->skillTime = get_date_now();
				GameData::getInstance()->skillTime += skillData->getInfo().cdTime;
				PartnerTransaction::getInstance()->upgradeMagic(userData->getInfo().uid, s_playerData->getInfo().id, INT(button->getName()));
				auto oldSkillData = s_playerData->getSkill();
				int skillID = oldSkillData->getInfo().next;
				auto newSkillData = GameData::getInstance()->getSkill(skillID);
				//auto newSkillData = SkillData::create(skillID);
				s_playerData->setSkill(newSkillData);
				GameData::getInstance()->isTaskComplete(GameData::MissionType::SKILL, 0, 0);
				userData->setMoney(userData->getInfo().money - need.money);
				initSkillLayer(_skillLayer);
				SoundService::getInstance()->playSFX(SOUND_EFFECT_SHIPUP);

				Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_NEWGUIDANCE_START);
			}
		}
		else if (tag == UI6_BUTTON_SPEEDUP)
		{

			//auto t = (GameData::getInstance()->skillTime - get_date_now()) / 60 == 0 ? 1 : (GameData::getInstance()->skillTime - get_date_now()) / 60 + 1;
			auto t = (GameData::getInstance()->skillTime ) / 60 == 0 ? 1 : (GameData::getInstance()->skillTime ) / 60 + 1;
			for (auto a : userData->getQuickens())
			{
				if (a->getInfo().type == GameData::CDTimeType::CD_SKILL)
				{
					r_skillDiamond = ceil(t*a->getInfo().reqValue);
					//userData->getInfo().diamond -= d;
					break;
				}
			}

			//std::string str = "644";
			//this->addChild(AlertView::create(LocalizeService::getInstance()->getString("644") + STRING(r_skillDiamond), AlertType::DIALOG, &r_skillDiamond), 10000, 99999);

			std::string str = "644";
			this->addChild(AlertView::create(STRING(r_skillDiamond), AlertType::DIALOG, &str), 10000, TagBaseScene::ALERT);
		}
		break;
	}
}

void PersonScene::touchPageView(Ref* object, cocos2d::ui::PageViewEventType type)
{
	auto pageView = static_cast<PageView*>(object);
	switch (type)
	{
	case PageViewEventType::PAGEVIEW_EVENT_TURNING:
		CCLOG("index: %d", pageView->getCurPageIndex());
		for (int i = 0; i < r_bags.size(); i++)
		{
			auto sprite = r_bags[i];
			sprite->setSpriteFrame(i == pageView->getCurPageIndex() ? "ss_partner_bagdot2.png" : "ss_partner_bagdot.png");
		}
		break;
	default:
		break;
	}
}

// bool PersonScene::onTouchBegan(Touch* touch, Event* event)
// {
// 	if (!r_onBag)
// 	{
// 		return false;
// 	}
// 
// 	auto target = static_cast<Widget*>(event->getCurrentTarget());
// 
// 	auto pointInTraget = target->convertTouchToNodeSpace(touch);
// 	auto r = Rect(0, 0, target->getSize().width, target->getSize().height);
// 	if (r.containsPoint(pointInTraget))
// 	{
// 		CCLOG("touch target tag: %d", target->getTag());
// 
// 		auto item = static_cast<ItemData*>(target->getUserObject());
// 		if (item != nullptr)
// 		{
// 			if (target->getTag() >= SCENE_NODE_EQUIP)
// 			{
// 				_equipTarget = target;
// 				return true;
// 			}
// 			else if (target->getTag() == SCENE_BAG_NODE_CELL)
// 			{
// 				if (item->getInfo().type == ItemData::ItemType::EQUIP)
// 				{
// 					_touchTarget = target;
// 					return true;
// 				}
// 			}
// 		}
// 	}
// 
// 	return false;
// }
// 
// void PersonScene::onTouchMoved(Touch* touch, Event* event)
// {
// 	//CCLOG("ccTouchMoved");
// 
// 	if (_movedTarget != nullptr)
// 	{
// 		_movedTarget->setPosition(touch->getLocation());
// 		return;
// 	}
// 
// 	if (_equipTarget != nullptr)
// 	{
// 		auto item = dynamic_cast<ItemData*>(_equipTarget->getUserObject());
// 
// 		CCS_GET_CHILD(_equipTarget, ImageView, equipIcon, UI_PERSONVIEW_3_ICON);
// 		CCS_GET_CHILD(_equipTarget, ImageView, equipBG, UI_PERSONVIEW_3_BG);
// 
// 		equipBG->setVisible(false);
// 		auto iconName = String::createWithFormat("ss_ty_partner_equip_%d.png", item->getInfo().equip);
// 		equipIcon->loadTexture(iconName->getCString(), TextureResType::PLIST);
// 
// 		_movedTarget = ImageView::create();
// 		_movedTarget->loadTexture(GET_PROP_ICON(item->getInfo().icon), TextureResType::PLIST);
// 		_movedTarget->setPosition(touch->getLocation());
// 		this->addChild(_movedTarget, 9999);
// 	}
// 
// 	if (_touchTarget != nullptr)
// 	{
// 		auto item = dynamic_cast<ItemData*>(_touchTarget->getUserObject());
// 
// 		// 		CCS_GET_CHILD(_touchTarget, ImageView, cellIcon, UI_OTHER_7_IMAGE_ICON);
// 		// 		CCS_GET_CHILD(_touchTarget, ImageView, cellLevel, UI_OTHER_7_IMAGE_BG);
// 		CCS_GET_CHILD(_touchTarget, Node, cellNode, UI_OTHER_7_ITEM);
// 		CCS_GET_CHILD(_touchTarget, TextBMFont, cellCount, UI_OTHER_7_TEXT_COUNT);
// 
// 		//cellIcon->setVisible(false);
// 		cellNode->setVisible(false);
// 		cellCount->setVisible(false);
// 		//cellLevel->setVisible(false);
// 
// 		_movedTarget = ImageView::create();
// 		_movedTarget->loadTexture(GET_PROP_ICON(item->getInfo().icon), TextureResType::PLIST);
// 		_movedTarget->setPosition(touch->getLocation());
// 		this->addChild(_movedTarget, 9999);
// 	}
// }
// 
// void PersonScene::onTouchEnded(Touch* touch, Event* event)
// {
// 	//CCLOG("ccTouchEnded");
// 
// 	CCS_GET_COMPONENT_FROM_SCENE(node, Layer, leftLayer, UI_PERSONVIEW_2);
// 
// 	CCLOG("w, h: %f, %f", leftLayer->getContentSize().width, leftLayer->getContentSize().height);
// 
// 	auto pointInTraget = leftLayer->convertTouchToNodeSpace(touch);
// 	auto r = Rect(0, 0, leftLayer->getContentSize().width, leftLayer->getContentSize().height);
// 	if (r.containsPoint(pointInTraget))
// 	{
// 		if (_touchTarget != nullptr)
// 		{
// 			auto item = static_cast<ItemData*>(_touchTarget->getUserObject());
// 
// 			if (item->getInfo().require <= s_playerData->getInfo().level)
// 			{
// 				CCS_GET_COMPONENT_FROM_SCENE(node, Layer, equipLayer, SCENE_NODE_EQUIP + (item->getInfo().equip - 1));
// 				CCS_GET_CHILD(equipLayer, ImageView, equipIcon, UI_PERSONVIEW_3_ICON);
// 				CCS_GET_CHILD(equipLayer, ImageView, equipBG, UI_PERSONVIEW_3_BG);
// 
// 				_touchTarget->setUserObject(nullptr);
// 				_touchTarget = nullptr;
// 
// 				auto equip = static_cast<ItemData*>(equipLayer->getUserObject());
// 
// 				if (equip != nullptr)
// 				{
// 					for (auto cellLayer : r_cellNodes)
// 					{
// 						// 						CCS_GET_CHILD(cellLayer, ImageView, cellIcon, UI_OTHER_7_IMAGE_ICON);
// 						// 						CCS_GET_CHILD(cellLayer, ImageView, cellLevel, UI_OTHER_7_IMAGE_BG);
// 						CCS_GET_CHILD(cellLayer, TextBMFont, cellCount, UI_OTHER_7_TEXT_COUNT);
// 
// 						if (cellLayer->getUserObject() == nullptr)
// 						{
// 							cellLayer->setUserObject(equip);
// 
// 							auto sprite = Item::create(equip->getInfo());
// 							cellLayer->addChild(sprite, cellLayer->getChildrenCount() + 1, UI_OTHER_7_ITEM);
// 
// 							//cellIcon->loadTexture(GET_PROP_ICON(equip->getInfo().icon), TextureResType::PLIST);
// 							//cellIcon->setVisible(true);
// 							// 							if (item->getInfo().type == ItemData::ItemType::EQUIP)
// 							// 							{
// 							// 								cellLevel->loadTexture(GET_PROP_ICON_MASK(equip->getInfo().icon), TextureResType::PLIST);
// 							// 								cellLevel->setVisible(true);
// 							// 							}
// 							cellCount->setText(STRING(equip->getInfo().count).c_str());
// 							cellCount->setVisible(true);
// 
// 							equipLayer->setUserObject(nullptr);
// 
// 							GameData::getInstance()->getUserData()->addItem(equip);
// 							s_playerData->discharge(equip->getInfo().id);
// 							break;
// 						}
// 					}
// 				}
// 
// 				if (item->getInfo().quality > 0)
// 				{
// 					equipBG->setVisible(true);
// 				}
// 
// 				equipIcon->loadTexture(GET_PROP_ICON(item->getInfo().icon), TextureResType::PLIST);
// 				equipIcon->setVisible(true);
// 				equipLayer->setUserObject(item);
// 
// 				GameData::getInstance()->getUserData()->rmvItem(item->getInfo().id);
// 				s_playerData->equip(item);
// 			}
// 			else
// 			{
// 				this->addChild(AlertView::create(LocalizeService::getInstance()->getString("4012"), AlertType::IDLE), 999999);
// 			}
// 		}
// 
// 		if (_equipTarget != nullptr)
// 		{
// 			auto item = static_cast<ItemData*>(_equipTarget->getUserObject());
// 
// 			CCS_GET_CHILD(_equipTarget, ImageView, equipIcon, UI_PERSONVIEW_3_ICON);
// 			CCS_GET_CHILD(_equipTarget, ImageView, equipBG, UI_PERSONVIEW_3_BG);
// 
// 			if (item->getInfo().quality > 0)
// 			{
// 				equipBG->setVisible(true);
// 			}
// 
// 			equipIcon->loadTexture(GET_PROP_ICON(item->getInfo().icon), TextureResType::PLIST);
// 			_equipTarget = nullptr;
// 		}
// 	}
// 
// 	if (_movedTarget != nullptr)
// 	{
// 		this->removeChild(_movedTarget);
// 		_movedTarget = nullptr;
// 	}
// 
// 	if (_touchTarget != nullptr)
// 	{
// 		// 		CCS_GET_CHILD(_touchTarget, ImageView, cellIcon, UI_OTHER_7_IMAGE_ICON);
// 		// 		CCS_GET_CHILD(_touchTarget, ImageView, cellLevel, UI_OTHER_7_IMAGE_BG);
// 		CCS_GET_CHILD(_touchTarget, Node, cellNode, UI_OTHER_7_ITEM);
// 		CCS_GET_CHILD(_touchTarget, TextBMFont, cellCount, UI_OTHER_7_TEXT_COUNT);
// 
// 		cellNode->setVisible(true);
// 		//cellIcon->setVisible(true);
// 		cellCount->setVisible(true);
// 		//cellLevel->setVisible(true);
// 
// 		_touchTarget = nullptr;
// 	}
// 
// 	if (_equipTarget != nullptr)
// 	{
// 		auto equip = static_cast<ItemData*>(_equipTarget->getUserObject());
// 
// 		for (auto cellLayer : r_cellNodes)
// 		{
// 			//CCS_GET_CHILD(cellLayer, ImageView, cellIcon, UI_OTHER_7_IMAGE_ICON);
// 			CCS_GET_CHILD(cellLayer, TextBMFont, cellCount, UI_OTHER_7_TEXT_COUNT);
// 			CCS_GET_CHILD(cellLayer, Button, cellBG, UI_OTHER_7_BUTTON_BG);
// 			//CCS_GET_CHILD(cellLayer, ImageView, cellLevel, UI_OTHER_7_IMAGE_BG);
// 
// 			if (cellBG->getUserObject() == nullptr)
// 			{
// 				cellBG->setUserObject(equip);
// 
// 				auto sprite = Item::create(equip->getInfo());
// 				cellLayer->addChild(sprite, cellLayer->getChildrenCount() + 1, UI_OTHER_7_ITEM);
// 				// 				cellIcon->loadTexture(GET_PROP_ICON(equip->getInfo().icon), TextureResType::PLIST);
// 				// 				cellIcon->setVisible(true);
// 				// 				if (equip->getInfo().type == ItemData::ItemType::EQUIP)
// 				// 				{
// 				// 					cellLevel->loadTexture(GET_PROP_ICON_MASK(equip->getInfo().icon), TextureResType::PLIST);
// 				// 					cellLevel->setVisible(true);
// 				// 				}
// 				cellCount->setText(STRING(equip->getInfo().count).c_str());
// 				cellCount->setVisible(true);
// 
// 				_equipTarget->setUserObject(nullptr);
// 
// 				GameData::getInstance()->getUserData()->addItem(equip);
// 				s_playerData->discharge(equip->getInfo().id);
// 				break;
// 			}
// 		}
// 
// 		_equipTarget = nullptr;
// 	}
// }

void PersonScene::initEquipments()
{
	for (int i = SCENE_NODE_EQUIP; i < SCENE_NODE_EQUIP + 6; i++)
	{
		CCS_GET_COMPONENT_FROM_SCENE(node, Layer, equipLayer, i);
		CCS_GET_CHILD(equipLayer, ImageView, equipIcon, UI_PERSONVIEW_3_ICON);
		//CCS_GET_CHILD(equipLayer, ImageView, imgBG, UI_PERSONVIEW_3_IMAGEIVEW);
		auto iconName = String::createWithFormat("ss_ty_partner_equip_%d.png", i - 30000);
		equipIcon->loadTexture(iconName->getCString(), TextureResType::PLIST);
		//equipIcon->setTouchEnabled(true);
		equipIcon->addTouchEventListener(this, toucheventselector(PersonScene::touchButton));
		equipLayer->setTag(i);

		//this->getEventDispatcher()->addEventListenerWithSceneGraphPriority(_listener->clone(), equipLayer);
		//imgBG->addTouchEventListener(this, toucheventselector(PersonScene::touchButton));
	}

	for (auto equip : s_playerData->getEquips())
	{
		if (equip.second->getInfo().equip <= 0) continue;

		CCS_GET_COMPONENT_FROM_SCENE(node, Layer, equipLayer, SCENE_NODE_EQUIP + equip.second->getInfo().equip - 1);
		CCS_GET_CHILD(equipLayer, ImageView, equipIcon, UI_PERSONVIEW_3_ICON);
		CCS_GET_CHILD(equipLayer, ImageView, equipBG, UI_PERSONVIEW_3_BG);
		if (equip.second->getInfo().type == ItemData::ItemType::EQUIP && equip.second->getInfo().quality > 0)
		{
			equipBG->loadTexture(GET_PROP_ICON_MASK(equip.second->getInfo().icon), TextureResType::PLIST);
			equipBG->setColor(GetEquipQualityColor(equip.second->getInfo().quality));
		}
		else
		{
			equipBG->setVisible(false);
		}

		equipIcon->loadTexture(GET_PROP_ICON(equip.second->getInfo().icon), TextureResType::PLIST);
		// 		auto sprite = Item::create(equip.second);
		// 		equipLayer->addChild(sprite, equipLayer->getChildrenCount() + 1, UI_OTHER_7_ITEM);
		equipIcon->setUserObject(equip.second);
	}
}

void PersonScene::initBagLayer(Node *layer)
{
	CCS_GET_CHILD(_bagLayer, PageView, pageView, UI5_PAGEVIEW_CELLS);

	r_onBag = true;
	r_bagIndex = 0;
	r_cellNodes.clear();
	pageView->removeAllPages();

	auto widget = CCS_CREATE_LAYER("UI_Other_7");
	auto items = GameData::getInstance()->getUserData()->getItems();
	int cellLayoutCount = items.size() / 16 == 0 ? 1 : items.size() / 16 + 1;
	for (int k = 0; k < cellLayoutCount; k++)
	{
		auto cellLayout = Layout::create();
		cellLayout->setTag(1);

		for (int i = 0; i < 4; i++)
		{
			for (int j = 0; j < 4; j++)
			{
				//auto node = CCS_CREATE_LAYER("UI_Other_7.ExportJson");
				auto node = widget->clone();
				node->setPosition(Point(7 + j * 105, 320 - i * 105));
				//node->setTag(SCENE_BAG_NODE_CELL);
				//this->getEventDispatcher()->addEventListenerWithSceneGraphPriority(_listener->clone(), node);

				//CCS_GET_CHILD(node, ImageView, cellIcon, UI_OTHER_7_IMAGE_ICON);
				CCS_GET_CHILD(node, TextBMFont, cellCount, UI_OTHER_7_TEXT_COUNT);
				CCS_GET_CHILD(node, Button, cellBG, UI_OTHER_7_BUTTON_BG);

				//cellIcon->loadTexture("ss_partner_baglock.png", TextureResType::PLIST);
				cellBG->addTouchEventListener(this, toucheventselector(PersonScene::touchButton));
				cellLayout->addChild(node);

				r_cellNodes.push_back(node);
			}
		}

		pageView->addPage(cellLayout);

		auto size = Director::getInstance()->getWinSize();
		auto point = Sprite::createWithSpriteFrameName(k == 0 ? "ss_partner_bagdot2.png" : "ss_partner_bagdot.png");
		point->setPosition(Point((size.width - 350) + k * 30, 58));
		this->addChild(point, this->getChildrenCount() + 1);
		r_bags.push_back(point);
	}

	int cellIndex = 0;
	for (auto item : items)
	{
		if (cellIndex >= r_cellNodes.size()) break;

		auto cellLayer = r_cellNodes[cellIndex];
		//CCS_GET_COMPONENT_FROM_SCENE(layer, Layer, cellLayer, cellIndex);
		// 		CCS_GET_CHILD(cellLayer, ImageView, cellIcon, UI_OTHER_7_IMAGE_ICON);
		// 		CCS_GET_CHILD(cellLayer, ImageView, cellLevel, UI_OTHER_7_IMAGE_BG);
		CCS_GET_CHILD(cellLayer, TextBMFont, cellCount, UI_OTHER_7_TEXT_COUNT);
		CCS_GET_CHILD(cellLayer, Button, cellBG, UI_OTHER_7_BUTTON_BG);

		auto sprite = Item::create(item.second->getInfo());
		cellLayer->addChild(sprite, cellLayer->getChildrenCount() + 1, UI_OTHER_7_ITEM);
		//		cellIcon->setVisible(false);
		//cellIcon->loadTexture(GET_PROP_ICON(item.second->getInfo().icon), TextureResType::PLIST);
		//cellIcon->setVisible(false);
		cellCount->setText(STRING(item.second->getInfo().count).c_str());
		cellLayer->setUserObject(item.second);
		cellBG->setUserObject(item.second);
		// 		if (item.second->getInfo().type == ItemData::ItemType::EQUIP && item.second->getInfo().quality > 0)
		// 		{
		// 			cellLevel->loadTexture(GET_PROP_ICON_MASK(item.second->getInfo().icon), TextureResType::PLIST);
		// 			cellLevel->setColor(GetEquipQualityColor(item.second->getInfo().quality));
		// 		}
		// 		else
		// 		{
		// 			cellLevel->setVisible(false);
		// 		}

		//		cellLevel->setVisible(false);

		// 		auto size = Director::getInstance()->getWinSize();
		// 		auto itemIcon = EffectSprite::createWithSpriteFrameName(GET_PROP_ICON(item.second->getInfo().icon));
		// 		itemIcon->setEffect(EffectOutline::create());
		// 		itemIcon->setPosition(Point(size.width / 2, size.height / 2));
		// 		this->addChild(itemIcon, this->getChildrenCount() + 1);

		cellIndex++;
	}
}

void PersonScene::initPropertyLayer(Node *layer)
{
	auto playerInfo = s_playerData->getInfo();

	auto hp = dynamic_cast<Text*>(layer->getChildByTag(UI4_TEXT_HP));
	hp->setText(STRING(s_playerData->getHP()));
	auto str = dynamic_cast<Text*>(layer->getChildByTag(UI4_TEXT_STR));
	str->setText(STRING(s_playerData->getSTR()));
	auto inc = dynamic_cast<Text*>(layer->getChildByTag(UI4_TEXT_INC));
	inc->setText(STRING(s_playerData->getINC()));
	auto atk = dynamic_cast<Text*>(layer->getChildByTag(UI4_TEXT_ATK));
	atk->setText(STRING(s_playerData->getATK()));
	auto def = dynamic_cast<Text*>(layer->getChildByTag(UI4_TEXT_DEF));
	def->setText(STRING(s_playerData->getDEF()));
	auto mag = dynamic_cast<Text*>(layer->getChildByTag(UI4_TEXT_MAG));
	mag->setText(STRING(s_playerData->getMAG()));
	auto res = dynamic_cast<Text*>(layer->getChildByTag(UI4_TEXT_RES));
	res->setText(STRING(s_playerData->getRES()));
	auto captainValue = dynamic_cast<Text*>(layer->getChildByTag(UI4_TEXT_CAPTAINVALUE));
	captainValue->setText(STRING(s_playerData->getCP()));
	auto navigateValue = dynamic_cast<Text*>(layer->getChildByTag(UI4_TEXT_NAVIGATEVALUE));
	navigateValue->setText(STRING(s_playerData->getNP()));
	auto operateValue = dynamic_cast<Text*>(layer->getChildByTag(UI4_TEXT_OPERATEVALUE));
	operateValue->setText(STRING(s_playerData->getOP()));
	auto ui4_level = dynamic_cast<Text*>(layer->getChildByTag(UI4_TEXT_LEVEL));
	ui4_level->setText(STRING(playerInfo.level));
	auto job = dynamic_cast<TextBMFont*>(layer->getChildByTag(UI4_TEXTBMFONT_JOB));
	std::string jobName = LocalizeService::getInstance()->getString(PLAYER_JOB_NAMES[playerInfo.job]);
	job->setText(jobName.c_str());
	auto exp = dynamic_cast<Text*>(layer->getChildByTag(UI4_TEXT_EXP));
	std::string str1 = STRING(playerInfo.exp) + "/" + STRING(playerInfo.maxExp);
	exp->setText(UTF8(str1));
	auto loadingbar = dynamic_cast<LoadingBar*>(layer->getChildByTag(UI4_LOADINGBAR_EXP));
	loadingbar->setPercent(FLOAT(playerInfo.exp) / FLOAT(playerInfo.maxExp) * 100);
	auto rankid = dynamic_cast<Text*>(layer->getChildByTag(UI4_TEXT_FIGHT));
	if (GameData::getInstance()->getUserData()->getInfo().rankId > 0)
		rankid->setText(STRING(GameData::getInstance()->getUserData()->getInfo().rankId));
}

void PersonScene::initSkillLayer(Node *layer)
{
	auto userData = GameData::getInstance()->getUserData();
	auto playerInfo = s_playerData->getInfo();
	auto skillData = s_playerData->getSkill();
	auto info = skillData->getInfo();
	auto effect = skillData->getEffect();
	auto need = skillData->getNeed();

	CCS_GET_CHILD(_skillLayer, ImageView, skillIcon, UI6_IMAGEVIEW_SKILLICON);
	CCS_GET_CHILD(_skillLayer, Text, skillName, UI6_TEXT_SKILLNAME);
	CCS_GET_CHILD(_skillLayer, Text, level, UI6_TEXT_LEVEL);
	CCS_GET_CHILD(_skillLayer, Text, ffecValue, UI6_TEXT_FFECTVALUE);
	CCS_GET_CHILD(_skillLayer, Text, evenType, UI6_TEXT_EXTENTYPE);
	CCS_GET_CHILD(_skillLayer, Button, speedupBtn, UI6_BUTTON_SPEEDUP);

	skillIcon->loadTexture(GET_SKILL_ICON_FILE(info.icon), TextureResType::PLIST);
	skillName->setText(LocalizeService::getInstance()->getString(info.name));
	level->setText("Lv." + STRING(info.level));
	if (info.target == 0)
	{
		ffecValue->setText(LocalizeService::getInstance()->getString(SKILL_EFFECT_NAMES(effect.type)) + STRING(effect.value));
		evenType->setText(LocalizeService::getInstance()->getString(SKILL_EXTENT_NAMES[info.extent]));
	}
	else
	{
		ffecValue->setText(LocalizeService::getInstance()->getString("100100") + STRING(effect.value));
		evenType->setText(LocalizeService::getInstance()->getString(info.extent == 1 ? "100100" : "100102"));
	}
	speedupBtn->addTouchEventListener(this, toucheventselector(PersonScene::touchButton));

	//auto nextSkillData = SkillData::create(info.next);
	auto nextSkillData = GameData::getInstance()->getSkill(info.next);
	auto nextInfo = nextSkillData->getInfo();
	auto nextEff = nextSkillData->getEffect();

	CCS_GET_CHILD(_skillLayer, Text, nextffecValue, UI6_TEXT_NEXTFFECTVALUE);
	CCS_GET_CHILD(_skillLayer, Text, nextLevel, UI6_TEXT_NEXTLEVEL);
	CCS_GET_CHILD(_skillLayer, ImageView, coin, UI6_IMAGEVIEW_COIN);
	CCS_GET_CHILD(_skillLayer, Text, money, UI6_TEXT_MONEY);
	CCS_GET_CHILD(_skillLayer, Button, skillUpBtn, UI6_BUTTON_SKILLUP);
	skillUpBtn->setPressedActionEnabled(true);

	CCS_GET_CHILD(_skillLayer, Text, cdTime, UI6_TEXT_CDTIME);
	//if (GameData::getInstance()->skillTime - get_date_now() <= 0)
	if (GameData::getInstance()->skillTime  <= 0)
	{
		cdTime->setText("0:00:00");
		speedupBtn->setOpacity(0);
		speedupBtn->setTouchEnabled(false);
		//this->unscheduleUpdate();
	}
	else
	{
		//cdTime->setText(getTimer(GameData::getInstance()->skillTime - get_date_now()));
		cdTime->setText(getTimer(GameData::getInstance()->skillTime ));
		speedupBtn->setOpacity(255);
		speedupBtn->setPressedActionEnabled(true);
		speedupBtn->setTouchEnabled(true);
		//this->scheduleUpdate();
	}

	//if (GameData::getInstance()->skillTime - get_date_now() > _cd)
	if (GameData::getInstance()->skillTime  > _cd)
		cdTime->setColor(Color3B(255, 0, 0));
	else
		cdTime->setColor(Color3B(255, 255, 255));

	//_isUp = GameData::getInstance()->skillTime - get_date_now() > _cd ? false : true;
	_isUp = GameData::getInstance()->skillTime  > _cd ? false : true;

	if (info.next == 0)
	{
		nextffecValue->setText("");
		nextLevel->setVisible(false);
		coin->setVisible(false);
		money->setVisible(false);
		skillUpBtn->setVisible(false);
	}
	else
	{
		if (need.level > s_playerData->getInfo().level)
			nextLevel->setColor(Color3B(255, 0, 0));
		if (need.money > userData->getInfo().money)
			money->setColor(Color3B(255, 0, 0));
		if (info.target == 0)
		{
			//nextffecValue->setText(LocalizeService::getInstance()->getString(SKILL_EFFECT_NAMES(1)) + LocalizeService::getInstance()->getString(SKILL_EFFECT_NAMES(nextEff.type)) + STRING(nextEff.value));
			nextffecValue->setText(LocalizeService::getInstance()->getString(SKILL_EFFECT_NAMES(nextEff.type)) + STRING(nextEff.value));
		}
		else
		{
			nextffecValue->setText(LocalizeService::getInstance()->getString("100100") + STRING(nextEff.value));
		}
		nextLevel->setText("Lv" + STRING(need.level));
		money->setText(STRING(need.money));
		skillUpBtn->setName(STRING(info.mid).c_str());
		skillUpBtn->addTouchEventListener(this, toucheventselector(PersonScene::touchButton));
	}

	auto skillDes = dynamic_cast<Text*>(layer->getChildByTag(UI6_TEXT_PASSIVESKILLDES));
	auto diamond = dynamic_cast<Text*>(layer->getChildByTag(UI6_TEXT_DIAMOND));
	auto refreshBtn = dynamic_cast<Button*>(layer->getChildByTag(UI6_BUTTON_REFRESH));
	refreshBtn->setPressedActionEnabled(true);
	auto xiaohao = dynamic_cast<Text*>(layer->getChildByTag(UI6_TEXT_XIAOHAO));
	auto diamondIcon = dynamic_cast<ImageView*>(layer->getChildByTag(UI6_IMAGEVIEW_DIAMOND));
	diamond->setVisible(true);
	refreshBtn->setVisible(true);
	diamondIcon->setVisible(true);
	xiaohao->setVisible(true);

	if (s_playerData->getTeamSkill() != nullptr)
	{
		auto teamSkillInfo = s_playerData->getTeamSkill()->getEffect();
		auto ts = GameData::getInstance()->getTeamSkillNeed(playerInfo.appraisal, 2);
		skillDes->setText(LocalizeService::getInstance()->getString(SKILL_EFFECT_NAMES(teamSkillInfo.type)) + STRING(teamSkillInfo.value * 100) + "%");
		diamond->setText(STRING(ts.value));
		_refresh = ts.value;
		if (ts.value > userData->getInfo().diamond)
		{
			diamond->setColor(Color3B(255, 0, 0));
		}
		refreshBtn->addTouchEventListener(this, toucheventselector(PersonScene::touchButton));
	}
	else
	{
		skillDes->setText(LocalizeService::getInstance()->getString(SKILL_EFFECT_NAMES(3)));
		diamond->setVisible(false);
		refreshBtn->setVisible(false);
		diamondIcon->setVisible(false);
		xiaohao->setVisible(false);
	}
	//this->schedule(schedule_selector(PersonScene::update), 1.0f);
}

void PersonScene::initTrainLayer(Node* layer)
{
	auto userData = GameData::getInstance()->getUserData();
	auto playerInfo = s_playerData->getInfo();
	auto cultivate1 = GameData::getInstance()->getCultivate(GameData::CultivateType::NORMAL);
	auto cultivate2 = GameData::getInstance()->getCultivate(GameData::CultivateType::STRENGTHEN);


	auto str1 = dynamic_cast<Text*>(layer->getChildByTag(UI7_TEXT_STR1));
	str1->setText(STRING(s_playerData->getSTR_G()));
	auto str2 = dynamic_cast<Text*>(layer->getChildByTag(UI7_TEXT_STR2));
	str2->setText("+" + STRING(s_playerData->getAttribute().STR_U));
	auto str3 = dynamic_cast<TextBMFont*>(layer->getChildByTag(UI7_TEXT_STR3));
	str3->setVisible(false);
	auto int1 = dynamic_cast<Text*>(layer->getChildByTag(UI7_TEXT_INT1));
	int1->setText(STRING(s_playerData->getINC_G()));
	auto int2 = dynamic_cast<Text*>(layer->getChildByTag(UI7_TEXT_INT2));
	int2->setText("+" + STRING(s_playerData->getAttribute().INC_U));
	auto int3 = dynamic_cast<TextBMFont*>(layer->getChildByTag(UI7_TEXT_INT3));
	int3->setVisible(false);
	auto commonBtn = dynamic_cast<Button*>(layer->getChildByTag(UI7_BUTTON_COMMON));
	commonBtn->setPressedActionEnabled(true);
	commonBtn->addTouchEventListener(this, toucheventselector(PersonScene::touchButton));
	auto strengthenBtn = dynamic_cast<Button*>(layer->getChildByTag(UI7_BUTTON_STRENGTHEN));
	strengthenBtn->setPressedActionEnabled(true);
	strengthenBtn->addTouchEventListener(this, toucheventselector(PersonScene::touchButton));
	auto cancelBtn = dynamic_cast<Button*>(layer->getChildByTag(UI7_BUTTON_CANCEL));
	cancelBtn->addTouchEventListener(this, toucheventselector(PersonScene::touchButton));
	auto saveBtn = dynamic_cast<Button*>(layer->getChildByTag(UI7_BUTTON_SAVE));
	saveBtn->setPressedActionEnabled(true);
	saveBtn->setVisible(false);
	saveBtn->addTouchEventListener(this, toucheventselector(PersonScene::touchButton));
	auto money = dynamic_cast<Text*>(layer->getChildByTag(UI7_TEXT_COMMONPRICE));
	money->setText(STRING(cultivate1.getMoney(playerInfo.level)));
	if (cultivate1.getMoney(playerInfo.level) > userData->getInfo().money)
		money->setColor(Color3B(255, 0, 0));
	auto diamond = dynamic_cast<Text*>(layer->getChildByTag(UI7_TEXT_STRENGTHENPRICE));
	diamond->setText(STRING(cultivate2.diamond));
	if (cultivate2.diamond > userData->getInfo().diamond)
		diamond->setColor(Color3B(255, 0, 0));
}

bool PersonScene::equipItem(ItemData *item)
{
	if (item->getInfo().require > s_playerData->getInfo().level)
	{
		//MessageBox("Sorry, your level is not enough ...", "Warning");
		this->addChild(AlertView::create(LocalizeService::getInstance()->getString("4003"), AlertType::IDLE), 999999);
		return false;
	}

	auto isJob = false;
	for (auto job : item->getInfo().jobs)
	{
		if (job == s_playerData->getInfo().job)
		{
			isJob = true;
			break;
		}
	}

	if (!isJob)
	{
		this->addChild(AlertView::create(LocalizeService::getInstance()->getString("652"), AlertType::IDLE), 999999);
		return false;
	}

	CCS_GET_COMPONENT_FROM_SCENE(node, Layer, equipLayer, SCENE_NODE_EQUIP + (item->getInfo().equip - 1));
	CCS_GET_CHILD(equipLayer, ImageView, equipIcon, UI_PERSONVIEW_3_ICON);
	CCS_GET_CHILD(equipLayer, ImageView, equipBG, UI_PERSONVIEW_3_BG);

	for (auto cellLayer : r_cellNodes)
	{
		CCS_GET_CHILD(cellLayer, Node, cellNode, UI_OTHER_7_ITEM);
		// 		CCS_GET_CHILD(cellLayer, ImageView, cellIcon, UI_OTHER_7_IMAGE_ICON);
		// 		CCS_GET_CHILD(cellLayer, ImageView, cellLevel, UI_OTHER_7_IMAGE_BG);
		CCS_GET_CHILD(cellLayer, TextBMFont, cellCount, UI_OTHER_7_TEXT_COUNT);
		CCS_GET_CHILD(cellLayer, Button, cellBG, UI_OTHER_7_BUTTON_BG);

		auto cellItem = dynamic_cast<ItemData*>(cellBG->getUserObject());

		if (cellItem != nullptr && cellItem->getInfo().id == item->getInfo().id)
		{
			cellBG->setUserObject(nullptr);
			cellNode->removeFromParent();
			//cellNode->setVisible(false);
			//cellIcon->setVisible(false);
			cellCount->setVisible(false);
			//cellLevel->setVisible(false);
			break;
		}
	}

	auto equip = static_cast<ItemData*>(equipIcon->getUserObject());

	if (equip != nullptr)
	{
		for (auto cellLayer : r_cellNodes)
		{
			//CCS_GET_CHILD(cellLayer, ImageView, cellIcon, UI_OTHER_7_IMAGE_ICON);
			CCS_GET_CHILD(cellLayer, TextBMFont, cellCount, UI_OTHER_7_TEXT_COUNT);
			CCS_GET_CHILD(cellLayer, Button, cellBG, UI_OTHER_7_BUTTON_BG);
			//CCS_GET_CHILD(cellLayer, ImageView, cellLevel, UI_OTHER_7_IMAGE_BG);

			if (cellBG->getUserObject() == nullptr)
			{
				cellBG->setUserObject(equip);

				auto sprite = Item::create(equip->getInfo());
				cellLayer->addChild(sprite, cellLayer->getChildrenCount() + 1, UI_OTHER_7_ITEM);
				// 				cellIcon->loadTexture(GET_PROP_ICON(equip->getInfo().icon), TextureResType::PLIST);
				// 				cellIcon->setVisible(true);
				// 				if (equip->getInfo().type == ItemData::ItemType::EQUIP)
				// 				{
				// 					cellLevel->loadTexture(GET_PROP_ICON_MASK(equip->getInfo().icon), TextureResType::PLIST);
				// 					cellLevel->setVisible(true);
				// 				}
				cellCount->setText(STRING(equip->getInfo().count).c_str());
				cellCount->setVisible(true);

				equipIcon->setUserObject(nullptr);

				GameData::getInstance()->getUserData()->addItem(equip);
				s_playerData->discharge(equip->getInfo().id);
				break;
			}
		}
	}

	if (item->getInfo().quality > 0)
	{
		equipBG->setVisible(true);
		equipBG->loadTexture(GET_PROP_ICON_MASK(item->getInfo().icon), TextureResType::PLIST);
		equipBG->setColor(GetEquipQualityColor(item->getInfo().quality));
	}
	else equipBG->setVisible(false);
	//equipBG->loadTexture(GET_PROP_ICON_MASK(item->getInfo().icon), TextureResType::PLIST);

	equipIcon->loadTexture(GET_PROP_ICON(item->getInfo().icon), TextureResType::PLIST);
	equipIcon->setVisible(true);
	equipIcon->setUserObject(item);

	GameData::getInstance()->getUserData()->rmvItem(item->getInfo().id);
	s_playerData->equip(item);

	return true;
}

bool PersonScene::unequipItem(ItemData *equip)
{
	for (auto cellLayer : r_cellNodes)
	{
		CCS_GET_CHILD(cellLayer, TextBMFont, cellCount, UI_OTHER_7_TEXT_COUNT);
		CCS_GET_CHILD(cellLayer, Button, cellBG, UI_OTHER_7_BUTTON_BG);

		if (cellBG->getUserObject() == nullptr)
		{
			cellBG->setUserObject(equip);

			auto sprite = Item::create(equip->getInfo());
			cellLayer->addChild(sprite, cellLayer->getChildrenCount() + 1, UI_OTHER_7_ITEM);
			cellCount->setText(STRING(equip->getInfo().count).c_str());
			cellCount->setVisible(true);

			GameData::getInstance()->getUserData()->addItem(equip);
			s_playerData->discharge(equip->getInfo().id);


			CCS_GET_COMPONENT_FROM_SCENE(node, Layer, equipLayer, SCENE_NODE_EQUIP + (equip->getInfo().equip - 1));
			CCS_GET_CHILD(equipLayer, ImageView, equipIcon, UI_PERSONVIEW_3_ICON);
			equipIcon->setUserObject(nullptr);
			equipIcon->loadTexture(cocos2d::String::createWithFormat("ss_ty_partner_equip_%d.png", equip->getInfo().equip)->getCString(), TextureResType::PLIST);
			return true;
		}
	}
	return false;
}

void PersonScene::update(float dt)
{
	CCS_GET_CHILD(_skillLayer, Text, cdTime, UI6_TEXT_CDTIME);
	CCS_GET_CHILD(_skillLayer, Button, speedupBtn, UI6_BUTTON_SPEEDUP);

	//auto nowtiem = get_date_now();
	//if (GameData::getInstance()->skillTime - get_date_now() <= 0)
	if (GameData::getInstance()->skillTime  <= 0)
	{
		cdTime->setText("0:00:00");
		speedupBtn->setOpacity(0);
		speedupBtn->setTouchEnabled(false);
		//this->unscheduleUpdate();
	}
	else
	{
		//cdTime->setText(getTimer(GameData::getInstance()->skillTime - get_date_now()));
		cdTime->setText(getTimer(GameData::getInstance()->skillTime ));
		speedupBtn->setOpacity(255);
		speedupBtn->setPressedActionEnabled(true);
		speedupBtn->setTouchEnabled(true);
		//this->scheduleUpdate();
	}

	//if (GameData::getInstance()->skillTime - get_date_now() > _cd)
	if (GameData::getInstance()->skillTime  > _cd)
		cdTime->setColor(Color3B(255, 0, 0));
	else
		cdTime->setColor(Color3B(255, 255, 255));
	//_isUp = GameData::getInstance()->skillTime - get_date_now() > _cd ? false : true;
	_isUp = GameData::getInstance()->skillTime  > _cd ? false : true;
}