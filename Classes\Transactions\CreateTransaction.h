//
//  CreateTransactions.h
//  TestSOS
//
//  Created by <PERSON><PERSON> on 13-12-21.
//
//

#ifndef __TestSOS__CreateTransactions__
#define __TestSOS__CreateTransactions__

#include "cocos2d.h"
#include "../SOSConfig.h"

static const char* EVENT_CREATE_SUCCESS = "event_create_success";
static const char* EVENT_GET_RANDOM_NAME = "event_get_random_name";

class CreateTransaction : public cocos2d::Ref
{
public:
	FUNC_INSTANCE(CreateTransaction);

public:
	enum FlagType : int
	{
		PLAYER_MID_ERROR = 2,
		PLAYER_NAME_SAME = 3,
	};
    
    CreateTransaction();
    virtual ~CreateTransaction();
    
    void create(std::string userID, std::string name, int seas, int test);
    void getName(std::string userID, int seas);
};

#endif /* defined(__TestSOS__CreateTransactions__) */
