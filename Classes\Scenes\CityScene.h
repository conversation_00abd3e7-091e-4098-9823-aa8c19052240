﻿#ifndef __CITYVIEW_SCENE_H__
#define __CITYVIEW_SCENE_H__

#include "cocos2d.h"
#include "ui/CocosGUI.h"
#include "cocostudio/CocoStudio.h"
#include "../SOSConfig.h"
#include "BaseScene.h"

static const std::string EVENT_CITY_SCENE_CLOUD_CLOSE = "event_city_scene_cloud_close";

class CityScene : public BaseScene
{
public:
	CREATE_FUNC(CityScene);

	static cocos2d::Scene* createScene(bool cloud = false);
	static cocos2d::ui::Widget* getCloudWidget();

	CityScene();
	virtual ~CityScene();
	virtual bool init();
	virtual void onEnter();

	void touchButton(cocos2d::Ref* object, cocos2d::ui::TouchEventType type);
	void removeNpcView(float dt);
	void initMission();
	void onCloudClose();

private:
	cocos2d::Node* node;
	cocos2d::Label* _label;
	cocos2d::Sprite* _cloud;
};

#endif // __CITYVIEW_SCENE_H__