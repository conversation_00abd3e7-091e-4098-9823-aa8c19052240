﻿//
//  FormationTransaction.cpp
//  TestSOS
//
//  Created by <PERSON><PERSON> on 13-12-21.
//
//

#include "FormationTransaction.h"
#include "../Services/HTTPService.h"
#include "../SOSConfig.h"

USING_NS_CC;
USING_STD;

static const char* TRANS_GAME_FORMATION_MODIFY = "Game_Formation_Modify.ashx";
static const char* TRANS_GAME_FORMATION_GET = "Game_Formation_Get.ashx";
static const char* TRANS_GAME_FORMATION_SAVE_CHANGE = "Game_Formation_SaveChang.ashx";

FUNC_GET_INSTANCE(FormationTransaction);

FormationTransaction::FormationTransaction()
{
	Director::getInstance()->getEventDispatcher()->addCustomEventListener(TRANS_GAME_FORMATION_GET, [=](EventCustom* event) {
		CCLOG("[HTTP] response: game formation get success ... ");
		auto data = static_cast<HTTPData *>(event->getUserData());
		auto userData = GameData::getInstance()->getUserData();
		auto teamData = userData->getTeam();
		auto &pv = teamData->getPositions();
		auto list = data->readDatas("PostionArray");
		for (int i = 0; i < list.size(); i++)
		{
			std::string str = list[i]->readString("OnlyID");
			pv[i] = str;
		}
		Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_FORMATION_GET_SUCCESS, NULL);
	});
}

FormationTransaction::~FormationTransaction()
{
	 Director::getInstance()->getEventDispatcher()->removeCustomEventListeners(TRANS_GAME_FORMATION_GET);
}

void FormationTransaction::Modify(std::string uID, std::string onlyID, int postion, int type)
{
	auto data = HTTPData::create();
	data->write("UID", uID.c_str());
	data->write("OnlyID", onlyID.c_str());
	data->write("Postion", postion);
	data->write("Type", type);

	auto url = WEB_HOST + TRANS_GAME_FORMATION_MODIFY;
	HTTPService::getInstance()->request(url.c_str(), data,TRANS_GAME_FORMATION_MODIFY);
}

void FormationTransaction::Get(std::string uID)
{
	auto data= HTTPData::create();
	data->write("UID",uID.c_str());
	auto url = WEB_HOST + TRANS_GAME_FORMATION_GET;
	HTTPService::getInstance()->request(url.c_str(),data,TRANS_GAME_FORMATION_GET);
}

void FormationTransaction::Save(std::string uID,PositionVector postionArray)
{
	auto data= HTTPData::create();
	data->write("UID",uID.c_str());

	std::vector<HTTPData *> value;
	for (int i = 0; i < postionArray.size(); i++)
	{
		auto data1 = HTTPData::create();
		data1->write("OnlyID",postionArray[i].c_str());
		data1->write("Postion",i+1);
		value.push_back(data1);
	}
	data->write("PostionArray",value);


	auto url = WEB_HOST + TRANS_GAME_FORMATION_SAVE_CHANGE;
	HTTPService::getInstance()->request(url.c_str(),data,TRANS_GAME_FORMATION_SAVE_CHANGE);
}