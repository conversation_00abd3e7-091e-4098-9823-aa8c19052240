//
//  Ship.h
//  TestSOS
//
//  Created by <PERSON><PERSON> on 14-1-9.
//
//

#ifndef __TestSOS__Ship__
#define __TestSOS__Ship__

#include "cocos2d.h"
#include "cocostudio/CocoStudio.h"

enum SHIP_FACE
{
    LEFT_UP = 0,
    UP,
    RIGHT_UP,
    RIGHT,
    RIGHT_DOWN,
    DOWN,
    LEFT_DOWN,
    LEFT,
};

class Ship : public cocos2d::Node
{
public:
    static Ship* create(std::string type);
    
public:
    Ship();
    virtual ~Ship();
    
    virtual bool init() override;
    virtual bool init(std::string type);
    
    virtual void update(float delta);
    
public:
    void faceTo(cocos2d::Point point);
    void runTo(cocos2d::Point point);
	void runTo(std::vector<cocos2d::Point> paths);
    
    inline SHIP_FACE getFace() { return _face; }
    void setFace(SHIP_FACE face);
    
    inline float getSpeed() { return _speed; }
    inline void setSpeed(float speed) { _speed = speed; }

	void onMovingCallback();

	inline bool isMoving() { return _moving != nullptr; }
	inline void stop() {
		if (_moving)
		{
			this->stopAction(_moving);
			_moving = nullptr;
		}
	}
	inline float getAngle() { return _angle; }
	inline void setAngle(float angle){ _angle = angle; }
protected:
    double pointToAngle(cocos2d::Point p1, cocos2d::Point p2);
	void moving();
    
protected:
    cocostudio::Armature* _armature;
    SHIP_FACE _face;
    float _speed;
	std::vector<cocos2d::Point> _paths;
	cocos2d::Action* _moving;
	
	float _angle;
};

#endif /* defined(__TestSOS__Ship__) */
