#include "WeaponShopScene.h"
#include "CityScene.h"

USING_NS_CC;
USING_NS_CC_CCS; 
USING_NS_CC_UI;

enum tagWeaponShopScene
{
	UI_WEAPONSHOP_1 = 10004,
	UI_WEAPONSHOP_2 = 10005,
	UI_WEAPONSHOP_5 = 10006,
	UI_WEAPONSHOP_KUANG = 10007,
	UI_WEAPONSHOP_CLOSE = 10008,

	UI_VIEW1_CHECKBOX_SHOP = 720,
	UI_VIEW1_CHECKBOX_SMITHY = 721,
	UI_VIEW1_CHECKBOX_SYNTHERTIC = 722,

	UI_VIEW2_CHECKBOX_EQUIPMENT = 723,
	UI_VIEW2_CHECKBOX_MATERIAL = 724,
	UI_VIEW2_SCROLLVIEW = 725,
	UI_VIEW2_TEXT_COIN = 728,
	UI_VIEW2_TEXT_DIAMOND = 731,
	UI_VIEW2_BUTTON_ADD = 732,

	UI_VIEW_OTHER1_IMAGEIVEW_REPLACE = 641,
	UI_VIEW_OTHER_IMAGEIVEW_REPLACE = 643,
	UI_VIEW_OTHER_TEXTBMFONT_NUMBER0 = 644,
	UI_VIEW_OTHER_TEXTBMFONT_NUMBER1 = 645,

	UI_VIEW3_OTHER1 = 10004,
	UI_VIEW3_OTHER2 = 10005,
	UI_VIEW3_OTHER3 = 10006,
	UI_VIEW3_TEXT_SUCESS = 754,
	UI_VIEW3_CHECKBOX_SELECT = 756,
	UI_VIEW3_TEXT_DIAMOND = 758,
	UI_VIEW3_TEXT_COIN = 761,
	UI_VIEW3_BUTTON_STRENGTHEN = 762,

	UI_VIEW4_OTHER1 = 10004,
	UI_VIEW4_OTHER2 = 10005,
	UI_VIEW4_OTHER3 = 10006,
	UI_VIEW4_OTHER4 = 10007,
	UI_VIEW4_OTHER5 = 10008,
	UI_VIEW4_TEXT_SUCESS = 785,
	UI_VIEW4_TEXT_COIN = 788,
	UI_VIEW4_BUTTON_SYNTHERTIC = 789,
	UI_VIEW4_CHECKBOX_EQUIPMENT = 790,
	UI_VIEW4_CHECKBOX_MATERIAL = 791,
	UI_VIEW4_SCROLLVIEW = 792,
};

Scene* WeaponShopScene::createScene()
{
	auto scene = Scene::create();
	auto layer = WeaponShopScene::create();
	scene->addChild(layer);
	return scene;
}

bool WeaponShopScene::init()
{
	if ( !BaseScene::init() )
	{
		return false;
	}

	node = CCS_CREATE_SCENE("Scene_WeaponShop");
	this->addChild(node);
	
	CCS_GET_COMPONENT_FROM_SCENE(node,Layer,layer1,UI_WEAPONSHOP_1);
	CCS_GET_CHILD(layer1,CheckBox,shopCbo,UI_VIEW1_CHECKBOX_SHOP);
	CCS_GET_CHILD(layer1,CheckBox,smithyCbo,UI_VIEW1_CHECKBOX_SMITHY);
	CCS_GET_CHILD(layer1,CheckBox,syntheticCbo,UI_VIEW1_CHECKBOX_SYNTHERTIC);
	shopCbo->addTouchEventListener(this,toucheventselector(WeaponShopScene::touchButton));
	smithyCbo->addTouchEventListener(this,toucheventselector(WeaponShopScene::touchButton));
	syntheticCbo->addTouchEventListener(this,toucheventselector(WeaponShopScene::touchButton));

	return true;
}

void WeaponShopScene::menuCloseCallback(Ref* pSender)
{
	Director::getInstance()->end();

#if (CC_TARGET_PLATFORM == CC_PLATFORM_IOS)
	exit(0);
#endif
}

void WeaponShopScene::touchButton(Ref* obj,cocos2d::ui::TouchEventType eventType)  
{  
	auto widget = dynamic_cast<Widget*>(obj);  
	int tag = widget->getTag();  
	switch (eventType)
	{
	case TouchEventType::TOUCH_EVENT_BEGAN: 
		break;
	case TouchEventType::TOUCH_EVENT_ENDED: 
	
		break;
	}
}

bool WeaponShopScene::onTouchBegan(Touch* touch, Event* event)  
{  
	CCLOG("ccTouchBegan");  
	return true;  
}  

void WeaponShopScene::onTouchMoved(Touch* touch, Event* event){  
	CCLOG("ccTouchMoved");  
}  

void WeaponShopScene::onTouchEnded(Touch* touch, Event* event)  
{  
	CCLOG("ccTouchEnded"); 
}  