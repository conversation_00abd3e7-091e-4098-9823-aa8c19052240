#ifndef __TestSOS__RankData__
#define __TestSOS__RankData__

#include "cocos2d.h"
#include "../SOSConfig.h"

class RankData : public cocos2d::Ref
{
public:
	FUNC_CREATE(RankData);

public:
	struct Info
	{
		std::string uid;
		std::string name;
		int topID;	
		int level;		
		int icon;
		int fight;
	};

	struct Battlefield
	{
		bool isWin;
		std::string uid;
		std::string name;
		int type;
	};

public:
	inline Info& getInfo() { return _info; }
	inline void setInfo(Info info) { _info = info; }

	inline Battlefield& getBattlefield() { return _battlefield; }
	inline void setBattlefield(Battlefield battlefield) { _battlefield = battlefield; }

private:
	Info _info;
	Battlefield _battlefield;
};

#endif