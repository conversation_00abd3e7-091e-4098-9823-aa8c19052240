#include "CopiesRewardView.h"
#include "GameData.h"
#include "../Services/LocalizeService.h"
#include "../Transactions/GovernmentTransaction.h"
#include "../Views/AlertView.h"
#include "../Scenes/BaseScene.h"
#include "../Services/NewguidanceService.h"

USING_NS_CC;
USING_NS_CC_CCS;
USING_NS_CC_UI;

enum TagCopiesRewardView
{
	BOX1_LAYOUT = 70001,
	BOX1_BUTTON = 70002,

	BOX2_BUTTON_BG = 70003,
	BOX2_TEXT_NAME = 70004,
	BOX2_IMAGEVIEW_LEVEL = 70006,
	BOX2_IMAGEVIEW_REPLACE = 70007,
	BOX2_LAYOUT = 70008,
	BOX2_IMAGEVIEW_SELECT = 73000,
};

std::vector<CopiesRewardData::Reward> crv_vec;
std::vector<Widget*> crv_widget;
int crv_copies = 0;
int crv_star = 0;
int crv_count = 0;

CopiesRewardView::CopiesRewardView()
{

}

CopiesRewardView::~CopiesRewardView()
{
	ActionManagerEx::getInstance()->releaseActionByName("UI_Box_1.json");
	crv_vec.clear();
	crv_widget.clear();
	this->getEventDispatcher()->removeCustomEventListeners(EVENT_COPIESREWARD_GET_SUCCESS);

	crv_copies = 0;
	crv_star = 0;
	crv_count = 0;
}

Layer* CopiesRewardView::createScene(int copiesID, int star, std::vector<CopiesRewardData::Reward> vec)
{
	crv_vec = vec;
	crv_copies = copiesID;
	crv_star = star;
	auto layer = CopiesRewardView::create();
	return layer;
}

bool CopiesRewardView::init()
{
	if (!PopupView::init())
	{
		return false;
	}

	layer = CCS_CREATE_LAYER("UI_Box_1");
	layer->setTag(TagBaseScene::COPIES_REWARD_VIEW);
	this->addChild(layer);

	CCS_GET_CHILD(layer, Button, btn, BOX1_BUTTON);
	//btn->setVisible(false);
	//btn->setTouchEnabled(false);
	btn->setPressedActionEnabled(true);
	btn->addTouchEventListener(this, toucheventselector(CopiesRewardView::touchButton));
	CCS_GET_CHILD(layer, Layout, layout, BOX1_LAYOUT);
	auto userData = GameData::getInstance()->getUserData();
	layout->setAnchorPoint(Point(0, 0));
	int diamond = 0;
	auto city = GameData::getInstance()->getCity(userData->getInfo().city);
	for (auto cop : city->getCopies())
	{
		if (cop.second->getInfo().mid == crv_copies)
		{
			auto rewards = cop.second->getCopiesReward();
			for (auto rew : rewards)
			{
				diamond = rew.second->getInfo().diamond;
			}
			break;
		}
	}
	//int a[3] = { 0 };
	//int i, m;
	//for (i = 0; i < crv_vec.size(); ++i)
	//{
	//	while (a[m = CCRANDOM_0_1() * crv_vec.size() - 1]);
	//	a[m] = i;
	//}

	for (int i = 0; i < crv_vec.size(); i++)
	{
		if (crv_vec[i].isGet)
			crv_count++;
	}

	for (int i = 0; i < crv_vec.size(); i++)
	{
		auto ui = CCS_CREATE_LAYER("UI_Box_2");
		CCS_GET_CHILD(ui, Button, btnBG, BOX2_BUTTON_BG);
		btnBG->setPressedActionEnabled(true);
		CCS_GET_CHILD(ui, Text, txtName, BOX2_TEXT_NAME);
		CCS_GET_CHILD(ui, ImageView, imgLevel, BOX2_IMAGEVIEW_LEVEL);
		CCS_GET_CHILD(ui, ImageView, imgReplace, BOX2_IMAGEVIEW_REPLACE);
		CCS_GET_CHILD(ui, Layout, panel, BOX2_LAYOUT);
		CCS_GET_CHILD(ui, ImageView, img, BOX2_IMAGEVIEW_SELECT);

		btnBG->setName(STRING(i));
		crv_widget.push_back(ui);
		if (!crv_vec[i].isGet)
		{
			btnBG->addTouchEventListener(this, toucheventselector(CopiesRewardView::touchButton));
			if (crv_count > 0)
			{
				img->setVisible(false);
				auto dia = ImageView::create("ss_ty_diamond.png", TextureResType::PLIST);
				dia->setPosition(Point(panel->getContentSize().width / 2 + 10, panel->getContentSize().height / 2));
				panel->addChild(dia, panel->getChildrenCount() + 1);
				auto diaTxt = Text::create();
				diaTxt->setFontSize(30);
				diaTxt->setPosition(Point(panel->getContentSize().width / 2 - 20, panel->getContentSize().height / 2));
				diaTxt->setText(STRING(diamond));
				diaTxt->setColor(Color3B(248, 157, 26));
				panel->addChild(diaTxt, panel->getChildrenCount() + 1);
			}
		}
		else
		{

			btn->setVisible(true);
			btn->setTouchEnabled(true);

			img->setVisible(false);
			auto label = LabelTTF::create();
			label->setHorizontalAlignment(cocos2d::TextHAlignment::CENTER);
			label->setFontSize(18);
			label->setPosition(panel->getContentSize().width / 2, panel->getContentSize().height / 2);
			label->setColor(Color3B(225, 223, 177));
			if (crv_vec[i].id == -2)
			{
				txtName->setText(LocalizeService::getInstance()->getString("650"));
				imgReplace->loadTexture("ss_exp.png", TextureResType::PLIST);
				label->setString(STRING(crv_vec[i].value));
				imgLevel->setVisible(false);
			}
			else if (crv_vec[i].id == -1)
			{
				txtName->setText(LocalizeService::getInstance()->getString("648"));
				if (crv_vec[i].value < 2)
					imgReplace->loadTexture("ss_box_diamond1.png", TextureResType::PLIST);
				else if (crv_vec[i].value >= 2 && crv_vec[i].value < 10)
					imgReplace->loadTexture("ss_box_diamond2.png", TextureResType::PLIST);
				else
					imgReplace->loadTexture("ss_box_diamond3.png", TextureResType::PLIST);
				label->setString(STRING(crv_vec[i].value));

				imgLevel->setVisible(false);
			}
			else if (crv_vec[i].id == 0)
			{
				txtName->setText(LocalizeService::getInstance()->getString("649"));
				if (crv_vec[i].value < 5000)
					imgReplace->loadTexture("ss_box_coin1.png", TextureResType::PLIST);
				else if (crv_vec[i].value >= 5000 && crv_vec[i].value<10000)
					imgReplace->loadTexture("ss_box_coin2.png", TextureResType::PLIST);
				else
					imgReplace->loadTexture("ss_box_coin3.png", TextureResType::PLIST);
				label->setString(STRING(crv_vec[i].value));
				imgLevel->setVisible(false);
			}
			else if (crv_vec[i].id > 0)
			{
				auto item = GameData::getInstance()->getItem(crv_vec[i].id);
				imgLevel->loadTexture(GET_PROP_ICON_MASK(item.icon), TextureResType::PLIST);
				imgReplace->loadTexture(GET_PROP_ICON(item.icon), TextureResType::PLIST);
				txtName->setText(LocalizeService::getInstance()->getString(item.name));
				std::string eff = "";
				imgLevel->setVisible(true);
				imgReplace->setVisible(true);
				auto effs = item.effects;
				for (auto a : effs)
				{
					if (a.type != 0)
					{
						if (a.type == 13)
						{
							eff += LocalizeService::getInstance()->getString("100046") + "+" + STRING(a.value) + "\r\n";
						}
						else if (a.type == 14)
						{
							eff += LocalizeService::getInstance()->getString("100047") + "+" + STRING(a.value) + "\r\n";
						}
						else
						{
							eff += LocalizeService::getInstance()->getString(STRING(100035 + a.type)) + "+" + STRING(a.value) + "\r\n";
						}
					}
				}
				label->setString(eff);
				imgLevel->setColor(GetEquipQualityColor(item.quality));
			}
			panel->addChild(label);
		}

	/*	int x = btn->getPosition().x + btn->getContentSize().width / 2;
		if (crv_vec.size() % 2 == 0)
		{
			
		}
		else
		{
			ui->setPositionX(i*ui->getContentSize().width);
		}

		if (this->getContentSize().width == 1067)
			ui->setPositionX(i*ui->getContentSize().width + 50);
		else*/
			ui->setPositionX(i*ui->getContentSize().width);
		layout->addChild(ui);
	}
	//ActionManagerEx::getInstance()->playActionByName("UI_Box_1.json", "Animation1");

	return true;
}

void CopiesRewardView::onEnter()
{
	this->getEventDispatcher()->addCustomEventListener(EVENT_COPIESREWARD_GET_SUCCESS, [=](EventCustom* event) {
		Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_NEWGUIDANCE_START);
		Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_COPIES_CHANGE);
		this->removeFromParent();
	});

	PopupView::onEnter();
}

void CopiesRewardView::touchButton(Ref* obj, cocos2d::ui::TouchEventType eventType)
{
	auto widget = dynamic_cast<Widget*>(obj);
	int tag = widget->getTag();

	switch (eventType)
	{
	case TouchEventType::TOUCH_EVENT_BEGAN:
		break;
	case TouchEventType::TOUCH_EVENT_ENDED:
		if (tag == BOX1_BUTTON)
		{
			GovernmentTransaction::getInstance()->getCopiesReward(crv_copies, crv_star);
			this->addChild(AlertView::create(LocalizeService::getInstance()->getString("632"), AlertType::LOADING), 1000, 99999);
		}
		else if (tag == BOX2_BUTTON_BG)
		{
			auto userData = GameData::getInstance()->getUserData();
			int diamond = 0;
			auto city = GameData::getInstance()->getCity(userData->getInfo().city);
			for (auto cop : city->getCopies())
			{
				if (cop.second->getInfo().mid == crv_copies)
				{
					auto rewards = cop.second->getCopiesReward();
					for (auto rew : rewards)
					{
						diamond = rew.second->getInfo().diamond;
					}
					break;
				}
			}

			if (crv_count > 0)
			{
				if (userData->getInfo().diamond > diamond)
					userData->setDiamond(userData->getInfo().diamond - diamond);
				else
				{
					this->addChild(AlertView::create(LocalizeService::getInstance()->getString("4009"), AlertType::IDLE), 999999);
					return;
				}
			}

			CCS_GET_CHILD(layer, Button, btn, BOX1_BUTTON);
			//btn->setVisible(true);
			//btn->setTouchEnabled(true);
			widget->setTouchEnabled(false);

			auto i = INT(widget->getName());
			CCS_GET_CHILD(layer, Layout, layout, BOX1_LAYOUT);
			for (auto ui : crv_widget)
			{
				CCS_GET_CHILD(ui, Button, btnBG, BOX2_BUTTON_BG);
				CCS_GET_CHILD(ui, Text, txtName, BOX2_TEXT_NAME);
				CCS_GET_CHILD(ui, ImageView, imgLevel, BOX2_IMAGEVIEW_LEVEL);
				CCS_GET_CHILD(ui, ImageView, imgReplace, BOX2_IMAGEVIEW_REPLACE);
				CCS_GET_CHILD(ui, Layout, panel, BOX2_LAYOUT);
				CCS_GET_CHILD(ui, ImageView, img, BOX2_IMAGEVIEW_SELECT);




				if (btnBG->getName() == widget->getName())
				{
					crv_vec[INT(btnBG->getName())].isGet = true;
					img->setVisible(false);
					panel->removeAllChildren();

					this->removeChildByTag(8888);
					auto emitter = ParticleSystemQuad::create("Efficiency/flash2.plist");
					//emitter->setPosition(Point(btnBG->getPositionX() + i*ui->getContentSize().width, btnBG->getPositionY() + 30));
					emitter->setPosition(Point(layout->getPositionX() + ui->getPositionX() + 116, layout->getPositionY() + ui->getPositionY() + 147));
					emitter->setTag(8888);
					this->addChild(emitter);

					auto label = LabelTTF::create();
					label->setHorizontalAlignment(cocos2d::TextHAlignment::CENTER);
					label->setFontSize(18);
					label->setPosition(panel->getContentSize().width / 2, panel->getContentSize().height / 2);
					label->setColor(Color3B(225, 223, 177));
					if (crv_vec[i].id == -2)
					{
						txtName->setText(LocalizeService::getInstance()->getString("650"));
						imgReplace->loadTexture("ss_exp.png", TextureResType::PLIST);
						label->setString(STRING(crv_vec[i].value));
						imgLevel->setVisible(false);
					}
					else if (crv_vec[i].id == -1)
					{
						txtName->setText(LocalizeService::getInstance()->getString("648"));
						if (crv_vec[i].value<2)
							imgReplace->loadTexture("ss_box_diamond1.png", TextureResType::PLIST);
						else if (crv_vec[i].value>2 && crv_vec[i].value < 10)
							imgReplace->loadTexture("ss_box_diamond2.png", TextureResType::PLIST);
						else
							imgReplace->loadTexture("ss_box_diamond3.png", TextureResType::PLIST);
						label->setString(STRING(crv_vec[i].value));

						imgLevel->setVisible(false);
					}
					else if (crv_vec[i].id == 0)
					{
						txtName->setText(LocalizeService::getInstance()->getString("649"));
						if (crv_vec[i].value<5000)
							imgReplace->loadTexture("ss_box_coin1.png", TextureResType::PLIST);
						else if (crv_vec[i].value>5000 && crv_vec[i].value<10000)
							imgReplace->loadTexture("ss_box_coin2.png", TextureResType::PLIST);
						else
							imgReplace->loadTexture("ss_box_coin3.png", TextureResType::PLIST);
						label->setString(STRING(crv_vec[i].value));
						imgLevel->setVisible(false);
					}
					else if (crv_vec[i].id > 0)
					{
						imgLevel->setVisible(true);
						auto item = GameData::getInstance()->getItem(crv_vec[i].id);
						imgLevel->loadTexture(GET_PROP_ICON_MASK(item.icon), TextureResType::PLIST);
						imgReplace->loadTexture(GET_PROP_ICON(item.icon), TextureResType::PLIST);
						txtName->setText(LocalizeService::getInstance()->getString(item.name));
						std::string eff = "";
						imgLevel->setVisible(true);
						imgReplace->setVisible(true);
						auto effs = item.effects;
						for (auto a : effs)
						{
							if (a.type != 0)
							{
								if (a.type == 13)
								{
									eff += LocalizeService::getInstance()->getString("100046") + "+" + STRING(a.value) + "\r\n";
								}
								else if (a.type == 14)
								{
									eff += LocalizeService::getInstance()->getString("100047") + "+" + STRING(a.value) + "\r\n";
								}
								else
								{
									eff += LocalizeService::getInstance()->getString(STRING(100035 + a.type)) + "+" + STRING(a.value) + "\r\n";
								}
							}
						}
						label->setString(eff);
						imgLevel->setColor(GetEquipQualityColor(item.quality));
					}
					panel->addChild(label);
				}
				else if (!crv_vec[INT(btnBG->getName())].isGet)
				{
					panel->removeAllChildren();
					img->setVisible(false);
					auto dia = ImageView::create("ss_ty_diamond.png", TextureResType::PLIST);
					dia->setPosition(Point(panel->getContentSize().width / 2 + 10, panel->getContentSize().height / 2));
					panel->addChild(dia, panel->getChildrenCount() + 1);
					auto diaTxt = Text::create();
					diaTxt->setFontSize(30);
					diaTxt->setPosition(Point(panel->getContentSize().width / 2 - 20, panel->getContentSize().height / 2));
					diaTxt->setText(STRING(diamond));
					diaTxt->setColor(Color3B(248, 157, 26));
					panel->addChild(diaTxt, panel->getChildrenCount() + 1);
				}
			}
			GovernmentTransaction::getInstance()->queryCopiesReward(crv_copies, crv_star, i + 1);
			crv_count++;

			Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_NEWGUIDANCE_START);
		}
		break;
	}
}

