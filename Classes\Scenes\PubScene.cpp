﻿#include "PubScene.h"
#include "CityScene.h"
#include "../Views/RecruitView.h"
#include "../GameData.h"
#include "../Services/LocalizeService.h"
#include "../Services/NewguidanceService.h"
#include "../Views/AlertView.h"

USING_NS_CC;
USING_NS_CC_CCS;
USING_NS_CC_UI;

enum tagPub
{
	UI_PUB_1 = 10004,
	UI_PUB_2 = 10005,
	UI_PUB_3 = 11000,
	UI_BUTTON_CLOSE = 298,	//关闭
	UI_BUTTON_ADD = 841,	//增加钻石
	UI_BUTTON_1 = 843,		//偶遇
	UI_BUTTON_2 = 844,		//巧遇
	UI_BUTTON_3 = 845,		//奇遇
	UI_BUTTON_OPPORTUNITY = 832,	//选项卡-机遇
	UI_BUTTON_STORY = 833,			//剧情
	UI_BUTTON_OLD = 834,			//旧部
	UI_TEXT_MONEY = 837,
	UI_TEXT_DIAMOND = 840,
	UI_SCROLLVIEW = 842,

	OTHER3_IMAGEVIEW_STAR = 1309,
	OTHER3_IMAGEVIEW_PERSON = 1310,
	OTHER3_TEXT_NAME = 1312,
	OTHER3_TEXT_POWER = 1314,
	OTHER3_BUTTON = 1316,

	UI2_TEXT_COIN = 851,
	UI2_TEXT_DIAMOND1 = 854,
	UI2_TEXT_DIAMOND2 = 857,
};

PubScene::PubScene()
{
	_costType = 0;
	_money1 = 0;
	_diamond2 = 0;
	_diamond3 = 0;
}

PubScene::~PubScene()

{
	this->getEventDispatcher()->removeCustomEventListeners(EVENT_RECRUIT_QUERY_SUCCESS);
	this->getEventDispatcher()->removeCustomEventListeners(EVENT_PARTNER_LEAVE_SUCCESS);
	this->getEventDispatcher()->removeCustomEventListeners(EVENT_NEWGUIDANCE_CHANGED);
	this->getEventDispatcher()->removeCustomEventListeners(EVENT_PUBSCENE_CHANGED);
	this->getEventDispatcher()->removeCustomEventListeners(EVENT_RECRUIT_CLOSE);

	_layer2->release();
	
	_costType = 0;
	_money1 = 0;
	_diamond2 = 0;
	_diamond3 = 0;
}

Scene* PubScene::createScene()
{
	// 'scene' is an autorelease object
	auto scene = Scene::create();

	// 'layer' is an autorelease object
	auto layer = PubScene::create();

	// add layer as a child to scene
	scene->addChild(layer);

	// return the scene
	return scene;
}

bool PubScene::init()
{
	if (!BaseScene::init())
	{
		return false;
	}

	node = CCS_CREATE_SCENE("Scene_Recruit");
	node->setTag(TagBaseScene::LAYER);
	addChild(node);

	Texture2D::setDefaultAlphaPixelFormat(Texture2D::PixelFormat::RGB888);
	auto imgBg = Sprite::create("ss_ty_bg1.png");
	auto size = Director::getInstance()->getWinSize();
	imgBg->setScale(size.width / 960, size.height / 640);
	imgBg->setAnchorPoint(Point(0, 0));
	this->addChild(imgBg, -1);
	Texture2D::setDefaultAlphaPixelFormat(Texture2D::PixelFormat::DEFAULT);

	_layer2 = CCS_CREATE_LAYER("UI_Recruit_2");
	_layer2->setTag(UI_PUB_2);
	_layer2->retain();

	auto child = node->getChildByTag(UI_PUB_1);
	auto reader = (ComRender*)child->getComponent("GUIComponent");
	auto layer = (Layer*)reader->getNode();
	auto addBtn = dynamic_cast<Button*>(layer->getChildByTag(UI_BUTTON_ADD));
	addBtn->addTouchEventListener(this, toucheventselector(PubScene::touchButton));
	auto opportunityBtn = dynamic_cast<CheckBox*>(layer->getChildByTag(UI_BUTTON_OPPORTUNITY));
	opportunityBtn->setSelectedState(true);
	opportunityBtn->addTouchEventListener(this, toucheventselector(PubScene::touchButton));
	auto storyBtn = dynamic_cast<CheckBox*>(layer->getChildByTag(UI_BUTTON_STORY));
	storyBtn->addTouchEventListener(this, toucheventselector(PubScene::touchButton));
	auto oldBtn = dynamic_cast<CheckBox*>(layer->getChildByTag(UI_BUTTON_OLD));
	oldBtn->addTouchEventListener(this, toucheventselector(PubScene::touchButton));
	auto userData = GameData::getInstance()->getUserData();
	auto cointext = dynamic_cast<Text *>(layer->getChildByTag(UI_TEXT_MONEY));
	auto diamondtext = dynamic_cast<Text *>(layer->getChildByTag(UI_TEXT_DIAMOND));
	cointext->setText(STRING(userData->getInfo().money));
	diamondtext->setText(STRING(userData->getInfo().diamond));


	auto child1 = node->getChildByTag(UI_PUB_3);
	auto reader1 = (ComRender*)child1->getComponent("GUIComponent");
	auto layer1 = (Layer*)reader1->getNode();
	auto closeBtn = dynamic_cast<Button*>(layer1->getChildByTag(UI_BUTTON_CLOSE));
	closeBtn->addTouchEventListener(this, toucheventselector(PubScene::touchButton));

	auto child2 = node->getChildByTag(UI_PUB_2);
	auto reader2 = (ComRender*)child2->getComponent("GUIComponent");
	auto layer2 = (Layer*)reader2->getNode();
	auto btn1 = dynamic_cast<Button*>(layer2->getChildByTag(UI_BUTTON_1));
	auto btn2 = dynamic_cast<Button*>(layer2->getChildByTag(UI_BUTTON_2));
	auto btn3 = dynamic_cast<Button*>(layer2->getChildByTag(UI_BUTTON_3));
	btn1->setPressedActionEnabled(true);
	btn2->setPressedActionEnabled(true);
	btn3->setPressedActionEnabled(true);
	btn1->addTouchEventListener(this, toucheventselector(PubScene::touchButton));
	btn2->addTouchEventListener(this, toucheventselector(PubScene::touchButton));
	btn3->addTouchEventListener(this, toucheventselector(PubScene::touchButton));

	auto configs = Configuration::getInstance()->getValue(RECRUIT_REFRESH_COST_CONFIG).asValueMap();
	auto &config1 = configs[STRING(1)].asValueMap();
	auto &config2 = configs[STRING(2)].asValueMap();
	auto &config3 = configs[STRING(3)].asValueMap();

	_money1 = config1["CostValue"].asInt();
	_diamond2 = config2["CostValue"].asInt();
	_diamond3 = config3["CostValue"].asInt();

	auto text1 = dynamic_cast<Text*>(layer2->getChildByTag(UI2_TEXT_COIN));
	auto text2 = dynamic_cast<Text*>(layer2->getChildByTag(UI2_TEXT_DIAMOND1));
	auto text3 = dynamic_cast<Text*>(layer2->getChildByTag(UI2_TEXT_DIAMOND2));
	text1->setText(STRING(_money1));
	if (userData->getInfo().money < _money1)
		text1->setColor(Color3B(255, 0, 0));
	text2->setText(STRING(_diamond2));
	if (userData->getInfo().diamond < _diamond2)
		text2->setColor(Color3B(255, 0, 0));
	text3->setText(STRING(_diamond3));
	if (userData->getInfo().diamond < _diamond3)
		text3->setColor(Color3B(255, 0, 0));

	if (userData->getPlayers().size() == 1)
		text3->setText(LocalizeService::getInstance()->getString("664"));
	return true;
}

void PubScene::onEnter()
{
	this->getEventDispatcher()->addCustomEventListener(EVENT_PUBSCENE_CHANGED, [=](EventCustom* event){
		auto userData = GameData::getInstance()->getUserData();
		auto child2 = node->getChildByTag(UI_PUB_2);
		auto reader2 = (ComRender*)child2->getComponent("GUIComponent");
		auto layer2 = (Layer*)reader2->getNode();
		auto text3 = dynamic_cast<Text*>(layer2->getChildByTag(UI2_TEXT_DIAMOND2));

		if (userData->getInfo().diamond < _diamond3)
			text3->setColor(Color3B(255, 0, 0));

		if (userData->getPlayers().size() == 1)
			text3->setText(LocalizeService::getInstance()->getString("664"));
		else
			text3->setText(STRING(_diamond3));


		auto child = node->getChildByTag(UI_PUB_1);
		auto reader = (ComRender*)child->getComponent("GUIComponent");
		auto layer = (Layer*)reader->getNode();
		auto cointext = dynamic_cast<Text *>(layer->getChildByTag(UI_TEXT_MONEY));
		auto diamondtext = dynamic_cast<Text *>(layer->getChildByTag(UI_TEXT_DIAMOND));
		cointext->setText(STRING(userData->getInfo().money));
		diamondtext->setText(STRING(userData->getInfo().diamond));
	});
	this->getEventDispatcher()->addCustomEventListener(EVENT_RECRUIT_CLOSE, [=](EventCustom* event) {
		auto userData = GameData::getInstance()->getUserData();
		auto child = node->getChildByTag(UI_PUB_1);
		auto reader = (ComRender*)child->getComponent("GUIComponent");
		auto layer = (Layer*)reader->getNode();
		auto cointext = dynamic_cast<Text *>(layer->getChildByTag(UI_TEXT_MONEY));
		auto diamondtext = dynamic_cast<Text *>(layer->getChildByTag(UI_TEXT_DIAMOND));
		cointext->setText(STRING(userData->getInfo().money));
		diamondtext->setText(STRING(userData->getInfo().diamond));
	});
	
	this->getEventDispatcher()->addCustomEventListener(EVENT_RECRUIT_QUERY_SUCCESS, [=](EventCustom* event) {
		this->removeChildByTag(99999);
		auto userData = GameData::getInstance()->getUserData();
		if (_costType == 1)
		{
			userData->getInfo().money -= _money1;
		}
		else if (_costType == 2)
		{
			userData->getInfo().diamond -= _diamond2;
		}
		else if (_costType == 3 && userData->getPlayers().size() > 1)
		{
			userData->getInfo().diamond -= _diamond3;
		}

		auto playerData = static_cast<PlayerData*>(event->getUserData());
		auto recruit = RecruitView::createScene(playerData, 1);
		addChild(recruit, this->getChildrenCount() + 1, TagBaseScene::RECRUIT_VIEW);
	});

	this->getEventDispatcher()->addCustomEventListener(EVENT_PARTNER_LEAVE_SUCCESS, [=](EventCustom* event) {
	});

// 	this->getEventDispatcher()->addCustomEventListener(EVENT_NEWGUIDANCE_CHANGED, [=](EventCustom* event) {
// 		if (GameData::getInstance()->isCreate)
// 			this->addChild(NewguidanceService::getInstance()->createLayer(), this->getChildrenCount() + 3);
// 	});
// 	if (GameData::getInstance()->isCreate)
// 		this->addChild(NewguidanceService::getInstance()->createLayer(), this->getChildrenCount() + 3);

// 	if (GameData::getInstance()->getUserData()->getInfo().state == UserData::StateType::GUIDER)
// 	{
// 		NewguidanceService::getInstance()->createLayer(this);
// 	}

	BaseScene::onEnter();
}

void PubScene::menuCloseCallback(Ref* pSender)
{
	Director::getInstance()->end();

#if (CC_TARGET_PLATFORM == CC_PLATFORM_IOS)
	exit(0);
#endif
}

void PubScene::touchButton(Ref* obj, cocos2d::ui::TouchEventType eventType)
{
	auto userData = GameData::getInstance()->getUserData();
	auto button = dynamic_cast<Widget*>(obj);
	int tag = button->getTag();
	auto child = node->getChildByTag(UI_PUB_1);
	auto reader = (ComRender*)child->getComponent("GUIComponent");
	auto layer = (Layer*)reader->getNode();
	auto opportunityBtn = dynamic_cast<CheckBox*>(layer->getChildByTag(UI_BUTTON_OPPORTUNITY));
	auto storyBtn = dynamic_cast<CheckBox*>(layer->getChildByTag(UI_BUTTON_STORY));
	auto oldBtn = dynamic_cast<CheckBox*>(layer->getChildByTag(UI_BUTTON_OLD));
	switch (eventType)
	{
	case TouchEventType::TOUCH_EVENT_BEGAN:
		break;
	case TouchEventType::TOUCH_EVENT_ENDED:
		/*if(this->getChildrenCount()<2)
		{*/
		opportunityBtn->setTouchEnabled(true);
		storyBtn->setTouchEnabled(true);
		oldBtn->setTouchEnabled(true);
		if (tag == UI_BUTTON_CLOSE)
		{
			Director::getInstance()->replaceScene(CityScene::createScene());
		}
		else if (tag == UI_BUTTON_ADD)
		{
			 this->addChild(AlertView::create(LocalizeService::getInstance()->getString("4001"), AlertType::IDLE), 999999);
		}
		else if (tag == UI_BUTTON_OPPORTUNITY)
		{
			opportunityBtn->setTouchEnabled(false);
			storyBtn->setSelectedState(false);
			storyBtn->setTouchEnabled(true);
			oldBtn->setSelectedState(false);
			oldBtn->setTouchEnabled(true);
			LoadView2();
		}
		else if (tag == UI_BUTTON_STORY)
		{
			storyBtn->setTouchEnabled(false);
			opportunityBtn->setSelectedState(false);
			opportunityBtn->setTouchEnabled(true);
			oldBtn->setSelectedState(false);
			oldBtn->setTouchEnabled(true);
			LoadStoryScrollView();
		}
		else if (tag == UI_BUTTON_OLD)
		{
			oldBtn->setTouchEnabled(false);
			opportunityBtn->setSelectedState(false);
			opportunityBtn->setTouchEnabled(true);
			storyBtn->setSelectedState(false);
			storyBtn->setTouchEnabled(true);
			LoadOldScrollView();
		}
		else if (tag == UI_BUTTON_1 || tag == UI_BUTTON_2 || tag == UI_BUTTON_3)
		{

			int playerNum = 0;
			auto players = userData->getPlayers();
			for (auto player : players)
			{
				if (player->getInfo().appoint != 1)
				{
					playerNum++;
				}
			}

			int restNum = 0;
			auto ships = userData->getShips();
			for (auto ship : ships)
			{
				if (ship.second->getInfo().use)
				{
					auto cabins = ship.second->getCabins();
					for (auto cab : cabins)
					{
						if (cab->getInfo().type == CabinData::CabinType::REST_HOUSE)
						{
							restNum = cab->getInfo().subjoin1;
							break;
						}
					}
					break;
				}
			}
			_costType = 0;
			if (restNum > playerNum)
			{
				if (tag == UI_BUTTON_1)
				{
					_costType = 1;
					//if (userData->getInfo().money >= _money1)
					//{
					//	userData->getInfo().money -= _money1;
					//}
					//else
					//{
					//	//MessageBox(LocalizeService::getInstance()->getString("4010").c_str(), "Tips");
					//	this->addChild(AlertView::create(LocalizeService::getInstance()->getString("4010"), AlertType::IDLE), 999999);
					//	return;
					//}

					if (userData->getInfo().money < _money1)
					{
						this->addChild(AlertView::create(LocalizeService::getInstance()->getString("4010"), AlertType::IDLE), 999999);
						return;
					}
				}
				else if (tag == UI_BUTTON_2)
				{
					_costType = 2;
					//if (userData->getInfo().diamond >= _diamond2)
					//{
					//	userData->getInfo().diamond -= _diamond2;
					//}
					//else
					//{
					//	this->addChild(AlertView::create(LocalizeService::getInstance()->getString("4009"), AlertType::IDLE), 999999);
					//	return;
					//}

					if (userData->getInfo().diamond < _diamond2)
					{
						this->addChild(AlertView::create(LocalizeService::getInstance()->getString("4009"), AlertType::IDLE), 999999);
						return;
					}
				}
				else
				{
					_costType = 3;
					//if (userData->getPlayers().size() > 1)
					//{
					//	if (userData->getInfo().diamond >= _diamond3)
					//	{
					//		userData->getInfo().diamond -= _diamond3;
					//	}
					//	else
					//	{
					//		this->addChild(AlertView::create(LocalizeService::getInstance()->getString("4009"), AlertType::IDLE), 999999);
					//		return;
					//	}
					//}
					if (userData->getPlayers().size() > 1)
					{
						if (userData->getInfo().diamond < _diamond3)
						{
							this->addChild(AlertView::create(LocalizeService::getInstance()->getString("4009"), AlertType::IDLE), 999999);
							return;
						}
					}
				}

				auto cointext = dynamic_cast<Text *>(layer->getChildByTag(UI_TEXT_MONEY));
				auto diamondtext = dynamic_cast<Text *>(layer->getChildByTag(UI_TEXT_DIAMOND));
				cointext->setText(STRING(userData->getInfo().money));
				diamondtext->setText(STRING(userData->getInfo().diamond));
				RecruitTransaction::getInstance()->Query(userData->getInfo().uid, userData->getInfo().city, 4, _costType);
				this->addChild(AlertView::create(LocalizeService::getInstance()->getString("632"), AlertType::LOADING), 1000, 99999);
				//this->addChild(LoadingView::create(), 1000, 99999);
			}
			else
			{
				this->addChild(AlertView::create(LocalizeService::getInstance()->getString("4013"), AlertType::IDLE), 999999);
				return;
			}
		}
		else
		{
			if (tag > 100000)
			{
				int count = 0;
				int num = 0;
				for (auto ship : userData->getShips())
				{
					if (ship.second->getInfo().use)
					{
						for (auto cabin : ship.second->getCabins())
						{
							if (cabin->getInfo().type == CabinData::CabinType::REST_HOUSE)
							{
								count = cabin->getInfo().subjoin1;
								break;
							}
						}
						break;
					}
				}

				for (auto player : userData->getPlayers())
				{
					if (player->getInfo().appoint != 1)
					{
						num++;
					}
				}

				if (num < count)
				{
					PartnerTransaction::getInstance()->leavePlayer(userData->getInfo().uid, button->getName(), 1);

					PlayerVector& players = userData->getPlayers();
					for (auto player : players)
					{
						PlayerData::Info& playerInfo = player->getInfo();
						if (button->getName() == playerInfo.id)
						{
							playerInfo.appoint = 0;
							playerInfo.playType = PlayerData::PlayType::PLAYING;
							break;
						}
					}
					LoadOldScrollView();
				}
				else
				{
					//MessageBox(LocalizeService::getInstance()->getString("4013").c_str(), "Tips");
					this->addChild(AlertView::create(LocalizeService::getInstance()->getString("4013"), AlertType::IDLE), 999999);
				}
			}
			else
			{
				auto playerData = dynamic_cast<PlayerData*>(button->getUserObject());
				auto recruit = RecruitView::createScene(playerData, 2);
				this->addChild(recruit, this->getChildrenCount() + 1);
			}
		}
		break;
	}
}

void PubScene::LoadView2()
{
	auto userData = GameData::getInstance()->getUserData();
	auto child = node->getChildByTag(UI_PUB_1);
	auto reader = (ComRender*)child->getComponent("GUIComponent");
	auto layer = (Layer*)reader->getNode();
	auto scrollview = dynamic_cast<ui::ScrollView*>(layer->getChildByTag(UI_SCROLLVIEW));
	scrollview->removeAllChildren();
	auto btn1 = dynamic_cast<Button*>(_layer2->getChildByTag(UI_BUTTON_1));
	auto btn2 = dynamic_cast<Button*>(_layer2->getChildByTag(UI_BUTTON_2));
	auto btn3 = dynamic_cast<Button*>(_layer2->getChildByTag(UI_BUTTON_3));
	btn1->addTouchEventListener(this, toucheventselector(PubScene::touchButton));
	btn2->addTouchEventListener(this, toucheventselector(PubScene::touchButton));
	btn3->addTouchEventListener(this, toucheventselector(PubScene::touchButton));

	auto text1 = dynamic_cast<Text*>(_layer2->getChildByTag(UI2_TEXT_COIN));
	auto text2 = dynamic_cast<Text*>(_layer2->getChildByTag(UI2_TEXT_DIAMOND1));
	auto text3 = dynamic_cast<Text*>(_layer2->getChildByTag(UI2_TEXT_DIAMOND2));
	text1->setText(STRING(_money1));
	if (userData->getInfo().money < _money1)
		text1->setColor(Color3B(255, 0, 0));
	text2->setText(STRING(_diamond2));
	if (userData->getInfo().diamond < _diamond2)
		text2->setColor(Color3B(255, 0, 0));
	text3->setText(STRING(_diamond3));
	if (userData->getInfo().diamond < _diamond3)
		text3->setColor(Color3B(255, 0, 0));

	node->addChild(_layer2, node->getChildrenCount() + 1);

	if (userData->getPlayers().size() == 1)
		text3->setText(LocalizeService::getInstance()->getString("664"));
}

void PubScene::LoadOldScrollView()
{
	node->removeChildByTag(UI_PUB_2);
	auto child = node->getChildByTag(UI_PUB_1);
	auto reader = (ComRender*)child->getComponent("GUIComponent");
	auto layer = (Layer*)reader->getNode();
	auto scrollview = dynamic_cast<ui::ScrollView*>(layer->getChildByTag(UI_SCROLLVIEW));
	scrollview->removeAllChildren();
	auto userData = GameData::getInstance()->getUserData();
	PlayerVector& players = userData->getPlayers();
	int i = 0;
	int width = 0;
	for (auto player : players)
	{
		PlayerData::Info& playerInfo = player->getInfo();
		if (playerInfo.appoint == 1)
		{
			auto other3 = CCS_CREATE_LAYER("UI_Other3_1");
			auto star = dynamic_cast<ImageView*>(other3->getChildByTag(OTHER3_IMAGEVIEW_STAR));
			star->loadTexture(GET_PLAYER_STAR(playerInfo.appraisal), TextureResType::PLIST);
			auto person = dynamic_cast<Button*>(other3->getChildByTag(OTHER3_IMAGEVIEW_PERSON));
			person->loadTextures(String::createWithFormat("ss_head_id%d.png", playerInfo.type)->getCString(), String::createWithFormat("ss_head_id%d.png", playerInfo.type)->getCString(), "", TextureResType::PLIST);
			person->setUserObject(player);
			person->addTouchEventListener(this, toucheventselector(PubScene::touchButton));
			auto name = dynamic_cast<Text*>(other3->getChildByTag(OTHER3_TEXT_NAME));
			name->setText(playerInfo.name);
			auto power = dynamic_cast<Text*>(other3->getChildByTag(OTHER3_TEXT_POWER));
			power->setText(LocalizeService::getInstance()->getString(PLAYER_JOB_NAMES[0]) + STRING(player->getFP()));
			auto btn = dynamic_cast<Button*>(other3->getChildByTag(OTHER3_BUTTON));
			btn->setName(playerInfo.id.c_str());
			btn->setTag(100000 + playerInfo.mid);
			btn->addTouchEventListener(this, toucheventselector(PubScene::touchButton));
			scrollview->addChild(other3);
			i++;
			width = other3->getContentSize().width;
		}
	}
	scrollview->setInnerContainerSize(Size(i*width, scrollview->getContentSize().height));
}



void PubScene::LoadStoryScrollView()
{
	node->removeChildByTag(UI_PUB_2);
	auto child = node->getChildByTag(UI_PUB_1);
	auto reader = (ComRender*)child->getComponent("GUIComponent");
	auto layer = (Layer*)reader->getNode();
	auto scrollview = dynamic_cast<ui::ScrollView*>(layer->getChildByTag(UI_SCROLLVIEW));
	scrollview->removeAllChildren();

}