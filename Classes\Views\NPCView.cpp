﻿#include "NPCView.h"
#include "../Scenes/GovernmentScene.h"
#include "../Scenes/ShopScene.h"
#include "../Scenes/PubScene.h"
#include "../Scenes/WeaponShopScene.h"
#include "../Scenes/DockScene.h"
#include "../MapScene.h"
#include "../Transactions/MissionTransaction.h"
#include "../Transactions/FightTransaction.h"
#include "../Services/LocalizeService.h"
#include "../Services/NewguidanceService.h"
#include "../Scenes/CityScene.h"
#include "AlertView.h"
#include "../Services/EffectService.h"
#include "../Services/SoundService.h"
#include "../Transactions/ShipTransaction.h"
#include "../Transactions/LoginTransaction.h"

USING_NS_CC;
USING_NS_CC_CCS;
USING_NS_CC_UI;

enum tagNpcView
{
	UI_IMAGEVIEW_BG = 52,
	UI_IMAGEVIEW_NPC = 53,
	UI_BUTTON_ENTER = 54,
	UI_TEXT_NPCNAME = 55,
	UI_TEXT_TALK = 56,
	UI_TEXT_BG1 = 57,
	UI_TEXT_MISSION1 = 58,
	UI_TEXT_STATE1 = 59,
	UI_SCROLLVIEW = 60,
	UI_BUTTON_GET = 980,
	UI_ARMATURE_SPEAK = 8563,
	UI_IMAGE_NPCTYPE = 36500,
	UI_IMAGE_OPTION_ICON = 36001,
	UI_TEXT_OPTION_LABEL = 36100,
	// 	UI_BUTTON_LEAVE = 23001,
};

int r_npcType;
std::vector<std::string> r_talkStrs;
int r_talkIndex;
EventListenerCustom* r_listener;
bool r_touched;
TaskData* r_task;
int tag_no_money = 626;

int r_money = 0;

NPCView::NPCView()
{
	r_touched = false;
}

NPCView::~NPCView()
{
	this->getEventDispatcher()->removeCustomEventListeners(EVENT_GAME_PROPS_GET);
	this->getEventDispatcher()->removeCustomEventListeners(EVENT_BUTTON_YES);
	this->getEventDispatcher()->removeEventListener(r_listener);
	r_listener = nullptr;
	r_task = nullptr;
	r_touched = false;
	r_npcType = 0;
	r_talkIndex = 0;
	r_talkStrs.clear();
	r_money = 0;
}

Layer* NPCView::createScene()
{
	return NPCView::createScene(0);
}

Layer* NPCView::createScene(int type)
{
	r_npcType = type;
	auto layer = NPCView::create();
	return layer;
}

bool NPCView::init()
{
	if (!PopupView::init())
	{
		return false;
	}

	_autoClose = false;

	auto userData = GameData::getInstance()->getUserData();
	_ui = CCS_CREATE_LAYER("UI_Mission_1");
	_ui->setTag(TagBaseScene::NPC_VIEW);
	this->addChild(_ui);

	CCS_GET_CHILD(_ui, ImageView, typeImage, UI_IMAGE_NPCTYPE);
	CCS_GET_CHILD(_ui, Button, enterBtn, UI_BUTTON_ENTER);
	enterBtn->setVisible(false);
	enterBtn->setTouchEnabled(false);
	typeImage->loadTexture(String::createWithFormat("ss_speak_title%d.png", r_npcType)->getCString(), TextureResType::PLIST);

	std::string talkStrId = "0";
	if (userData->getInfo().state == UserData::StateType::GUIDER)
	{
		auto novice = userData->getNovices().at(userData->getGuidance().id).at(userData->getGuidance().action);
		talkStrId = novice->getInfo().dialog;
	}

	if (r_npcType > 0 && r_npcType <= NPCType::WHARF&&r_npcType != NPCType::WEAPON)
	{
		enterBtn->addTouchEventListener(this, toucheventselector(NPCView::touchButton));
		enterBtn->loadTextures(String::createWithFormat("ss_speak_%d.png", r_npcType)->getCString(), String::createWithFormat("ss_speak_%d.png", r_npcType)->getCString(), "", TextureResType::PLIST);
		enterBtn->setPressedActionEnabled(true);

		for (int state = 2; state >= 0; state--)
		{
			for (int type = 1; type <= 2; type++)
			{
				loadMissionList(state, type);
			}
		}

		if (talkStrId == "0")
		{
			enterBtn->setVisible(true);
			enterBtn->setTouchEnabled(true);
			talkStrId = NPC_TALK(r_npcType, userData->getInfo().state);
		}
	}
	else if (r_npcType > NPCType::WHARF)
	{
		talkStrId = STRING(r_npcType);
	}

	auto talkStr = LocalizeService::getInstance()->getString(talkStrId);

	r_talkIndex = 0;
	r_talkStrs = split(talkStr, "|");

	if (r_talkStrs.size() > 1)
	{
		CCS_GET_CHILD(_ui, ImageView, bgImage, UI_IMAGEVIEW_BG);
		auto point = bgImage->convertToWorldSpace(Point(580, 180));
		ArmatureDataManager::getInstance()->addArmatureFileInfo("Animation_Speak.ExportJson");
		auto armature = Armature::create("Animation_Speak");
		armature->getAnimation()->play("Animation_Speak");
		armature->setPosition(point);
		this->addChild(armature, this->getChildrenCount() + 1, UI_ARMATURE_SPEAK);
	}

	taskDialog(r_talkStrs);

	auto listener = EventListenerTouchOneByOne::create();
	listener->setSwallowTouches(true);
	listener->onTouchBegan = CC_CALLBACK_2(NPCView::onTouchBegan, this);
	listener->onTouchMoved = CC_CALLBACK_2(NPCView::onTouchMoved, this);
	listener->onTouchEnded = CC_CALLBACK_2(NPCView::onTouchEnded, this);
	this->getEventDispatcher()->addEventListenerWithSceneGraphPriority(listener, _ui);

	return true;
}

void NPCView::menuCloseCallback(Ref* pSender)
{
	Director::getInstance()->end();

#if (CC_TARGET_PLATFORM == CC_PLATFORM_IOS)
	exit(0);
#endif
}

void NPCView::touchButton(Ref* obj, cocos2d::ui::TouchEventType eventType)
{
	auto widget = dynamic_cast<Widget*>(obj);
	int tag = widget->getTag();
	int state = 0;
	auto userData = GameData::getInstance()->getUserData();
	auto roleData = GameData::getInstance()->getPlayer(userData->getInfo().role);
	auto cityData = GameData::getInstance()->getCity(userData->getInfo().city);
	switch (eventType)
	{
	case TouchEventType::TOUCH_EVENT_BEGAN:
		break;
	case TouchEventType::TOUCH_EVENT_ENDED:
		if (tag == UI_BUTTON_ENTER)
		{
			switch (r_npcType)
			{
			case 1:{
					   auto copies = cityData->getCopies();
					   if (copies.size() > 0)
					   {
						   int copiesID = 0;

						   for (auto copis : copies)
						   {

							   if (copis.second->getInfo().level > roleData->getInfo().level)
							   {
								   if (copiesID == 0) continue;
							   }

							   if (copis.second->getInfo().task >= userData->getCurrentTask()->getInfo().mid)
							   {
								   if (copiesID == 0) continue;
							   }

							   if (copiesID == 0 || copiesID > copis.second->getInfo().mid)
								   copiesID = copis.second->getInfo().mid;
						   }

						   for (auto cop : copies)
						   {
							   if (copiesID == 0) break;

							   auto chaps = cop.second->getChapters();
							   for (auto ch : chaps)
							   {
								   if (ch.second->getInfo().mid - 10 * INT(cop.first) == 10 && ch.second->getInfo().evaluate > 0)
								   {
									   if (copiesID < cop.second->getInfo().mid)
									   {
										   copiesID += cop.second->getInfo().mid - copiesID;
									   }
								   }
								   if (ch.second->getInfo().mid - 10 * INT(cop.first) < 10 && ch.second->getInfo().evaluate>0)
								   {
									   if (copiesID < cop.second->getInfo().mid)
										   copiesID = cop.second->getInfo().mid;
								   }
							   }
						   }

						   if (copiesID == 0)
						   {
							   this->addChild(AlertView::create(LocalizeService::getInstance()->getString(copiesID == 0 ? "647" : "635"), AlertType::IDLE), 999999);
						   }
						   else
							   Director::getInstance()->replaceScene(GovernmentScene::createScene(copiesID));
					   }
					   else
					   {
						   //MessageBox(LocalizeService::getInstance()->getString("4002").c_str(), "Tips");
						   this->addChild(AlertView::create(LocalizeService::getInstance()->getString("4002"), AlertType::IDLE), 999999);
					   }
			}
				break;
			case 2:
				Director::getInstance()->replaceScene(ShopScene::createScene());
				break;
			case 3:
				Director::getInstance()->replaceScene(PubScene::createScene());
				break;
			case 4:
				Director::getInstance()->replaceScene(WeaponShopScene::createScene());
				break;
			case 5:
				Director::getInstance()->replaceScene(DockScene::createScene());
				break;
			case 6:{
					   initTargetCity();
					   auto ship = userData->getMyShip();
					   for (auto cabin : ship->getCabins())
					   {
						   if (cabin->getInfo().type == CabinData::CabinType::CAPTAIN_HOUSE&&cabin->getInfo().appoint == "")
						   {
							   this->addChild(AlertView::create(LocalizeService::getInstance()->getString("630"), AlertType::IDLE), 999999);
							   return;
						   }
					   }
					   //auto str = LocalizeService::getInstance()->getString("625");
					   std::string str = "625";
					   AlertView::createAndAdded(STRING(ship->getInfo().recharge), AlertType::DIALOG, &str);
					   //this->addChild(AlertView::create(STRING(ship->getInfo().recharge), AlertType::DIALOG, &str), 999999, TagBaseScene::ALERT);
			}
				break;
			default:
				this->removeFromParent();
				break;
			}
		}
		else if (tag == UI_BUTTON_GET)
		{
			auto taskData = dynamic_cast<TaskData*>(widget->getUserObject());
			auto& taskInfo = taskData->getInfo();
			int submit = 0;
			if (taskInfo.state == 0)
			{
				taskInfo.state = 1;
				submit = 1;
				EffectService::getInstance()->play("Animation_Task", "Animation1");
				SoundService::getInstance()->playSFX("Sounds/fx_mission.mp3");

				Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_TASK_CREATED, NULL);

				MissionTransaction::getInstance()->submit(userData->getInfo().uid, taskInfo.mid, userData->getInfo().city, 1, "");
				for (int i = GameData::MissionType::DIALOG; i <= GameData::MissionType::DEDUCTITEM; i++)
				{
					if (i != GameData::MissionType::APPOINTMENT&&i != GameData::MissionType::FORMATION&&i != GameData::MissionType::RANDOMPLAYER&&i != GameData::MissionType::SELL)
					{
						GameData::getInstance()->isTaskComplete(i, 0, 0);
					}
				}
			}
			else if (taskInfo.state == 1)
			{
				taskInfo.state = 2;
				MissionTransaction::getInstance()->submit(userData->getInfo().uid, taskInfo.mid, userData->getInfo().city, 2, "");
			}
			else if (taskInfo.state == 2)
			{
				taskInfo.state = 3;
				submit = 2;
			}
			if (submit != 0)
			{
				if (taskInfo.state == 3)
				{
					if (taskData->getFactor().type == GameData::MissionType::DEDUCTCARGO)
					{
						for (auto cargo : userData->getCargos())
						{
							if (cargo.mid == taskData->getFactor().id&&cargo.count >= taskData->getFactor().value)
							{
								taskData->getInfo().state = 3;
								cargo.count = taskData->getFactor().value;
								GameData::getInstance()->getUserData()->sellCargo(cargo, true);
								break;
							}
							else
							{
								taskData->getInfo().state = 1;
							}
						}
					}
					else if (taskData->getFactor().type == GameData::MissionType::DEDUCTMONEY)
					{
						if (userData->getInfo().money >= taskData->getFactor().value)
						{
							int money = userData->getInfo().money - taskData->getFactor().value;
							GameData::getInstance()->getUserData()->setMoney(money);
						}
						else
						{
							taskData->getInfo().state = 1;
						}
					}
					else if (taskData->getFactor().type == GameData::MissionType::DEDUCTITEM)
					{
						for (auto& item : userData->getItems())
						{
							if (item.second->getInfo().count >= taskData->getFactor().value && item.second->getInfo().mid == taskData->getFactor().id)
							{
								taskData->getInfo().state = 3;
								item.second->getInfo().count -= taskData->getFactor().value;
								if (item.second->getInfo().count <= 0)
									GameData::getInstance()->getUserData()->rmvItem(item.second->getInfo().id);
								break;
							}
							else
							{
								taskData->getInfo().state = 1;
							}
						}
					}

					EffectService::getInstance()->play("Animation_Task", "Animation2");
					SoundService::getInstance()->playSFX("Sounds/fx_mission.mp3");
					FightTransaction::getInstance()->getRewards(userData->getInfo().uid, 2, STRING(taskInfo.mid));
					this->addChild(AlertView::create(LocalizeService::getInstance()->getString("632"), AlertType::LOADING), 1000, 99999);
				}
			}
			if (taskInfo.state != 3)
			{
				r_npcType = 0;
				r_task = nullptr;
				r_talkIndex = 0;
				r_talkStrs.clear();

				CCS_GET_CHILD(_ui, Layout, layout, UI_SCROLLVIEW);
				layout->removeAllChildren();
				
				Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_NEWGUIDANCE_START);
				Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_NPCVIEW_CHANGED, NULL);
				this->removeFromParent();
			}
		}
		else if (tag == UI_TEXT_BG1)
		{
			if (GameData::getInstance()->getUserData()->getInfo().state == UserData::StateType::GUIDER)
			{
				Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_NEWGUIDANCE_START);
			}

			CCS_GET_CHILD(_ui, Layout, layout, UI_SCROLLVIEW);
			CCS_GET_CHILD(_ui, Button, enterBtn, UI_BUTTON_ENTER);
			enterBtn->setVisible(false);
			enterBtn->setEnabled(false);
			enterBtn->setTouchEnabled(false);

			auto taskData = dynamic_cast<TaskData*>(widget->getUserObject());
			if (taskData != nullptr)
			{
				layout->removeAllChildren();

				std::string taskDialogStr;
				if (taskData->getInfo().state < 2)
				{
					taskDialogStr = LocalizeService::getInstance()->getString(taskData->getInfo().receiveTaskDialog);
				}
				else
				{
					taskDialogStr = LocalizeService::getInstance()->getString(taskData->getInfo().payTaskDialog);
				}

				r_talkIndex = 0;
				r_talkStrs = split(taskDialogStr, "|");
				if (r_talkStrs.size() > 0)
				{
					taskDialog(r_talkStrs);

					if (r_talkStrs.size() > 1)
					{
						CCS_GET_CHILD(_ui, ImageView, bgImage, UI_IMAGEVIEW_BG);
						auto point = bgImage->convertToWorldSpace(Point(580, 180));
						ArmatureDataManager::getInstance()->addArmatureFileInfo("Animation_Speak.ExportJson");
						auto armature = Armature::create("Animation_Speak");
						armature->getAnimation()->play("Animation_Speak");
						armature->setPosition(point);
						this->addChild(armature, this->getChildrenCount() + 1, UI_ARMATURE_SPEAK);
					}
				}

				//当对白只有一句话时直接显示选项
				if (r_talkStrs.size() == 1 && taskData->getInfo().state != 1)
				{
					auto content = LocalizeService::getInstance()->getString(taskData->getInfo().state == 0 ? "100080" : "100081");
					auto layer = createOption(content, "", getMissionState(taskData->getInfo().state), UI_BUTTON_GET, taskData);
					layout->addChild(layer);
				}

				r_task = taskData;
			}
		}
		break;
	}
}

void NPCView::onEnter()
{
	r_listener = EventListenerCustom::create(EVENT_BUTTON_YES, [=](EventCustom* event){
		auto tag = static_cast<int*>(event->getUserData());

		if (tag != nullptr && *tag == 626)
		{
			return;
		}

		if (r_npcType == NPCType::WHARF)
		{
			auto userData = GameData::getInstance()->getUserData();
			auto ship = userData->getMyShip();
			for (auto cabin : ship->getCabins())
			{
				if (cabin->getInfo().type == CabinData::CabinType::CAPTAIN_HOUSE&&cabin->getInfo().appoint == "")
				{
					return;
				}
			}

			if (userData->getInfo().money >= ship->getInfo().recharge)
			{
				userData->setMoney(userData->getInfo().money - ship->getInfo().recharge);
				ShipTransaction::getInstance()->leaveCity(userData->getInfo().uid);
// 				UserDefault::getInstance()->setBoolForKey("InCity", false);
// 				UserDefault::getInstance()->setIntegerForKey("GuideSave", userData->getGuidance().id);
				GameData::getInstance()->getUserData()->getInfo().inCity = false;
// 				CityScene::getCloudWidget()->setVisible(true);
// 				ActionManagerEx::getInstance()->playActionByName("UI_LoadingCloud_1.json", "Animation1", CallFunc::create([](){
// 					ArmatureDataManager::getInstance()->removeArmatureFilesInfo();
// // 					SpriteFrameCache::getInstance()->removeSpriteFrames();
// // 					Director::getInstance()->getTextureCache()->removeAllTextures();
// 					// 					SpriteFrameCache::getInstance()->removeUnusedSpriteFrames();
// 					// 					Director::getInstance()->getTextureCache()->removeUnusedTextures();
// 					Director::getInstance()->replaceScene(MapScene::create());
// 				}));
				LoginTransaction::getInstance()->updateInfo();
				Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_CITY_SCENE_CLOUD_CLOSE);
			}
			else
			{
				this->addChild(AlertView::create(LocalizeService::getInstance()->getString("626"), AlertType::IDLE, &tag_no_money), 999999);
			}
		}
	});

	this->getEventDispatcher()->addCustomEventListener(EVENT_GAME_PROPS_GET, [=](EventCustom* event) {
		this->removeChildByTag(99999);

		r_npcType = 0;
		r_task = nullptr;
		r_talkIndex = 0;
		r_talkStrs.clear();

		CCS_GET_CHILD(_ui, Layout, layout, UI_SCROLLVIEW);
		layout->removeAllChildren();

		Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_NEWGUIDANCE_START);
		Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_NPCVIEW_CHANGED, NULL);
		this->removeFromParent();
	});

	this->getEventDispatcher()->addEventListenerWithFixedPriority(r_listener, 1);

	PopupView::onEnter();
}

bool NPCView::onTouchBegan(Touch* touch, Event* event)
{
	CCLOG("onTouchBegan");
	CCS_GET_CHILD(_ui, ImageView, bgImg, UI_IMAGEVIEW_BG);
	Point touchLocation = touch->getLocation();
	Rect r = Rect(bgImg->getPositionX(), bgImg->getPositionY(), bgImg->getContentSize().width, bgImg->getContentSize().height);
	if (!r.containsPoint(touchLocation))
	{
		CCLOG("x:%f,y:%f", bgImg->getPositionX(), bgImg->getPositionY());
		r_touched = true;
	}
	return true;
}

void NPCView::onTouchMoved(Touch* touch, Event* event)
{
	CCLOG("onTouchMoved");
}

void NPCView::onTouchEnded(Touch* touch, Event* event)
{
	CCLOG("onTouchEnded");

	if (r_touched)
	{
		Director::getInstance()->getEventDispatcher()->dispatchCustomEvent("event_button_touch_ended", NULL);
		Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_NPCVIEW_CHANGED, NULL);
		this->removeFromParent();
	}

	if (r_talkStrs.size() > r_talkIndex)
	{
		taskDialog(r_talkStrs);

		Director::getInstance()->getEventDispatcher()->dispatchCustomEvent("event_button_touch_ended", NULL);

		if (r_talkIndex >= r_talkStrs.size() || r_talkStrs.size() == 1)
		{
			CCS_GET_CHILD(_ui, Layout, layout, UI_SCROLLVIEW);
			layout->removeAllChildren();

			this->removeChildByTag(UI_ARMATURE_SPEAK);

			if (r_task == nullptr)
			{
				CCS_GET_CHILD(_ui, Button, enterBtn, UI_BUTTON_ENTER);
				enterBtn->setVisible(true);
				enterBtn->setTouchEnabled(true);
				return;
			}

			auto content = LocalizeService::getInstance()->getString(r_task->getInfo().state == 0 ? "100080" : "100081");

			auto layer = createOption(content, "", getMissionState(r_task->getInfo().state), UI_BUTTON_GET, r_task);
			layout->addChild(layer);


			r_task = nullptr;
			r_talkIndex = 0;
			r_talkStrs.clear();
		}
	}
}

void NPCView::loadMissionList(int state, int type)
{
	auto userData = GameData::getInstance()->getUserData();

	CCS_GET_CHILD(_ui, Layout, layout, UI_SCROLLVIEW);
	auto tasks = userData->getTasks();
	for (auto& task : tasks)
	{
		if (task.second->getInfo().type == type&&task.second->getInfo().state == state)
		{
			if (task.second->getInfo().state == 0 && task.second->getInfo().city == userData->getInfo().city || task.second->getInfo().state < 3 && task.second->getInfo().state > 0 && task.second->getInfo().deliveryCityID == userData->getInfo().city)
			{
				if (task.second->getInfo().state == 0 && task.second->getInfo().receivenpc == r_npcType || task.second->getInfo().state == 1 && task.second->getInfo().paynpc == r_npcType || task.second->getInfo().state == 2 && task.second->getInfo().paynpc == r_npcType)
				{

					if (task.second->getInfo().state == 1 && task.second->getFactor().type == 1 && task.second->getFactor().id == userData->getInfo().city && task.second->getInfo().paynpc == r_npcType)
						task.second->getInfo().state = 2;

					auto content = LocalizeService::getInstance()->getString(TASK_TYPE[task.second->getInfo().type].c_str()) + LocalizeService::getInstance()->getString(task.second->getInfo().name);
					auto state = LocalizeService::getInstance()->getString(MISSION_STATE[task.second->getInfo().state]);
					auto color = getMissionState(task.second->getInfo().state);

					auto layer = createOption(content, state, getMissionState(task.second->getInfo().state), UI_TEXT_BG1, task.second);
					layout->addChild(layer);

				}
			}
		}
	}
}

void NPCView::taskDialog(std::vector<std::string> list)
{
	CCS_GET_CHILD(_ui, Text, talkText, UI_TEXT_TALK);
	CCS_GET_CHILD(_ui, ImageView, npcImg, UI_IMAGEVIEW_NPC);
	CCS_GET_CHILD(_ui, Text, npcText, UI_TEXT_NPCNAME);

	auto talks = split(list[r_talkIndex], "^");
	auto talkNpc = talks[0];
	auto talkDialog = talks[1];

	if (talkNpc == "0")
	{
		auto role = GameData::getInstance()->getPlayer(GameData::getInstance()->getUserData()->getInfo().role);
		npcImg->loadTexture(GET_PLAYER_IMAGE(role->getInfo().mid), TextureResType::PLIST);
		npcText->setText(role->getInfo().name + LocalizeService::getInstance()->getString("628"));
	}
	else
	{
		npcImg->loadTexture(GET_NPC_IMAGE(talkNpc.c_str()), TextureResType::PLIST);
		npcText->setText(LocalizeService::getInstance()->getString(NPC_NAME(talkNpc)) + LocalizeService::getInstance()->getString("628"));
	}

	talkText->setText(talkDialog);

	r_talkIndex++;
}

void NPCView::initTargetCity()
{
	if (GameData::getInstance()->getUserData()->getInfo().state == UserData::StateType::GUIDER)
	{
		auto user = GameData::getInstance()->getUserData();
		auto guidances = user->getNovices();
		auto guidance = guidances[user->getGuidance().id][user->getGuidance().action];

		user->getInfo().targetCity = guidance->getInfo().value;
		LoginTransaction::getInstance()->updateInfo();
		//UserDefault::getInstance()->setIntegerForKey("TargetCity", user->getInfo().targetCity);
	}
}

Node* NPCView::createOption(std::string content, std::string state, Color3B color, int tag, Ref* data)
{
	auto layer = CCS_CREATE_LAYER("UI_Mission_4");
	CCS_GET_CHILD(layer, Button, bgImg, UI_TEXT_BG1);
	CCS_GET_CHILD(layer, ImageView, iconImage, UI_IMAGE_OPTION_ICON);
	CCS_GET_CHILD(layer, TextBMFont, labelText, UI_TEXT_OPTION_LABEL);
	CCS_GET_CHILD(layer, Text, contentTxt, UI_TEXT_MISSION1);
	CCS_GET_CHILD(layer, Text, stateTxt, UI_TEXT_STATE1);

	bgImg->setUserObject(data);
	bgImg->setTag(tag);
	bgImg->setPressedActionEnabled(true);
	bgImg->addTouchEventListener(this, toucheventselector(NPCView::touchButton));

	if (tag == UI_BUTTON_GET)
	{
		//contentTxt->setTextHorizontalAlignment(TextHAlignment::CENTER);
		labelText->setText(content);
		iconImage->setVisible(false);
		bgImg->loadTextures("ss_taskbg4.png", "ss_taskbg4.png", "", TextureResType::PLIST);
	}
	else if (tag == UI_TEXT_BG1)
	{
		contentTxt->setText(content);
		contentTxt->setColor(color);
		stateTxt->setText(state);
		stateTxt->setColor(color);

		TaskData* taskData = dynamic_cast<TaskData*>(data);
		switch (taskData->getInfo().state)
		{
		case TaskData::StateType::UNCLAIMED:
			bgImg->loadTextures("ss_taskbg2.png", "ss_taskbg2.png", "", TextureResType::PLIST);
			iconImage->loadTexture("ss_taskicon1.png", TextureResType::PLIST);
			break;
		case TaskData::StateType::ACCEPTED:
			bgImg->setTouchEnabled(false);
			bgImg->loadTextures("ss_taskbg3.png", "ss_taskbg3.png", "", TextureResType::PLIST);
			iconImage->loadTexture("ss_taskicon2.png", TextureResType::PLIST);
			break;
		case TaskData::StateType::FINISHED:
			bgImg->loadTextures("ss_taskbg.png", "ss_taskbg.png", "", TextureResType::PLIST);
			iconImage->loadTexture("rss_myship_select.png", TextureResType::PLIST);
			break;
		default:
			break;
		}
	}

	return layer;
}