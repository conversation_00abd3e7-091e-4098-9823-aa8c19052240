#include "ArenaTransaction.h"
#include "../Services/HTTPService.h"
#include "../GameData.h"

USING_NS_CC;
USING_STD;

static const char* TRANS_GAME_GETRANK = "Game_Get_RankList.ashx";


FUNC_GET_INSTANCE(ArenaTransaction);

ArenaTransaction::ArenaTransaction()
{
	Director::getInstance()->getEventDispatcher()->addCustomEventListener(TRANS_GAME_GETRANK, [=](EventCustom* event) {
		auto userData = GameData::getInstance()->getUserData();
		auto& ranks = userData->getRanks();
		ranks.clear();
		auto data = static_cast<HTTPData *>(event->getUserData());
		GameData::getInstance()->getUserData()->getInfo().rankId = data->readInt("rankID");
		auto friList = data->readDatas("friList");
		auto jjcList = data->readDatas("jjcList");
		for (auto i = 0; i < jjcList.size(); i++)
		{
			RankData* rankData = RankData::create();
			auto element1 = jjcList[i];
			RankData::Info info;
			info.uid = element1->readString("UserID");
			info.fight = element1->readInt("Fight");
			info.icon = element1->readInt("Icon");
			info.level = element1->readInt("Level");
			info.name = element1->readString("UserName");
			info.topID = element1->readInt("TopID");
			rankData->setInfo(info);


			if (i < friList.size())
			{
				auto element = friList[i];
				RankData::Battlefield battlefield;
				battlefield.isWin = element->readBool("IsWin");
				battlefield.name = element->readString("TargetName");
				battlefield.uid = element->readString("TargetID");
				battlefield.type = element->readInt("Type");
				rankData->setBattlefield(battlefield);
			}
			else
			{
				RankData::Battlefield battlefield;
				battlefield.isWin = false;
				battlefield.name = "";
				battlefield.uid = "";
				battlefield.type = 0;
				rankData->setBattlefield(battlefield);
			}
			ranks.pushBack(rankData);
		}
		int type = data->readInt("Type");
		Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(EVENT_GAME_RANKGET_SUCCESS, &type);
	});
}

ArenaTransaction::~ArenaTransaction()
{
	Director::getInstance()->getEventDispatcher()->removeCustomEventListeners(TRANS_GAME_GETRANK);
}

void ArenaTransaction::rankGet(std::string uid, int type)
{
	auto data = HTTPData::create();
	data->write("UserID", uid.c_str());
	data->write("Type", type);
	auto url = WEB_HOST + TRANS_GAME_GETRANK;

	HTTPService::getInstance()->request(url.c_str(), data, TRANS_GAME_GETRANK);
}
