#include "WarehouseView.h"
#include "../Services/LocalizeService.h"

USING_NS_CC;
USING_NS_CC_CCS;
USING_NS_CC_UI;

enum tagWarehouseView
{
	UI_TEXT_WEIGHT = 947,
	UI_TEXT_MONEY = 950,
	UI_PAGEVIEW = 961,
	UI_BUTTON_CLOSE = 951,

	UI3_SCROLLITEM_BUTTON_BG = 812,
	UI3_SCROLLITEM_TEXT_NAME = 814,
	UI3_SCROLLITEM_TEXT_PRICE = 764,
	UI3_SCROLLITEM_IMAGE_ICON = 813,
	UI3_SCROLLITEM_TEXT_AMOUNT = 815,
};

WarehouseView::WarehouseView()
{

}

WarehouseView::~WarehouseView()
{

}

Layer* WarehouseView::createScene()
{
	auto layer = WarehouseView::create();
	return layer;
}

bool WarehouseView::init()
{
	if (!PopupView::init())
	{
		return false;
	}
	_autoClose = false;
	_warehouse = CCS_CREATE_LAYER("UI_ship_2");
	addChild(_warehouse);

	CCS_GET_CHILD(_warehouse, Button, closeBtn, UI_BUTTON_CLOSE);
	closeBtn->addTouchEventListener(this, toucheventselector(WarehouseView::touchButton));
	auto userData = GameData::getInstance()->getUserData();
	CCS_GET_CHILD(_warehouse, PageView, pageview, UI_PAGEVIEW);

	int row = 1;
	int num = 0;
	int maxWeight = 0;
	int weight = 0;
	int money = 0;

	Layout * layout;
	for (auto cargo : userData->getCargos())
	{
		if (num % 6 == 0)
		{
			layout = Layout::create();
			pageview->addPage(layout);
			row = 1;
		}

		auto widght = CCS_CREATE_LAYER("UI_Shop_3");
		if (num % 2 != 0)
		{
			widght->setPosition(Point(pageview->getContentSize().width - widght->getContentSize().width - 30, pageview->getContentSize().height - row * widght->getContentSize().height - 10));
			row++;
		}
		else
			widght->setPosition(Point(0, pageview->getContentSize().height - row * widght->getContentSize().height - 10));
		layout->addChild(widght);
		num++;


		auto city = GameData::getInstance()->getCity(userData->getInfo().city);
		auto& shopCargos = city->getCargos();

// 		for (auto &cargoInShop : shopCargos)
// 		{
// 			if (cargo.mid == cargoInShop.mid)
// 			{
// 				cargo.sell = cargoInShop.sell;	
// 				break;
// 			}
// 		}

		CCS_GET_CHILD(widght, Text, priceLabel, UI3_SCROLLITEM_TEXT_PRICE);
		CCS_GET_CHILD(widght, ImageView, iconImage, UI3_SCROLLITEM_IMAGE_ICON);
		CCS_GET_CHILD(widght, Button, bgButton, UI3_SCROLLITEM_BUTTON_BG);
		CCS_GET_CHILD(widght, Text, amountLabel, UI3_SCROLLITEM_TEXT_AMOUNT);
		CCS_GET_CHILD(widght, Text, nameLabel, UI3_SCROLLITEM_TEXT_NAME);

		nameLabel->setText(LocalizeService::getInstance()->getString(cargo.name));
		amountLabel->setText(STRING(cargo.count) + "X" + STRING(cargo.space));
		priceLabel->setText(STRING(cargo.price));
		iconImage->loadTexture(GET_CARGO_ICON(cargo.mid), TextureResType::PLIST);
		iconImage->setVisible(true);

		weight += cargo.space*cargo.count;
		money += cargo.sell*cargo.count;
	}



	pageview->addEventListenerPageView(this, pagevieweventselector(WarehouseView::pageViewEvent));
	for (auto ship : userData->getShips())
	{
		if (ship.second->getInfo().use)
		{
			for (auto cabin : ship.second->getCabins())
			{
				if (cabin->getInfo().type == CabinData::CabinType::WARE_HOUSE)
				{
					maxWeight += cabin->getInfo().subjoin1;
				}
			}
			break;
		}
	}
	CCS_GET_CHILD(_warehouse, Text, weightTxt, UI_TEXT_WEIGHT);
	CCS_GET_CHILD(_warehouse, Text, moneyTxt, UI_TEXT_MONEY);
	weightTxt->setText(STRING(weight) + "/" + STRING(maxWeight));
	moneyTxt->setText(STRING(money));
	return true;
}

void WarehouseView::menuCloseCallback(Ref* pSender)
{
	Director::getInstance()->end();

#if (CC_TARGET_PLATFORM == CC_PLATFORM_IOS)
	exit(0);
#endif
}

void WarehouseView::onEnter()
{
	Layer::onEnter();
}

void WarehouseView::touchButton(Ref* obj, cocos2d::ui::TouchEventType eventType)
{
	auto userData = GameData::getInstance()->getUserData();
	auto widget = dynamic_cast<Widget*>(obj);
	int tag = widget->getTag();
	switch (eventType)
	{
	case TouchEventType::TOUCH_EVENT_BEGAN:
		break;
	case TouchEventType::TOUCH_EVENT_ENDED:

		if (tag == UI_BUTTON_CLOSE)
		{
			this->removeFromParent();
		}
		break;
	}
}

void WarehouseView::pageViewEvent(Ref *pSender, PageViewEventType type)
{
	switch (type)
	{
	case PAGEVIEW_EVENT_TURNING:
	{
								   PageView* pageView = dynamic_cast<PageView*>(pSender);
								   pageView->getCurPageIndex() + 1;

	}

		break;
	default:
		break;
	}
}