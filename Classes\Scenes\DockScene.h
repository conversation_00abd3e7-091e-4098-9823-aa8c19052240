﻿#ifndef __DOCK_SCENE_H__
#define __DOCK_SCENE_H__

#include "cocos2d.h"
#include "ui/CocosGUI.h"
#include "cocostudio/CocoStudio.h"
#include "../SOSConfig.h"
#include "BaseScene.h"

class DockScene : public BaseScene
{
public:
	CREATE_FUNC(DockScene);
	static cocos2d::Scene* createScene();
	
	DockScene();
	virtual ~DockScene();
	virtual bool init() override;
	virtual void onEnter() override;
	
	void touchButton(cocos2d::Ref* object, cocos2d::ui::TouchEventType type);
	//void touchCheckBox(cocos2d::Ref* pSender, cocos2d::ui::CheckBoxEventType type);

	void initShips();
	void createShipInfo(cocos2d::ui::Widget* widget);
	cocos2d::ui::Widget* createShipAttri(int content, int tag, int value);
    
private:
	bool _status;
	cocos2d::Node* node;
    
};

#endif // __DOCK_SCENE_H__